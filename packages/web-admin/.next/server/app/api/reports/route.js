"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/reports/route";
exports.ids = ["app/api/reports/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _workspaces_finalagent_main_final_packages_web_admin_src_app_api_reports_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/reports/route.js */ \"(rsc)/./src/app/api/reports/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/reports/route\",\n        pathname: \"/api/reports\",\n        filename: \"route\",\n        bundlePath: \"app/api/reports/route\"\n    },\n    resolvedPagePath: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/api/reports/route.js\",\n    nextConfigOutput,\n    userland: _workspaces_finalagent_main_final_packages_web_admin_src_app_api_reports_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/reports/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/reports/route.js":
/*!**************************************!*\
  !*** ./src/app/api/reports/route.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Global Prisma instance for serverless optimization\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient({\n    datasources: {\n        db: {\n            url: process.env.DATABASE_URL\n        }\n    },\n    log:  true ? [\n        \"error\"\n    ] : 0\n});\n// Prevent multiple instances in development\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const type = searchParams.get(\"type\");\n        const status = searchParams.get(\"status\");\n        const priority = searchParams.get(\"priority\");\n        const agentId = searchParams.get(\"agentId\");\n        const siteId = searchParams.get(\"siteId\");\n        const startDate = searchParams.get(\"startDate\");\n        const endDate = searchParams.get(\"endDate\");\n        let reportsData;\n        try {\n            // Build where clause for filtering\n            const where = {\n                ...type && {\n                    type\n                },\n                ...status && {\n                    status\n                },\n                ...priority && {\n                    priority\n                },\n                ...agentId && {\n                    agentId\n                },\n                ...siteId && {\n                    siteId\n                },\n                ...startDate && endDate && {\n                    createdAt: {\n                        gte: new Date(startDate),\n                        lte: new Date(endDate)\n                    }\n                }\n            };\n            // Fetch reports with related data\n            const reports = await prisma.report.findMany({\n                where,\n                include: {\n                    agent: {\n                        include: {\n                            user: true\n                        }\n                    },\n                    site: true,\n                    shift: {\n                        include: {\n                            site: true\n                        }\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            });\n            // Transform data for frontend\n            const transformedReports = reports.map((report)=>({\n                    id: report.id,\n                    title: report.title,\n                    description: report.description,\n                    type: report.type,\n                    priority: report.priority,\n                    status: report.status,\n                    agentId: report.agentId,\n                    agentName: report.agent ? `${report.agent.user.firstName} ${report.agent.user.lastName}` : \"Unknown\",\n                    agentEmployeeId: report.agent?.employeeId,\n                    siteId: report.siteId,\n                    siteName: report.site?.name || \"Unknown Site\",\n                    siteAddress: report.site?.address,\n                    shiftId: report.shiftId,\n                    location: report.location,\n                    photos: report.photos || [],\n                    videos: report.videos || [],\n                    documents: report.documents || [],\n                    rating: report.rating,\n                    feedback: report.feedback,\n                    reviewedBy: report.reviewedBy,\n                    reviewedAt: report.reviewedAt,\n                    reviewNotes: report.reviewNotes,\n                    createdAt: report.createdAt,\n                    updatedAt: report.updatedAt\n                }));\n            reportsData = {\n                reports: transformedReports,\n                total: transformedReports.length,\n                lastUpdated: new Date().toISOString()\n            };\n        } catch (dbError) {\n            console.error(\"Database query error:\", dbError);\n            // Return sample data for demonstration when database is not available\n            reportsData = {\n                reports: [\n                    {\n                        id: \"1\",\n                        title: \"Suspicious Activity Report\",\n                        description: \"Observed individual attempting to access restricted area without proper authorization. Subject was approached and asked to leave the premises.\",\n                        type: \"INCIDENT\",\n                        priority: \"HIGH\",\n                        status: \"SUBMITTED\",\n                        agentId: \"3\",\n                        agentName: \"Moussa Diop\",\n                        agentEmployeeId: \"BH003\",\n                        siteId: \"3\",\n                        siteName: \"Orange Senegal\",\n                        siteAddress: \"Rue de Thiong, Dakar\",\n                        shiftId: \"1\",\n                        location: {\n                            latitude: 14.6928,\n                            longitude: -17.4467,\n                            address: \"Orange Senegal Main Entrance\"\n                        },\n                        photos: [\n                            \"https://example.com/photos/incident_001.jpg\"\n                        ],\n                        videos: [],\n                        documents: [],\n                        rating: null,\n                        feedback: null,\n                        reviewedBy: null,\n                        reviewedAt: null,\n                        reviewNotes: null,\n                        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()\n                    },\n                    {\n                        id: \"2\",\n                        title: \"Routine Patrol Report\",\n                        description: \"Completed scheduled patrol of all designated areas. No incidents or anomalies observed. All security systems functioning normally.\",\n                        type: \"PATROL\",\n                        priority: \"NORMAL\",\n                        status: \"APPROVED\",\n                        agentId: \"1\",\n                        agentName: \"Amadou Ba\",\n                        agentEmployeeId: \"BH001\",\n                        siteId: \"1\",\n                        siteName: \"Sonatel HQ\",\n                        siteAddress: \"Plateau, Dakar, Senegal\",\n                        shiftId: \"2\",\n                        location: {\n                            latitude: 14.6937,\n                            longitude: -17.4441,\n                            address: \"Sonatel HQ Perimeter\"\n                        },\n                        photos: [\n                            \"https://example.com/photos/patrol_001.jpg\",\n                            \"https://example.com/photos/patrol_002.jpg\"\n                        ],\n                        videos: [],\n                        documents: [],\n                        rating: 5,\n                        feedback: \"Excellent patrol coverage and attention to detail.\",\n                        reviewedBy: \"<EMAIL>\",\n                        reviewedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n                        reviewNotes: \"Thorough documentation and professional conduct.\",\n                        createdAt: new Date(Date.now() - 18 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()\n                    },\n                    {\n                        id: \"3\",\n                        title: \"Equipment Malfunction Report\",\n                        description: \"CCTV camera #3 in the parking area appears to be malfunctioning. Image quality is poor and camera is not responding to remote controls.\",\n                        type: \"MAINTENANCE\",\n                        priority: \"MEDIUM\",\n                        status: \"IN_REVIEW\",\n                        agentId: \"2\",\n                        agentName: \"Fatou Sow\",\n                        agentEmployeeId: \"BH002\",\n                        siteId: \"2\",\n                        siteName: \"CBAO Bank Dakar\",\n                        siteAddress: \"Avenue L\\xe9opold S\\xe9dar Senghor, Dakar\",\n                        shiftId: \"3\",\n                        location: {\n                            latitude: 14.7167,\n                            longitude: -17.4677,\n                            address: \"CBAO Bank Parking Area\"\n                        },\n                        photos: [\n                            \"https://example.com/photos/camera_malfunction.jpg\"\n                        ],\n                        videos: [],\n                        documents: [\n                            \"https://example.com/docs/maintenance_request.pdf\"\n                        ],\n                        rating: null,\n                        feedback: null,\n                        reviewedBy: null,\n                        reviewedAt: null,\n                        reviewNotes: null,\n                        createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()\n                    },\n                    {\n                        id: \"4\",\n                        title: \"Emergency Response Report\",\n                        description: \"Responded to fire alarm activation in Building B. Conducted evacuation procedures and coordinated with fire department. False alarm caused by kitchen smoke.\",\n                        type: \"EMERGENCY\",\n                        priority: \"CRITICAL\",\n                        status: \"APPROVED\",\n                        agentId: \"1\",\n                        agentName: \"Amadou Ba\",\n                        agentEmployeeId: \"BH001\",\n                        siteId: \"4\",\n                        siteName: \"Ecobank Senegal\",\n                        siteAddress: \"Place de l'Ind\\xe9pendance, Dakar\",\n                        shiftId: \"4\",\n                        location: {\n                            latitude: 14.6892,\n                            longitude: -17.4419,\n                            address: \"Ecobank Building B\"\n                        },\n                        photos: [\n                            \"https://example.com/photos/emergency_response.jpg\"\n                        ],\n                        videos: [\n                            \"https://example.com/videos/evacuation_procedure.mp4\"\n                        ],\n                        documents: [\n                            \"https://example.com/docs/fire_dept_report.pdf\",\n                            \"https://example.com/docs/incident_timeline.pdf\"\n                        ],\n                        rating: 5,\n                        feedback: \"Excellent emergency response and coordination.\",\n                        reviewedBy: \"<EMAIL>\",\n                        reviewedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n                        reviewNotes: \"Proper protocols followed. Quick response time.\",\n                        createdAt: new Date(Date.now() - 30 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()\n                    }\n                ],\n                total: 4,\n                lastUpdated: new Date().toISOString(),\n                note: \"Sample data - Database connection pending\"\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(reportsData);\n    } catch (error) {\n        console.error(\"Reports API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch reports data\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/reports/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Freports%2Froute&page=%2Fapi%2Freports%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Freports%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();