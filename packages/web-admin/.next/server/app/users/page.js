/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/users/page";
exports.ids = ["app/users/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1c3a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'users',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/users/page.js */ \"(rsc)/./src/app/users/page.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/users/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/users/page\",\n        pathname: \"/users\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGbm9kZV9tb2R1bGVzJTJGJTQwY2xlcmslMkZuZXh0anMlMkZkaXN0JTJGZXNtJTJGYXBwLXJvdXRlciUyRmNsaWVudCUyRkNsZXJrUHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDbGllbnRDbGVya1Byb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRndvcmtzcGFjZXMlMkZmaW5hbGFnZW50LW1haW4tZmluYWwlMkZub2RlX21vZHVsZXMlMkYlNDBjbGVyayUyRm5leHRqcyUyRmRpc3QlMkZlc20lMkZjbGllbnQtYm91bmRhcnklMkZjb250cm9sQ29tcG9uZW50cy5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkF1dGhlbnRpY2F0ZVdpdGhSZWRpcmVjdENhbGxiYWNrJTIyJTJDJTIyQ2xlcmtMb2FkZWQlMjIlMkMlMjJDbGVya0xvYWRpbmclMjIlMkMlMjJNdWx0aXNlc3Npb25BcHBTdXBwb3J0JTIyJTJDJTIyUmVkaXJlY3RUb0NyZWF0ZU9yZ2FuaXphdGlvbiUyMiUyQyUyMlJlZGlyZWN0VG9Pcmdhbml6YXRpb25Qcm9maWxlJTIyJTJDJTIyUmVkaXJlY3RUb1NpZ25JbiUyMiUyQyUyMlJlZGlyZWN0VG9TaWduVXAlMjIlMkMlMjJSZWRpcmVjdFRvVXNlclByb2ZpbGUlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGd29ya3NwYWNlcyUyRmZpbmFsYWdlbnQtbWFpbi1maW5hbCUyRm5vZGVfbW9kdWxlcyUyRiU0MGNsZXJrJTJGbmV4dGpzJTJGZGlzdCUyRmVzbSUyRmNsaWVudC1ib3VuZGFyeSUyRmhvb2tzLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyRW1haWxMaW5rRXJyb3JDb2RlJTIyJTJDJTIyTWFnaWNMaW5rRXJyb3JDb2RlJTIyJTJDJTIyV2l0aENsZXJrJTIyJTJDJTIyV2l0aFNlc3Npb24lMjIlMkMlMjJXaXRoVXNlciUyMiUyQyUyMmlzQ2xlcmtBUElSZXNwb25zZUVycm9yJTIyJTJDJTIyaXNFbWFpbExpbmtFcnJvciUyMiUyQyUyMmlzS25vd25FcnJvciUyMiUyQyUyMmlzTWFnaWNMaW5rRXJyb3IlMjIlMkMlMjJpc01ldGFtYXNrRXJyb3IlMjIlMkMlMjJ1c2VBdXRoJTIyJTJDJTIydXNlQ2xlcmslMjIlMkMlMjJ1c2VFbWFpbExpbmslMjIlMkMlMjJ1c2VNYWdpY0xpbmslMjIlMkMlMjJ1c2VPcmdhbml6YXRpb24lMjIlMkMlMjJ1c2VPcmdhbml6YXRpb25MaXN0JTIyJTJDJTIydXNlT3JnYW5pemF0aW9ucyUyMiUyQyUyMnVzZVNlc3Npb24lMjIlMkMlMjJ1c2VTZXNzaW9uTGlzdCUyMiUyQyUyMnVzZVNpZ25JbiUyMiUyQyUyMnVzZVNpZ25VcCUyMiUyQyUyMnVzZVVzZXIlMjIlMkMlMjJ3aXRoQ2xlcmslMjIlMkMlMjJ3aXRoU2Vzc2lvbiUyMiUyQyUyMndpdGhVc2VyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRndvcmtzcGFjZXMlMkZmaW5hbGFnZW50LW1haW4tZmluYWwlMkZub2RlX21vZHVsZXMlMkYlNDBjbGVyayUyRm5leHRqcyUyRmRpc3QlMkZlc20lMkZjbGllbnQtYm91bmRhcnklMkZ1aUNvbXBvbmVudHMuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJDcmVhdGVPcmdhbml6YXRpb24lMjIlMkMlMjJHb29nbGVPbmVUYXAlMjIlMkMlMjJPcmdhbml6YXRpb25MaXN0JTIyJTJDJTIyT3JnYW5pemF0aW9uUHJvZmlsZSUyMiUyQyUyMk9yZ2FuaXphdGlvblN3aXRjaGVyJTIyJTJDJTIyU2lnbkluJTIyJTJDJTIyU2lnbkluQnV0dG9uJTIyJTJDJTIyU2lnbkluV2l0aE1ldGFtYXNrQnV0dG9uJTIyJTJDJTIyU2lnbk91dEJ1dHRvbiUyMiUyQyUyMlNpZ25VcCUyMiUyQyUyMlNpZ25VcEJ1dHRvbiUyMiUyQyUyMlVzZXJCdXR0b24lMjIlMkMlMjJVc2VyUHJvZmlsZSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC5qcyU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGd29ya3NwYWNlcyUyRmZpbmFsYWdlbnQtbWFpbi1maW5hbCUyRnBhY2thZ2VzJTJGd2ViLWFkbWluJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9RQUFzTDtBQUN0TDtBQUNBLHdRQUF3WDtBQUN4WDtBQUNBLGdQQUF5aEI7QUFDemhCO0FBQ0EsOFBBQXVYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vP2E0NzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDbGllbnRDbGVya1Byb3ZpZGVyXCJdICovIFwiL3dvcmtzcGFjZXMvZmluYWxhZ2VudC1tYWluLWZpbmFsL25vZGVfbW9kdWxlcy9AY2xlcmsvbmV4dGpzL2Rpc3QvZXNtL2FwcC1yb3V0ZXIvY2xpZW50L0NsZXJrUHJvdmlkZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkF1dGhlbnRpY2F0ZVdpdGhSZWRpcmVjdENhbGxiYWNrXCIsXCJDbGVya0xvYWRlZFwiLFwiQ2xlcmtMb2FkaW5nXCIsXCJNdWx0aXNlc3Npb25BcHBTdXBwb3J0XCIsXCJSZWRpcmVjdFRvQ3JlYXRlT3JnYW5pemF0aW9uXCIsXCJSZWRpcmVjdFRvT3JnYW5pemF0aW9uUHJvZmlsZVwiLFwiUmVkaXJlY3RUb1NpZ25JblwiLFwiUmVkaXJlY3RUb1NpZ25VcFwiLFwiUmVkaXJlY3RUb1VzZXJQcm9maWxlXCJdICovIFwiL3dvcmtzcGFjZXMvZmluYWxhZ2VudC1tYWluLWZpbmFsL25vZGVfbW9kdWxlcy9AY2xlcmsvbmV4dGpzL2Rpc3QvZXNtL2NsaWVudC1ib3VuZGFyeS9jb250cm9sQ29tcG9uZW50cy5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiRW1haWxMaW5rRXJyb3JDb2RlXCIsXCJNYWdpY0xpbmtFcnJvckNvZGVcIixcIldpdGhDbGVya1wiLFwiV2l0aFNlc3Npb25cIixcIldpdGhVc2VyXCIsXCJpc0NsZXJrQVBJUmVzcG9uc2VFcnJvclwiLFwiaXNFbWFpbExpbmtFcnJvclwiLFwiaXNLbm93bkVycm9yXCIsXCJpc01hZ2ljTGlua0Vycm9yXCIsXCJpc01ldGFtYXNrRXJyb3JcIixcInVzZUF1dGhcIixcInVzZUNsZXJrXCIsXCJ1c2VFbWFpbExpbmtcIixcInVzZU1hZ2ljTGlua1wiLFwidXNlT3JnYW5pemF0aW9uXCIsXCJ1c2VPcmdhbml6YXRpb25MaXN0XCIsXCJ1c2VPcmdhbml6YXRpb25zXCIsXCJ1c2VTZXNzaW9uXCIsXCJ1c2VTZXNzaW9uTGlzdFwiLFwidXNlU2lnbkluXCIsXCJ1c2VTaWduVXBcIixcInVzZVVzZXJcIixcIndpdGhDbGVya1wiLFwid2l0aFNlc3Npb25cIixcIndpdGhVc2VyXCJdICovIFwiL3dvcmtzcGFjZXMvZmluYWxhZ2VudC1tYWluLWZpbmFsL25vZGVfbW9kdWxlcy9AY2xlcmsvbmV4dGpzL2Rpc3QvZXNtL2NsaWVudC1ib3VuZGFyeS9ob29rcy5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQ3JlYXRlT3JnYW5pemF0aW9uXCIsXCJHb29nbGVPbmVUYXBcIixcIk9yZ2FuaXphdGlvbkxpc3RcIixcIk9yZ2FuaXphdGlvblByb2ZpbGVcIixcIk9yZ2FuaXphdGlvblN3aXRjaGVyXCIsXCJTaWduSW5cIixcIlNpZ25JbkJ1dHRvblwiLFwiU2lnbkluV2l0aE1ldGFtYXNrQnV0dG9uXCIsXCJTaWduT3V0QnV0dG9uXCIsXCJTaWduVXBcIixcIlNpZ25VcEJ1dHRvblwiLFwiVXNlckJ1dHRvblwiLFwiVXNlclByb2ZpbGVcIl0gKi8gXCIvd29ya3NwYWNlcy9maW5hbGFnZW50LW1haW4tZmluYWwvbm9kZV9tb2R1bGVzL0BjbGVyay9uZXh0anMvZGlzdC9lc20vY2xpZW50LWJvdW5kYXJ5L3VpQ29tcG9uZW50cy5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fusers%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fusers%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/users/page.js */ \"(ssr)/./src/app/users/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGcGFja2FnZXMlMkZ3ZWItYWRtaW4lMkZzcmMlMkZhcHAlMkZ1c2VycyUyRnBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUErRyIsInNvdXJjZXMiOlsid2VicGFjazovL0BiYWhpbmxpbmsvd2ViLWFkbWluLz9iNTdmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL3dvcmtzcGFjZXMvZmluYWxhZ2VudC1tYWluLWZpbmFsL3BhY2thZ2VzL3dlYi1hZG1pbi9zcmMvYXBwL3VzZXJzL3BhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fusers%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/users/page.js":
/*!*******************************!*\
  !*** ./src/app/users/page.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Switch/Switch.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_43__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,Menu,MenuItem,Paper,Select,Switch,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Security.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Business.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/MoreVert.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_37__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Edit.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_38__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Block.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_39__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Block,Business,CheckCircle,Delete,Edit,MoreVert,People,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ModernSidebar */ \"(ssr)/./src/components/ModernSidebar.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/ApiService */ \"(ssr)/./src/services/ApiService.js\");\n// BahinLink Web Admin Users Management Page\n// ⚠️ CRITICAL: Real user management with role-based access ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst UsersPage = ()=>{\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createDialogOpen, setCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editDialogOpen, setEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        role: \"\",\n        status: \"\",\n        search: \"\"\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        admins: 0,\n        supervisors: 0,\n        agents: 0,\n        clients: 0,\n        active: 0,\n        inactive: 0\n    });\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: \"\",\n        lastName: \"\",\n        email: \"\",\n        phone: \"\",\n        role: \"AGENT\",\n        isActive: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUsers();\n    }, [\n        filters\n    ]);\n    const loadUsers = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const params = {};\n            if (filters.role) params.role = filters.role;\n            if (filters.status) params.status = filters.status;\n            if (filters.search) params.search = filters.search;\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/users\", params);\n            if (response.success) {\n                setUsers(response.data);\n                // Calculate stats\n                const newStats = {\n                    total: response.data.length,\n                    admins: response.data.filter((u)=>u.role === \"ADMIN\").length,\n                    supervisors: response.data.filter((u)=>u.role === \"SUPERVISOR\").length,\n                    agents: response.data.filter((u)=>u.role === \"AGENT\").length,\n                    clients: response.data.filter((u)=>u.role === \"CLIENT\").length,\n                    active: response.data.filter((u)=>u.isActive).length,\n                    inactive: response.data.filter((u)=>!u.isActive).length\n                };\n                setStats(newStats);\n            }\n        } catch (error) {\n            console.error(\"Error loading users:\", error);\n            setError(\"Failed to load users\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleMenuOpen = (event, user)=>{\n        setAnchorEl(event.currentTarget);\n        setSelectedUser(user);\n    };\n    const handleMenuClose = ()=>{\n        setAnchorEl(null);\n        setSelectedUser(null);\n    };\n    const handleCreateUser = async ()=>{\n        try {\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/users\", newUser);\n            if (response.success) {\n                setCreateDialogOpen(false);\n                setNewUser({\n                    firstName: \"\",\n                    lastName: \"\",\n                    email: \"\",\n                    phone: \"\",\n                    role: \"AGENT\",\n                    isActive: true\n                });\n                loadUsers();\n            }\n        } catch (error) {\n            console.error(\"Error creating user:\", error);\n            setError(\"Failed to create user\");\n        }\n    };\n    const handleEditUser = async ()=>{\n        try {\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`/users/${selectedUser.id}`, selectedUser);\n            if (response.success) {\n                setEditDialogOpen(false);\n                setSelectedUser(null);\n                loadUsers();\n            }\n        } catch (error) {\n            console.error(\"Error updating user:\", error);\n            setError(\"Failed to update user\");\n        }\n    };\n    const handleToggleStatus = async (userId, isActive)=>{\n        try {\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`/users/${userId}`, {\n                isActive: !isActive\n            });\n            if (response.success) {\n                loadUsers();\n            }\n        } catch (error) {\n            console.error(\"Error updating user status:\", error);\n            setError(\"Failed to update user status\");\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (window.confirm(\"Are you sure you want to delete this user?\")) {\n            try {\n                const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(`/users/${userId}`);\n                if (response.success) {\n                    loadUsers();\n                }\n            } catch (error) {\n                console.error(\"Error deleting user:\", error);\n                setError(\"Failed to delete user\");\n            }\n        }\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"ADMIN\":\n                return \"error\";\n            case \"SUPERVISOR\":\n                return \"warning\";\n            case \"AGENT\":\n                return \"primary\";\n            case \"CLIENT\":\n                return \"info\";\n            default:\n                return \"default\";\n        }\n    };\n    const getRoleIcon = (role)=>{\n        switch(role){\n            case \"ADMIN\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 211,\n                    columnNumber: 28\n                }, undefined);\n            case \"SUPERVISOR\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 212,\n                    columnNumber: 33\n                }, undefined);\n            case \"AGENT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 213,\n                    columnNumber: 28\n                }, undefined);\n            case \"CLIENT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 214,\n                    columnNumber: 29\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 215,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    const getInitials = (firstName, lastName)=>{\n        return `${firstName?.[0] || \"\"}${lastName?.[0] || \"\"}`.toUpperCase();\n    };\n    const StatCard = ({ title, value, icon: Icon, color = \"primary\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: `${color}.main`,\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: `${color}.main`,\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 235,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n            lineNumber: 224,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    p: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            mb: 3,\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                variant: \"h4\",\n                                fontWeight: \"bold\",\n                                children: \"User Management\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 248,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    gap: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 254,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        onClick: loadUsers,\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 252,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"contained\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 261,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        onClick: ()=>setCreateDialogOpen(true),\n                                        children: \"Add User\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 259,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 251,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 247,\n                        columnNumber: 7\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        severity: \"error\",\n                        sx: {\n                            mb: 2\n                        },\n                        onClose: ()=>setError(null),\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        sx: {\n                            mb: 3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"Total Users\",\n                                    value: stats.total,\n                                    icon: _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                    color: \"primary\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 279,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 278,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"Admins\",\n                                    value: stats.admins,\n                                    icon: _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                    color: \"error\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 287,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 286,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"Supervisors\",\n                                    value: stats.supervisors,\n                                    icon: _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                    color: \"warning\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 295,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 294,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"Agents\",\n                                    value: stats.agents,\n                                    icon: _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                    color: \"info\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 303,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 302,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"Clients\",\n                                    value: stats.clients,\n                                    icon: _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                    color: \"secondary\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 311,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 310,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                    title: \"Active\",\n                                    value: stats.active,\n                                    icon: _barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                    color: \"success\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 319,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 318,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 277,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        sx: {\n                            p: 2,\n                            mb: 3\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            container: true,\n                            spacing: 2,\n                            alignItems: \"center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        fullWidth: true,\n                                        size: \"small\",\n                                        label: \"Search\",\n                                        value: filters.search,\n                                        onChange: (e)=>setFilters((prev)=>({\n                                                    ...prev,\n                                                    search: e.target.value\n                                                })),\n                                        placeholder: \"Search by name or email...\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 331,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        fullWidth: true,\n                                        size: \"small\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                children: \"Role\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                value: filters.role,\n                                                label: \"Role\",\n                                                onChange: (e)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            role: e.target.value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: \"\",\n                                                        children: \"All Roles\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: \"ADMIN\",\n                                                        children: \"Admin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: \"SUPERVISOR\",\n                                                        children: \"Supervisor\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: \"AGENT\",\n                                                        children: \"Agent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: \"CLIENT\",\n                                                        children: \"Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 345,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 342,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        fullWidth: true,\n                                        size: \"small\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                value: filters.status,\n                                                label: \"Status\",\n                                                onChange: (e)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            status: e.target.value\n                                                        })),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: \"\",\n                                                        children: \"All Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: \"active\",\n                                                        children: \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: \"inactive\",\n                                                        children: \"Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 362,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 360,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 359,\n                                    columnNumber: 11\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    item: true,\n                                    xs: 12,\n                                    sm: 6,\n                                    md: 3,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"outlined\",\n                                        fullWidth: true,\n                                        onClick: ()=>setFilters({\n                                                role: \"\",\n                                                status: \"\",\n                                                search: \"\"\n                                            }),\n                                        children: \"Clear Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 374,\n                                    columnNumber: 11\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 330,\n                            columnNumber: 9\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 329,\n                        columnNumber: 7\n                    }, undefined),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            py: 4\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        component: _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                children: \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                children: \"Role\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 397,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                children: \"Contact\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 399,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                children: \"Created\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                align: \"right\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 401,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 395,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                    children: users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            colSpan: 6,\n                                            align: \"center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"body2\",\n                                                color: \"textSecondary\",\n                                                sx: {\n                                                    py: 4\n                                                },\n                                                children: \"No users found\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 408,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 407,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 406,\n                                        columnNumber: 17\n                                    }, undefined) : users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            hover: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            gap: 2\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                                                sx: {\n                                                                    bgcolor: \"primary.main\"\n                                                                },\n                                                                children: getInitials(user.firstName, user.lastName)\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        variant: \"body2\",\n                                                                        fontWeight: \"medium\",\n                                                                        children: [\n                                                                            user.firstName,\n                                                                            \" \",\n                                                                            user.lastName\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        variant: \"caption\",\n                                                                        color: \"textSecondary\",\n                                                                        children: user.email\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                        lineNumber: 425,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                                        icon: getRoleIcon(user.role),\n                                                        label: user.role,\n                                                        color: getRoleColor(user.role),\n                                                        size: \"small\",\n                                                        variant: \"outlined\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: user.phone || \"No phone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        sx: {\n                                                            display: \"flex\",\n                                                            alignItems: \"center\",\n                                                            gap: 1\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                                                checked: user.isActive,\n                                                                onChange: ()=>handleToggleStatus(user.id, user.isActive),\n                                                                size: \"small\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                children: user.isActive ? \"Active\" : \"Inactive\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        children: new Date(user.createdAt).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    align: \"right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                                        size: \"small\",\n                                                        onClick: (e)=>handleMenuOpen(e, user),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {}, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                            lineNumber: 472,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 415,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 392,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                        anchorEl: anchorEl,\n                        open: Boolean(anchorEl),\n                        onClose: handleMenuClose,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: ()=>{\n                                    setEditDialogOpen(true);\n                                    handleMenuClose();\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_37__[\"default\"], {\n                                        fontSize: \"small\",\n                                        sx: {\n                                            mr: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 493,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Edit User\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 489,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: ()=>{\n                                    handleToggleStatus(selectedUser?.id, selectedUser?.isActive);\n                                    handleMenuClose();\n                                },\n                                children: selectedUser?.isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_38__[\"default\"], {\n                                            fontSize: \"small\",\n                                            sx: {\n                                                mr: 1\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Deactivate\"\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            fontSize: \"small\",\n                                            sx: {\n                                                mr: 1\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 507,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        \"Activate\"\n                                    ]\n                                }, void 0, true)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 496,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                onClick: ()=>{\n                                    handleDeleteUser(selectedUser?.id);\n                                    handleMenuClose();\n                                },\n                                sx: {\n                                    color: \"error.main\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Block_Business_CheckCircle_Delete_Edit_MoreVert_People_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_39__[\"default\"], {\n                                        fontSize: \"small\",\n                                        sx: {\n                                            mr: 1\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 519,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    \"Delete User\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 512,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 484,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                        open: createDialogOpen,\n                        onClose: ()=>setCreateDialogOpen(false),\n                        maxWidth: \"sm\",\n                        fullWidth: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                children: \"Create New User\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 526,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    container: true,\n                                    spacing: 2,\n                                    sx: {\n                                        mt: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                fullWidth: true,\n                                                label: \"First Name\",\n                                                value: newUser.firstName,\n                                                onChange: (e)=>setNewUser((prev)=>({\n                                                            ...prev,\n                                                            firstName: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 530,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 529,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                fullWidth: true,\n                                                label: \"Last Name\",\n                                                value: newUser.lastName,\n                                                onChange: (e)=>setNewUser((prev)=>({\n                                                            ...prev,\n                                                            lastName: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 537,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                fullWidth: true,\n                                                label: \"Email\",\n                                                type: \"email\",\n                                                value: newUser.email,\n                                                onChange: (e)=>setNewUser((prev)=>({\n                                                            ...prev,\n                                                            email: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 546,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 545,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                fullWidth: true,\n                                                label: \"Phone\",\n                                                value: newUser.phone,\n                                                onChange: (e)=>setNewUser((prev)=>({\n                                                            ...prev,\n                                                            phone: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 555,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 554,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                fullWidth: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 564,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        value: newUser.role,\n                                                        label: \"Role\",\n                                                        onChange: (e)=>setNewUser((prev)=>({\n                                                                    ...prev,\n                                                                    role: e.target.value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: \"ADMIN\",\n                                                                children: \"Admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: \"SUPERVISOR\",\n                                                                children: \"Supervisor\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: \"AGENT\",\n                                                                children: \"Agent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 572,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: \"CLIENT\",\n                                                                children: \"Client\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 573,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 563,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 562,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 528,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 527,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: ()=>setCreateDialogOpen(false),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 580,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: handleCreateUser,\n                                        variant: \"contained\",\n                                        children: \"Create User\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 581,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 579,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 525,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_40__[\"default\"], {\n                        open: editDialogOpen,\n                        onClose: ()=>setEditDialogOpen(false),\n                        maxWidth: \"sm\",\n                        fullWidth: true,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_41__[\"default\"], {\n                                children: \"Edit User\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 587,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_42__[\"default\"], {\n                                children: selectedUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    container: true,\n                                    spacing: 2,\n                                    sx: {\n                                        mt: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                fullWidth: true,\n                                                label: \"First Name\",\n                                                value: selectedUser.firstName,\n                                                onChange: (e)=>setSelectedUser((prev)=>({\n                                                            ...prev,\n                                                            firstName: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 592,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 591,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                fullWidth: true,\n                                                label: \"Last Name\",\n                                                value: selectedUser.lastName,\n                                                onChange: (e)=>setSelectedUser((prev)=>({\n                                                            ...prev,\n                                                            lastName: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 600,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 599,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                fullWidth: true,\n                                                label: \"Email\",\n                                                type: \"email\",\n                                                value: selectedUser.email,\n                                                onChange: (e)=>setSelectedUser((prev)=>({\n                                                            ...prev,\n                                                            email: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 608,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 607,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                fullWidth: true,\n                                                label: \"Phone\",\n                                                value: selectedUser.phone || \"\",\n                                                onChange: (e)=>setSelectedUser((prev)=>({\n                                                            ...prev,\n                                                            phone: e.target.value\n                                                        }))\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 617,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 616,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                fullWidth: true,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        children: \"Role\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        value: selectedUser.role,\n                                                        label: \"Role\",\n                                                        onChange: (e)=>setSelectedUser((prev)=>({\n                                                                    ...prev,\n                                                                    role: e.target.value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: \"ADMIN\",\n                                                                children: \"Admin\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 632,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: \"SUPERVISOR\",\n                                                                children: \"Supervisor\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 633,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: \"AGENT\",\n                                                                children: \"Agent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: \"CLIENT\",\n                                                                children: \"Client\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 627,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 625,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 624,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 588,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_43__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: ()=>setEditDialogOpen(false),\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 643,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_Menu_MenuItem_Paper_Select_Switch_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        onClick: handleEditUser,\n                                        variant: \"contained\",\n                                        children: \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 644,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 642,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 586,\n                        columnNumber: 7\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n        lineNumber: 242,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UsersPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/users/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ModernSidebar.js":
/*!*****************************************!*\
  !*** ./src/components/ModernSidebar.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/system/esm/colorManipulator.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemButton/ListItemButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assessment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Analytics.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Shield.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ChevronLeft.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ExitToApp.js\");\n// BahinLink Modern Sidebar Navigation Component\n// ⚠️ CRITICAL: Sophisticated 2024 design with real data integration ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst SIDEBAR_WIDTH_EXPANDED = 280;\nconst SIDEBAR_WIDTH_COLLAPSED = 72;\nconst ModernSidebar = ()=>{\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const { signOut } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modern 2024 color palette - sophisticated neutrals with accent\n    const colors = {\n        sidebar: \"#1a1d29\",\n        sidebarHover: \"#252936\",\n        accent: \"#6366f1\",\n        accentHover: \"#5855eb\",\n        text: \"#e2e8f0\",\n        textSecondary: \"#94a3b8\",\n        textMuted: \"#64748b\",\n        border: \"#334155\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\" // Modern red\n    };\n    const navigationItems = [\n        {\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            path: \"/\",\n            description: \"Overview & Analytics\"\n        },\n        {\n            label: \"Agents\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            path: \"/agents\",\n            description: \"Security Personnel\"\n        },\n        {\n            label: \"Sites\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/sites\",\n            description: \"Client Locations\"\n        },\n        {\n            label: \"Shifts\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            path: \"/shifts\",\n            description: \"Schedule Management\"\n        },\n        {\n            label: \"Reports\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            path: \"/reports\",\n            description: \"Security Reports\"\n        },\n        {\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            path: \"/analytics\",\n            description: \"Performance Metrics\"\n        },\n        {\n            label: \"Users\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            path: \"/users\",\n            description: \"User Management\"\n        }\n    ];\n    const handleNavigation = (path)=>{\n        router.push(path);\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/sign-in\");\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        variant: \"permanent\",\n        sx: {\n            width: sidebarWidth,\n            flexShrink: 0,\n            \"& .MuiDrawer-paper\": {\n                width: sidebarWidth,\n                boxSizing: \"border-box\",\n                backgroundColor: colors.sidebar,\n                borderRight: `1px solid ${colors.border}`,\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.enteringScreen\n                }),\n                overflowX: \"hidden\",\n                // Modern shadow\n                boxShadow: \"4px 0 24px rgba(0, 0, 0, 0.12)\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    p: 3,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: isCollapsed ? \"center\" : \"space-between\",\n                    borderBottom: `1px solid ${colors.border}`,\n                    minHeight: 80\n                },\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                sx: {\n                                    fontSize: 32,\n                                    color: colors.accent,\n                                    mr: 2,\n                                    filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            color: colors.text,\n                                            fontWeight: 700,\n                                            fontSize: \"1.25rem\",\n                                            letterSpacing: \"-0.025em\"\n                                        },\n                                        children: \"BahinLink\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: colors.textSecondary,\n                                            fontSize: \"0.75rem\",\n                                            fontWeight: 500\n                                        },\n                                        children: \"Security Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, undefined),\n                    isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        sx: {\n                            fontSize: 28,\n                            color: colors.accent,\n                            filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: toggleSidebar,\n                        sx: {\n                            color: colors.textSecondary,\n                            backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                            width: 32,\n                            height: 32,\n                            \"&:hover\": {\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                color: colors.text\n                            },\n                            transition: \"all 0.2s ease-in-out\"\n                        },\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 225,\n                            columnNumber: 26\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 225,\n                            columnNumber: 45\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    label: \"LIVE\",\n                    size: \"small\",\n                    sx: {\n                        backgroundColor: colors.success,\n                        color: \"white\",\n                        fontWeight: 600,\n                        fontSize: \"0.75rem\",\n                        height: 24,\n                        \"& .MuiChip-label\": {\n                            px: 1.5\n                        },\n                        display: isCollapsed ? \"none\" : \"flex\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    px: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        py: 0\n                    },\n                    children: navigationItems.map((item)=>{\n                        const Icon = item.icon;\n                        const active = isActive(item.path);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            disablePadding: true,\n                            sx: {\n                                mb: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                title: isCollapsed ? `${item.label} - ${item.description}` : \"\",\n                                placement: \"right\",\n                                arrow: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    onClick: ()=>handleNavigation(item.path),\n                                    sx: {\n                                        borderRadius: 2,\n                                        mx: 1,\n                                        px: 2,\n                                        py: 1.5,\n                                        minHeight: 48,\n                                        backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                        border: active ? `1px solid ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.3)}` : \"1px solid transparent\",\n                                        \"&:hover\": {\n                                            backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2) : (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                            transform: \"translateX(2px)\"\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            sx: {\n                                                color: active ? colors.accent : colors.textSecondary,\n                                                minWidth: isCollapsed ? \"auto\" : 40,\n                                                mr: isCollapsed ? 0 : 1.5,\n                                                transition: \"color 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                sx: {\n                                                    fontSize: 22\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                lineNumber: 290,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            primary: item.label,\n                                            secondary: item.description,\n                                            primaryTypographyProps: {\n                                                sx: {\n                                                    color: active ? colors.text : colors.textSecondary,\n                                                    fontWeight: active ? 600 : 500,\n                                                    fontSize: \"0.875rem\",\n                                                    transition: \"color 0.2s ease-in-out\"\n                                                }\n                                            },\n                                            secondaryTypographyProps: {\n                                                sx: {\n                                                    color: colors.textMuted,\n                                                    fontSize: \"0.75rem\",\n                                                    mt: 0.25\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 294,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 262,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 257,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.path, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 256,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        sx: {\n                            borderColor: colors.border,\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        disablePadding: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: isCollapsed ? \"Settings\" : \"\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: ()=>handleNavigation(\"/settings\"),\n                                sx: {\n                                    borderRadius: 2,\n                                    mx: 1,\n                                    px: 2,\n                                    py: 1.5,\n                                    minHeight: 48,\n                                    backgroundColor: pathname === \"/settings\" ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                        transform: \"translateX(2px)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\",\n                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        sx: {\n                                            color: pathname === \"/settings\" ? colors.accent : colors.textSecondary,\n                                            minWidth: isCollapsed ? \"auto\" : 40,\n                                            mr: isCollapsed ? 0 : 1.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            sx: {\n                                                fontSize: 22\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        primary: \"Settings\",\n                                        primaryTypographyProps: {\n                                            sx: {\n                                                color: pathname === \"/settings\" ? colors.text : colors.textSecondary,\n                                                fontWeight: pathname === \"/settings\" ? 600 : 500,\n                                                fontSize: \"0.875rem\"\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 3,\n                    borderTop: `1px solid ${colors.border}`,\n                    pt: 2\n                },\n                children: !isCollapsed ? // Expanded Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    sx: {\n                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                        borderRadius: 3,\n                        p: 2,\n                        mx: 1,\n                        border: `1px solid ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.border, 0.5)}`\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            mb: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    src: user?.imageUrl,\n                                    alt: user?.fullName,\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        mr: 2,\n                                        border: `2px solid ${colors.accent}`,\n                                        boxShadow: `0 0 0 2px ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)}`\n                                    },\n                                    children: user?.firstName?.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    sx: {\n                                        flex: 1,\n                                        minWidth: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: colors.text,\n                                                fontWeight: 600,\n                                                fontSize: \"0.875rem\",\n                                                lineHeight: 1.2,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            mt: 0.5,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: 14,\n                                                        color: colors.accent,\n                                                        mr: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    sx: {\n                                                        color: colors.textSecondary,\n                                                        fontSize: \"0.75rem\",\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"Administrator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: colors.textMuted,\n                                                fontSize: \"0.7rem\",\n                                                display: \"block\",\n                                                mt: 0.25,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: user?.primaryEmailAddress?.emailAddress\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            onClick: handleSignOut,\n                            sx: {\n                                width: \"100%\",\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                color: colors.error,\n                                borderRadius: 2,\n                                py: 1,\n                                \"&:hover\": {\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                    transform: \"translateY(-1px)\"\n                                },\n                                transition: \"all 0.2s ease-in-out\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18,\n                                        mr: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    variant: \"caption\",\n                                    sx: {\n                                        fontWeight: 600\n                                    },\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, undefined) : // Collapsed Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: `${user?.fullName} - Administrator`,\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                src: user?.imageUrl,\n                                alt: user?.fullName,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    border: `2px solid ${colors.accent}`,\n                                    boxShadow: `0 0 0 2px ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)}`,\n                                    cursor: \"pointer\"\n                                },\n                                children: user?.firstName?.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: \"Sign Out\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                onClick: handleSignOut,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                    color: colors.error,\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                        transform: \"scale(1.05)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 515,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 500,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 483,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ModernSidebar.js\n");

/***/ }),

/***/ "(ssr)/./src/services/ApiService.js":
/*!************************************!*\
  !*** ./src/services/ApiService.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../../node_modules/axios/lib/axios.js\");\n// BahinLink Web Admin API Service\n// ⚠️ CRITICAL: Real production API integration with Clerk authentication ONLY\n\n// Real production API base URL\nconst API_BASE_URL =  true ? \"http://localhost:3001/api\" : 0;\nclass ApiService {\n    constructor(){\n        this.baseURL = API_BASE_URL;\n        this.setupInterceptors();\n    }\n    /**\n   * Setup axios interceptors for real authentication\n   */ setupInterceptors() {\n        // Request interceptor to add real Clerk token\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.request.use(async (config)=>{\n            try {\n                // For client-side requests, we'll handle auth in the component\n                // This is a simplified version for development\n                config.headers[\"Content-Type\"] = \"application/json\";\n                config.baseURL = this.baseURL;\n                console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n                return config;\n            } catch (error) {\n                console.error(\"Request interceptor error:\", error);\n                return config;\n            }\n        }, (error)=>{\n            console.error(\"Request interceptor error:\", error);\n            return Promise.reject(error);\n        });\n        // Response interceptor for error handling\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.response.use((response)=>{\n            console.log(`API Response: ${response.status} ${response.config.url}`);\n            return response.data;\n        }, (error)=>{\n            console.error(\"API Error:\", error.response?.data || error.message);\n            if (error.response?.status === 401) {\n                // Redirect to sign-in\n                window.location.href = \"/sign-in\";\n            }\n            return Promise.reject(error.response?.data || error);\n        });\n    }\n    /**\n   * GET request to real API\n   */ async get(endpoint, params = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(endpoint, {\n                params\n            });\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * POST request to real API\n   */ async post(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * PUT request to real API\n   */ async put(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * DELETE request to real API\n   */ async delete(endpoint) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(endpoint);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Dashboard Analytics\n    async getDashboardAnalytics() {\n        return this.get(\"/analytics/dashboard\");\n    }\n    async getPerformanceMetrics(params = {}) {\n        return this.get(\"/analytics/performance\", params);\n    }\n    async getAgentLocations() {\n        return this.get(\"/analytics/locations\");\n    }\n    // User Management\n    async getUsers(params = {}) {\n        return this.get(\"/users\", params);\n    }\n    async getUser(userId) {\n        return this.get(`/users/${userId}`);\n    }\n    async updateUser(userId, data) {\n        return this.put(`/users/${userId}`, data);\n    }\n    // Agent Management\n    async getAgents(params = {}) {\n        return this.get(\"/agents\", params);\n    }\n    async getAgent(agentId) {\n        return this.get(`/agents/${agentId}`);\n    }\n    async updateAgent(agentId, data) {\n        return this.put(`/agents/${agentId}`, data);\n    }\n    async getNearbyAgents(latitude, longitude, radius) {\n        return this.get(\"/agents/nearby\", {\n            latitude,\n            longitude,\n            radius\n        });\n    }\n    // Site Management\n    async getSites(params = {}) {\n        return this.get(\"/sites\", params);\n    }\n    async createSite(data) {\n        return this.post(\"/sites\", data);\n    }\n    async getSite(siteId) {\n        return this.get(`/sites/${siteId}`);\n    }\n    async updateSite(siteId, data) {\n        return this.put(`/sites/${siteId}`, data);\n    }\n    async generateSiteQR(siteId) {\n        return this.post(`/sites/${siteId}/generate-qr`);\n    }\n    // Shift Management\n    async getShifts(params = {}) {\n        return this.get(\"/shifts\", params);\n    }\n    async createShift(data) {\n        return this.post(\"/shifts\", data);\n    }\n    async getShift(shiftId) {\n        return this.get(`/shifts/${shiftId}`);\n    }\n    async updateShift(shiftId, data) {\n        return this.put(`/shifts/${shiftId}`, data);\n    }\n    async assignShift(shiftId, agentId) {\n        return this.put(`/shifts/${shiftId}`, {\n            agentId\n        });\n    }\n    // Time Tracking\n    async getTimeEntries(params = {}) {\n        return this.get(\"/time/entries\", params);\n    }\n    async verifyTimeEntry(timeEntryId, data) {\n        return this.put(`/time/entries/${timeEntryId}/verify`, data);\n    }\n    // Reports Management\n    async getReports(params = {}) {\n        return this.get(\"/reports\", params);\n    }\n    async getReport(reportId) {\n        return this.get(`/reports/${reportId}`);\n    }\n    async approveReport(reportId, data = {}) {\n        return this.post(`/reports/${reportId}/approve`, data);\n    }\n    async rejectReport(reportId, data) {\n        return this.post(`/reports/${reportId}/reject`, data);\n    }\n    // Notifications\n    async getNotifications(params = {}) {\n        return this.get(\"/notifications\", params);\n    }\n    async sendNotification(data) {\n        return this.post(\"/notifications\", data);\n    }\n    async broadcastNotification(data) {\n        return this.post(\"/notifications/broadcast\", data);\n    }\n    // Communications\n    async getMessages(params = {}) {\n        return this.get(\"/communications\", params);\n    }\n    async sendMessage(data) {\n        return this.post(\"/communications\", data);\n    }\n    async getMessageThreads(params = {}) {\n        return this.get(\"/communications/threads\", params);\n    }\n    // Client Management\n    async getClients(params = {}) {\n        return this.get(\"/clients\", params);\n    }\n    async getClientRequests(params = {}) {\n        return this.get(\"/client/requests\", params);\n    }\n    async updateClientRequest(requestId, data) {\n        return this.put(`/client/requests/${requestId}`, data);\n    }\n    // Geofencing\n    async checkGeofence(data) {\n        return this.post(\"/geofence/check\", data);\n    }\n    async getGeofenceViolations(params = {}) {\n        return this.get(\"/geofence/violations\", params);\n    }\n    async resolveGeofenceViolation(violationId, data) {\n        return this.put(`/geofence/violations/${violationId}/resolve`, data);\n    }\n    // File Management\n    async uploadFile(file, type = \"document\") {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const endpoint = `/upload/${type}`;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                },\n                baseURL: this.baseURL\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Bulk Operations\n    async bulkUpdateShifts(shiftIds, data) {\n        return this.put(\"/shifts/bulk\", {\n            shiftIds,\n            ...data\n        });\n    }\n    async bulkNotifyAgents(agentIds, notification) {\n        return this.post(\"/notifications/bulk\", {\n            agentIds,\n            ...notification\n        });\n    }\n    // Export Functions\n    async exportReports(params = {}) {\n        return this.get(\"/reports/export\", params);\n    }\n    async exportTimeEntries(params = {}) {\n        return this.get(\"/time/entries/export\", params);\n    }\n    async exportAgentPerformance(params = {}) {\n        return this.get(\"/analytics/performance/export\", params);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new ApiService());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/ApiService.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8d498114e33b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRjMjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZDQ5ODExNGUzM2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"(rsc)/../../node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n// BahinLink Web Admin Layout\n// ⚠️ CRITICAL: Real Clerk authentication and production data integration ONLY\n\n\n\n\n// Real Clerk publishable key - MUST be production key\nconst CLERK_PUBLISHABLE_KEY = \"pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk\";\nconst metadata = {\n    title: \"BahinLink Admin - Security Workforce Management\",\n    description: \"Real-time security workforce management for Bahin SARL\",\n    keywords: \"security, workforce, management, GPS tracking, Senegal, Bahin SARL\",\n    authors: [\n        {\n            name: \"Bahin SARL\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.ClerkProvider, {\n        publishableKey: CLERK_PUBLISHABLE_KEY,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/users/page.js":
/*!*******************************!*\
  !*** ./src/app/users/page.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/users/page.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/node-fetch-native","vendor-chunks/@clerk","vendor-chunks/@peculiar","vendor-chunks/asn1js","vendor-chunks/@opentelemetry","vendor-chunks/webcrypto-core","vendor-chunks/swr","vendor-chunks/pvtsutils","vendor-chunks/tslib","vendor-chunks/pvutils","vendor-chunks/cookie","vendor-chunks/deepmerge","vendor-chunks/use-sync-external-store","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/react-is","vendor-chunks/hoist-non-react-statics","vendor-chunks/@babel","vendor-chunks/object-assign","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@popperjs","vendor-chunks/react-transition-group","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();