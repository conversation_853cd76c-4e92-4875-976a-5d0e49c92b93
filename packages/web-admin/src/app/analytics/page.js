'use client';

import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Button,
  FormControl,
  Select,
  MenuItem,
  InputLabel
} from '@mui/material';
import { Download as DownloadIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import ModernSidebar from '../components/ModernSidebar';

export default function AnalyticsPage() {
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [analyticsData, setAnalyticsData] = useState(null);

  // Sample KPI data
  const kpis = {
    totalShifts: 156,
    completedShifts: 142,
    activeAgents: 18,
    efficiency: 94.2,
    totalHours: 1248,
    averageRating: 4.7,
    totalReports: 89,
    responseTime: 12.5
  };

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      setAnalyticsData({
        shifts: [
          { date: '2024-01-15', completed: 12, cancelled: 2 },
          { date: '2024-01-16', completed: 15, cancelled: 1 },
          { date: '2024-01-17', completed: 18, cancelled: 0 },
          { date: '2024-01-18', completed: 14, cancelled: 3 },
          { date: '2024-01-19', completed: 16, cancelled: 1 },
          { date: '2024-01-20', completed: 20, cancelled: 2 },
          { date: '2024-01-21', completed: 17, cancelled: 1 }
        ]
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = () => {
    console.log('Exporting analytics data...');
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#ffffff' }}>
      <ModernSidebar />

      <Box component="main" sx={{ flexGrow: 1, p: 4 }}>
        {/* Clean Header */}
        <Box sx={{ mb: 6, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography 
              variant="h3" 
              sx={{ 
                fontWeight: 300, 
                color: '#1a1a1a',
                mb: 1,
                letterSpacing: '-0.02em',
                fontSize: '2.5rem'
              }}
            >
              Analytics
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: '#6b7280',
                fontSize: '16px'
              }}
            >
              Real-time insights and performance metrics
            </Typography>
          </Box>

          {/* Clean Controls */}
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <FormControl size="small" sx={{ minWidth: 140 }}>
              <InputLabel sx={{ fontSize: '14px' }}>Time Range</InputLabel>
              <Select
                value={timeRange}
                label="Time Range"
                onChange={(e) => setTimeRange(e.target.value)}
                sx={{ 
                  fontSize: '14px',
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#e5e7eb'
                  }
                }}
              >
                <MenuItem value="1d">Last 24 Hours</MenuItem>
                <MenuItem value="7d">Last 7 Days</MenuItem>
                <MenuItem value="30d">Last 30 Days</MenuItem>
                <MenuItem value="90d">Last 90 Days</MenuItem>
              </Select>
            </FormControl>
            
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExport}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Export
            </Button>
            
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadAnalyticsData}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Refresh
            </Button>
          </Box>
        </Box>

        {/* Minimalist KPI Grid */}
        <Box sx={{ mb: 8 }}>
          <Box 
            sx={{ 
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(240px, 1fr))',
              gap: 4,
              mb: 6
            }}
          >
            {/* Total Shifts */}
            <Box sx={{ 
              p: 4, 
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{ 
                fontWeight: 700, 
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {kpis.totalShifts}
              </Typography>
              <Typography variant="body1" sx={{ 
                color: '#6b7280', 
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Total Shifts
              </Typography>
              <Typography variant="caption" sx={{ 
                color: '#10b981', 
                fontSize: '13px',
                fontWeight: 500
              }}>
                +12.5% from last period
              </Typography>
            </Box>

            {/* Completion Rate */}
            <Box sx={{ 
              p: 4, 
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{ 
                fontWeight: 700, 
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {((kpis.completedShifts / kpis.totalShifts) * 100).toFixed(1)}%
              </Typography>
              <Typography variant="body1" sx={{ 
                color: '#6b7280', 
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Completion Rate
              </Typography>
              <Typography variant="caption" sx={{ 
                color: '#10b981', 
                fontSize: '13px',
                fontWeight: 500
              }}>
                +5.2% from last period
              </Typography>
            </Box>

            {/* Active Agents */}
            <Box sx={{ 
              p: 4, 
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{ 
                fontWeight: 700, 
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {kpis.activeAgents}
              </Typography>
              <Typography variant="body1" sx={{ 
                color: '#6b7280', 
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Active Agents
              </Typography>
              <Typography variant="caption" sx={{ 
                color: '#ef4444', 
                fontSize: '13px',
                fontWeight: 500
              }}>
                -1.5% from last period
              </Typography>
            </Box>

            {/* Efficiency */}
            <Box sx={{ 
              p: 4, 
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{ 
                fontWeight: 700, 
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {kpis.efficiency}%
              </Typography>
              <Typography variant="body1" sx={{ 
                color: '#6b7280', 
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Efficiency Rate
              </Typography>
              <Typography variant="caption" sx={{ 
                color: '#10b981', 
                fontSize: '13px',
                fontWeight: 500
              }}>
                +3.4% from last period
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Clean Performance Section */}
        {loading ? (
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '300px'
          }}>
            <CircularProgress size={40} sx={{ color: '#6b7280' }} />
          </Box>
        ) : analyticsData ? (
          <Box sx={{
            p: 6,
            borderRadius: 3,
            border: '1px solid #f1f5f9',
            backgroundColor: '#ffffff'
          }}>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 500,
                color: '#1a1a1a',
                mb: 4,
                fontSize: '1.75rem'
              }}
            >
              Performance Overview
            </Typography>

            <Box sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: 4
            }}>
              <Box sx={{ textAlign: 'center', p: 3 }}>
                <Typography variant="h3" sx={{
                  fontWeight: 600,
                  color: '#10b981',
                  mb: 1,
                  fontSize: '2rem'
                }}>
                  {kpis.totalHours.toLocaleString()}h
                </Typography>
                <Typography variant="body2" sx={{
                  color: '#6b7280',
                  fontSize: '14px'
                }}>
                  Total Hours Worked
                </Typography>
              </Box>

              <Box sx={{ textAlign: 'center', p: 3 }}>
                <Typography variant="h3" sx={{
                  fontWeight: 600,
                  color: '#3b82f6',
                  mb: 1,
                  fontSize: '2rem'
                }}>
                  {kpis.averageRating}/5
                </Typography>
                <Typography variant="body2" sx={{
                  color: '#6b7280',
                  fontSize: '14px'
                }}>
                  Average Rating
                </Typography>
              </Box>

              <Box sx={{ textAlign: 'center', p: 3 }}>
                <Typography variant="h3" sx={{
                  fontWeight: 600,
                  color: '#f59e0b',
                  mb: 1,
                  fontSize: '2rem'
                }}>
                  {kpis.responseTime}min
                </Typography>
                <Typography variant="body2" sx={{
                  color: '#6b7280',
                  fontSize: '14px'
                }}>
                  Avg Response Time
                </Typography>
              </Box>

              <Box sx={{ textAlign: 'center', p: 3 }}>
                <Typography variant="h3" sx={{
                  fontWeight: 600,
                  color: '#8b5cf6',
                  mb: 1,
                  fontSize: '2rem'
                }}>
                  {kpis.totalReports}
                </Typography>
                <Typography variant="body2" sx={{
                  color: '#6b7280',
                  fontSize: '14px'
                }}>
                  Total Reports
                </Typography>
              </Box>
            </Box>
          </Box>
        ) : (
          <Box sx={{
            p: 8,
            textAlign: 'center',
            borderRadius: 3,
            border: '1px solid #f1f5f9',
            backgroundColor: '#ffffff'
          }}>
            <Typography variant="h5" sx={{
              color: '#6b7280',
              mb: 2,
              fontWeight: 400
            }}>
              No data available
            </Typography>
            <Typography variant="body1" sx={{ color: '#9ca3af' }}>
              Analytics will appear here once data is collected
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}
