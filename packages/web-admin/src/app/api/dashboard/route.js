import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Global Prisma instance for serverless optimization
const globalForPrisma = globalThis;

const prisma = globalForPrisma.prisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],
});

// Prevent multiple instances in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

export async function GET(request) {
  try {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const last7Days = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    let dashboardData;
    
    try {
      // Fetch real dashboard data from database
      const [
        totalAgents,
        activeAgents,
        todayShifts,
        activeShifts,
        pendingReports,
        recentReports,
        activeSites,
        recentActivity
      ] = await Promise.all([
        // Total agents
        prisma.user.count({
          where: { role: 'AGENT', isActive: true }
        }),
        
        // Active agents (agents with shifts today or currently on duty)
        prisma.user.count({
          where: {
            role: 'AGENT',
            isActive: true,
            agent: {
              isAvailable: true
            }
          }
        }),
        
        // Today's shifts
        prisma.shift.count({
          where: {
            shiftDate: {
              gte: today,
              lt: new Date(today.getTime() + 24 * 60 * 60 * 1000)
            }
          }
        }),
        
        // Active shifts (in progress)
        prisma.shift.count({
          where: {
            status: 'IN_PROGRESS'
          }
        }),
        
        // Pending reports
        prisma.report.count({
          where: {
            status: 'SUBMITTED'
          }
        }),
        
        // Recent reports (last 7 days)
        prisma.report.findMany({
          where: {
            createdAt: {
              gte: last7Days
            }
          },
          include: {
            agent: {
              include: {
                user: true
              }
            },
            shift: {
              include: {
                site: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        }),
        
        // Active sites
        prisma.site.count({
          where: {
            isActive: true
          }
        }),
        
        // Recent activity (shifts and reports)
        prisma.shift.findMany({
          where: {
            createdAt: {
              gte: last7Days
            }
          },
          include: {
            agent: {
              include: {
                user: true
              }
            },
            site: true
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5
        })
      ]);

      // Calculate additional metrics
      const completedShiftsToday = await prisma.shift.count({
        where: {
          shiftDate: {
            gte: today,
            lt: new Date(today.getTime() + 24 * 60 * 60 * 1000)
          },
          status: 'COMPLETED'
        }
      });

      const completionRate = todayShifts > 0 ? (completedShiftsToday / todayShifts) * 100 : 0;

      // Calculate average rating from recent reports
      const reportsWithRating = recentReports.filter(r => r.rating && r.rating > 0);
      const averageRating = reportsWithRating.length > 0 
        ? reportsWithRating.reduce((sum, r) => sum + r.rating, 0) / reportsWithRating.length
        : 0;

      dashboardData = {
        kpis: {
          totalAgents,
          activeAgents,
          todayShifts,
          activeShifts,
          pendingReports,
          activeSites,
          completionRate: Math.round(completionRate * 10) / 10,
          averageRating: Math.round(averageRating * 10) / 10
        },
        recentActivity: recentActivity.map(shift => ({
          id: shift.id,
          type: 'shift',
          title: `Shift at ${shift.site?.name || 'Unknown Site'}`,
          agent: shift.agent?.user?.firstName + ' ' + shift.agent?.user?.lastName,
          time: shift.createdAt,
          status: shift.status
        })),
        recentReports: recentReports.map(report => ({
          id: report.id,
          type: report.type,
          title: report.title,
          agent: report.agent?.user?.firstName + ' ' + report.agent?.user?.lastName,
          site: report.shift?.site?.name,
          time: report.createdAt,
          status: report.status,
          priority: report.priority
        })),
        lastUpdated: new Date().toISOString()
      };

    } catch (dbError) {
      console.error('Database query error:', dbError);
      
      // Return sample data for demonstration when database is not available
      dashboardData = {
        kpis: {
          totalAgents: 25,
          activeAgents: 18,
          todayShifts: 12,
          activeShifts: 8,
          pendingReports: 5,
          activeSites: 15,
          completionRate: 92.5,
          averageRating: 4.6
        },
        recentActivity: [
          {
            id: '1',
            type: 'shift',
            title: 'Shift at Sonatel HQ',
            agent: 'Amadou Ba',
            time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            status: 'IN_PROGRESS'
          },
          {
            id: '2',
            type: 'shift',
            title: 'Shift at CBAO Bank Dakar',
            agent: 'Fatou Sow',
            time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            status: 'COMPLETED'
          }
        ],
        recentReports: [
          {
            id: '1',
            type: 'INCIDENT',
            title: 'Suspicious activity reported',
            agent: 'Moussa Diop',
            site: 'Orange Senegal',
            time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
            status: 'SUBMITTED',
            priority: 'HIGH'
          }
        ],
        lastUpdated: new Date().toISOString(),
        note: 'Sample data - Database connection pending'
      };
    }

    return NextResponse.json(dashboardData);
    
  } catch (error) {
    console.error('Dashboard API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard data', details: error.message },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
