import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Global Prisma instance for serverless optimization
const globalForPrisma = globalThis;

const prisma = globalForPrisma.prisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],
});

// Prevent multiple instances in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let sitesData;
    
    try {
      // Build where clause for filtering
      const where = {
        ...(type && { type }),
        ...(status && { isActive: status === 'ACTIVE' }),
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { address: { contains: search, mode: 'insensitive' } }
          ]
        })
      };

      // Fetch sites with related data
      const sites = await prisma.site.findMany({
        where,
        include: {
          shifts: {
            where: {
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
              }
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 5
          },
          reports: {
            where: {
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
              }
            },
            orderBy: {
              createdAt: 'desc'
            },
            take: 5
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Transform data for frontend
      const transformedSites = sites.map(site => {
        const shifts = site.shifts || [];
        const reports = site.reports || [];
        
        // Calculate active shifts
        const activeShifts = shifts.filter(s => s.status === 'IN_PROGRESS').length;
        
        // Calculate total shifts this month
        const totalShifts = shifts.length;
        
        // Calculate incident count
        const incidents = reports.filter(r => r.type === 'INCIDENT').length;

        return {
          id: site.id,
          name: site.name,
          type: site.type,
          address: site.address,
          coordinates: site.coordinates,
          isActive: site.isActive,
          contactPerson: site.contactPerson,
          contactPhone: site.contactPhone,
          contactEmail: site.contactEmail,
          securityLevel: site.securityLevel,
          accessInstructions: site.accessInstructions,
          emergencyProcedures: site.emergencyProcedures,
          geofenceRadius: site.geofenceRadius,
          qrCode: site.qrCode,
          stats: {
            activeShifts,
            totalShifts,
            incidents,
            lastShift: shifts.length > 0 ? shifts[0].createdAt : null
          },
          recentShifts: shifts.slice(0, 3),
          recentReports: reports.slice(0, 3),
          createdAt: site.createdAt,
          updatedAt: site.updatedAt
        };
      });

      sitesData = {
        sites: transformedSites,
        total: transformedSites.length,
        lastUpdated: new Date().toISOString()
      };

    } catch (dbError) {
      console.error('Database query error:', dbError);
      
      // Return sample data for demonstration when database is not available
      sitesData = {
        sites: [
          {
            id: '1',
            name: 'Sonatel HQ',
            type: 'COMMERCIAL',
            address: 'Plateau, Dakar, Senegal',
            coordinates: {
              latitude: 14.6937,
              longitude: -17.4441
            },
            isActive: true,
            contactPerson: 'Mamadou Diallo',
            contactPhone: '+221 33 839 9000',
            contactEmail: '<EMAIL>',
            securityLevel: 'HIGH',
            accessInstructions: 'Badge required for entry. Report to security desk.',
            emergencyProcedures: 'Contact security immediately at ext. 911',
            geofenceRadius: 100,
            qrCode: 'QR_SONATEL_001',
            stats: {
              activeShifts: 2,
              totalShifts: 45,
              incidents: 1,
              lastShift: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
            },
            recentShifts: [],
            recentReports: [],
            createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            name: 'CBAO Bank Dakar',
            type: 'FINANCIAL',
            address: 'Avenue Léopold Sédar Senghor, Dakar',
            coordinates: {
              latitude: 14.7167,
              longitude: -17.4677
            },
            isActive: true,
            contactPerson: 'Fatou Ndiaye',
            contactPhone: '+221 33 849 5000',
            contactEmail: '<EMAIL>',
            securityLevel: 'CRITICAL',
            accessInstructions: 'Biometric access required. Escort mandatory.',
            emergencyProcedures: 'Silent alarm protocol. Contact police immediately.',
            geofenceRadius: 50,
            qrCode: 'QR_CBAO_002',
            stats: {
              activeShifts: 1,
              totalShifts: 38,
              incidents: 0,
              lastShift: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
            },
            recentShifts: [],
            recentReports: [],
            createdAt: new Date(Date.now() - 150 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '3',
            name: 'Orange Senegal',
            type: 'COMMERCIAL',
            address: 'Rue de Thiong, Dakar',
            coordinates: {
              latitude: 14.6928,
              longitude: -17.4467
            },
            isActive: true,
            contactPerson: 'Ousmane Ba',
            contactPhone: '+221 33 859 5000',
            contactEmail: '<EMAIL>',
            securityLevel: 'MEDIUM',
            accessInstructions: 'Visitor badge required. Check in at reception.',
            emergencyProcedures: 'Evacuate to assembly point. Contact emergency services.',
            geofenceRadius: 75,
            qrCode: 'QR_ORANGE_003',
            stats: {
              activeShifts: 0,
              totalShifts: 32,
              incidents: 2,
              lastShift: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
            },
            recentShifts: [],
            recentReports: [],
            createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '4',
            name: 'Ecobank Senegal',
            type: 'FINANCIAL',
            address: 'Place de l\'Indépendance, Dakar',
            coordinates: {
              latitude: 14.6892,
              longitude: -17.4419
            },
            isActive: true,
            contactPerson: 'Aissatou Sow',
            contactPhone: '+221 33 869 7000',
            contactEmail: '<EMAIL>',
            securityLevel: 'CRITICAL',
            accessInstructions: 'Dual authentication required. Metal detector screening.',
            emergencyProcedures: 'Lockdown protocol. Notify central security.',
            geofenceRadius: 60,
            qrCode: 'QR_ECOBANK_004',
            stats: {
              activeShifts: 1,
              totalShifts: 28,
              incidents: 0,
              lastShift: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
            },
            recentShifts: [],
            recentReports: [],
            createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          }
        ],
        total: 4,
        lastUpdated: new Date().toISOString(),
        note: 'Sample data - Database connection pending'
      };
    }

    return NextResponse.json(sitesData);
    
  } catch (error) {
    console.error('Sites API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch sites data', details: error.message },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
