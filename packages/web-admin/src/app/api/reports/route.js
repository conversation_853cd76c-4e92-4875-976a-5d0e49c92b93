import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Global Prisma instance for serverless optimization
const globalForPrisma = globalThis;

const prisma = globalForPrisma.prisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],
});

// Prevent multiple instances in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const priority = searchParams.get('priority');
    const agentId = searchParams.get('agentId');
    const siteId = searchParams.get('siteId');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    let reportsData;
    
    try {
      // Build where clause for filtering
      const where = {
        ...(type && { type }),
        ...(status && { status }),
        ...(priority && { priority }),
        ...(agentId && { agentId }),
        ...(siteId && { siteId }),
        ...(startDate && endDate && {
          createdAt: {
            gte: new Date(startDate),
            lte: new Date(endDate)
          }
        })
      };

      // Fetch reports with related data
      const reports = await prisma.report.findMany({
        where,
        include: {
          agent: {
            include: {
              user: true
            }
          },
          site: true,
          shift: {
            include: {
              site: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Transform data for frontend
      const transformedReports = reports.map(report => ({
        id: report.id,
        title: report.title,
        description: report.description,
        type: report.type,
        priority: report.priority,
        status: report.status,
        agentId: report.agentId,
        agentName: report.agent ? `${report.agent.user.firstName} ${report.agent.user.lastName}` : 'Unknown',
        agentEmployeeId: report.agent?.employeeId,
        siteId: report.siteId,
        siteName: report.site?.name || 'Unknown Site',
        siteAddress: report.site?.address,
        shiftId: report.shiftId,
        location: report.location,
        photos: report.photos || [],
        videos: report.videos || [],
        documents: report.documents || [],
        rating: report.rating,
        feedback: report.feedback,
        reviewedBy: report.reviewedBy,
        reviewedAt: report.reviewedAt,
        reviewNotes: report.reviewNotes,
        createdAt: report.createdAt,
        updatedAt: report.updatedAt
      }));

      reportsData = {
        reports: transformedReports,
        total: transformedReports.length,
        lastUpdated: new Date().toISOString()
      };

    } catch (dbError) {
      console.error('Database query error:', dbError);
      
      // Return sample data for demonstration when database is not available
      reportsData = {
        reports: [
          {
            id: '1',
            title: 'Suspicious Activity Report',
            description: 'Observed individual attempting to access restricted area without proper authorization. Subject was approached and asked to leave the premises.',
            type: 'INCIDENT',
            priority: 'HIGH',
            status: 'SUBMITTED',
            agentId: '3',
            agentName: 'Moussa Diop',
            agentEmployeeId: 'BH003',
            siteId: '3',
            siteName: 'Orange Senegal',
            siteAddress: 'Rue de Thiong, Dakar',
            shiftId: '1',
            location: {
              latitude: 14.6928,
              longitude: -17.4467,
              address: 'Orange Senegal Main Entrance'
            },
            photos: [
              'https://example.com/photos/incident_001.jpg'
            ],
            videos: [],
            documents: [],
            rating: null,
            feedback: null,
            reviewedBy: null,
            reviewedAt: null,
            reviewNotes: null,
            createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '2',
            title: 'Routine Patrol Report',
            description: 'Completed scheduled patrol of all designated areas. No incidents or anomalies observed. All security systems functioning normally.',
            type: 'PATROL',
            priority: 'NORMAL',
            status: 'APPROVED',
            agentId: '1',
            agentName: 'Amadou Ba',
            agentEmployeeId: 'BH001',
            siteId: '1',
            siteName: 'Sonatel HQ',
            siteAddress: 'Plateau, Dakar, Senegal',
            shiftId: '2',
            location: {
              latitude: 14.6937,
              longitude: -17.4441,
              address: 'Sonatel HQ Perimeter'
            },
            photos: [
              'https://example.com/photos/patrol_001.jpg',
              'https://example.com/photos/patrol_002.jpg'
            ],
            videos: [],
            documents: [],
            rating: 5,
            feedback: 'Excellent patrol coverage and attention to detail.',
            reviewedBy: '<EMAIL>',
            reviewedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
            reviewNotes: 'Thorough documentation and professional conduct.',
            createdAt: new Date(Date.now() - 18 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '3',
            title: 'Equipment Malfunction Report',
            description: 'CCTV camera #3 in the parking area appears to be malfunctioning. Image quality is poor and camera is not responding to remote controls.',
            type: 'MAINTENANCE',
            priority: 'MEDIUM',
            status: 'IN_REVIEW',
            agentId: '2',
            agentName: 'Fatou Sow',
            agentEmployeeId: 'BH002',
            siteId: '2',
            siteName: 'CBAO Bank Dakar',
            siteAddress: 'Avenue Léopold Sédar Senghor, Dakar',
            shiftId: '3',
            location: {
              latitude: 14.7167,
              longitude: -17.4677,
              address: 'CBAO Bank Parking Area'
            },
            photos: [
              'https://example.com/photos/camera_malfunction.jpg'
            ],
            videos: [],
            documents: [
              'https://example.com/docs/maintenance_request.pdf'
            ],
            rating: null,
            feedback: null,
            reviewedBy: null,
            reviewedAt: null,
            reviewNotes: null,
            createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '4',
            title: 'Emergency Response Report',
            description: 'Responded to fire alarm activation in Building B. Conducted evacuation procedures and coordinated with fire department. False alarm caused by kitchen smoke.',
            type: 'EMERGENCY',
            priority: 'CRITICAL',
            status: 'APPROVED',
            agentId: '1',
            agentName: 'Amadou Ba',
            agentEmployeeId: 'BH001',
            siteId: '4',
            siteName: 'Ecobank Senegal',
            siteAddress: 'Place de l\'Indépendance, Dakar',
            shiftId: '4',
            location: {
              latitude: 14.6892,
              longitude: -17.4419,
              address: 'Ecobank Building B'
            },
            photos: [
              'https://example.com/photos/emergency_response.jpg'
            ],
            videos: [
              'https://example.com/videos/evacuation_procedure.mp4'
            ],
            documents: [
              'https://example.com/docs/fire_dept_report.pdf',
              'https://example.com/docs/incident_timeline.pdf'
            ],
            rating: 5,
            feedback: 'Excellent emergency response and coordination.',
            reviewedBy: '<EMAIL>',
            reviewedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            reviewNotes: 'Proper protocols followed. Quick response time.',
            createdAt: new Date(Date.now() - 30 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
          }
        ],
        total: 4,
        lastUpdated: new Date().toISOString(),
        note: 'Sample data - Database connection pending'
      };
    }

    return NextResponse.json(reportsData);
    
  } catch (error) {
    console.error('Reports API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch reports data', details: error.message },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
