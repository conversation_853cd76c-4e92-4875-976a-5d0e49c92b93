// BahinLink Web Admin Shifts Management Page
// ⚠️ CRITICAL: Real shift management with live data ONLY

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Schedule as ScheduleIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  LocationOn
} from '@mui/icons-material';
import ModernSidebar from '../../components/ModernSidebar';

const ShiftsPage = () => {
  const [shifts, setShifts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    agentId: '',
    siteId: '',
    startDate: null,
    endDate: null
  });
  const [agents, setAgents] = useState([]);
  const [sites, setSites] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    scheduled: 0,
    inProgress: 0,
    completed: 0,
    cancelled: 0
  });
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newShift, setNewShift] = useState({
    agentId: '',
    siteId: '',
    shiftDate: new Date(),
    startTime: '',
    endTime: '',
    specialInstructions: ''
  });

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/shifts');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setShifts(data.shifts || []);

      // Calculate stats
      const shiftsData = data.shifts || [];
      const newStats = {
        total: shiftsData.length,
        scheduled: shiftsData.filter(s => s.status === 'SCHEDULED').length,
        inProgress: shiftsData.filter(s => s.status === 'IN_PROGRESS').length,
        completed: shiftsData.filter(s => s.status === 'COMPLETED').length,
        cancelled: shiftsData.filter(s => s.status === 'CANCELLED').length
      };
      setStats(newStats);

    } catch (error) {
      console.error('Error loading shifts data:', error);
      setError('Failed to load shifts data');
      // Set fallback data
      setShifts([]);
      setStats({
        total: 0,
        scheduled: 0,
        inProgress: 0,
        completed: 0,
        cancelled: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateShift = async () => {
    try {
      const shiftData = {
        ...newShift,
        shiftDate: newShift.shiftDate.toISOString().split('T')[0],
        startTime: `${newShift.shiftDate.toISOString().split('T')[0]}T${newShift.startTime}:00`,
        endTime: `${newShift.shiftDate.toISOString().split('T')[0]}T${newShift.endTime}:00`
      };

      const response = await ApiService.post('/shifts', shiftData);
      
      if (response.success) {
        setCreateDialogOpen(false);
        setNewShift({
          agentId: '',
          siteId: '',
          shiftDate: new Date(),
          startTime: '',
          endTime: '',
          specialInstructions: ''
        });
        loadData();
      }
    } catch (error) {
      console.error('Error creating shift:', error);
      setError('Failed to create shift');
    }
  };

  const handleEditShift = async (shiftId, updateData) => {
    try {
      const response = await ApiService.put(`/shifts/${shiftId}`, updateData);
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error updating shift:', error);
      setError('Failed to update shift');
    }
  };

  const handleDeleteShift = async (shiftId) => {
    try {
      const response = await ApiService.delete(`/shifts/${shiftId}`);
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error deleting shift:', error);
      setError('Failed to delete shift');
    }
  };

  const handleStatusChange = async (shiftId, newStatus) => {
    await handleEditShift(shiftId, { status: newStatus });
  };

  const handleExport = async () => {
    try {
      // In a real app, this would generate and download a CSV/Excel file
      console.log('Exporting shifts data...');
      alert('Export functionality would be implemented here');
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const StatCard = ({ title, value, icon: Icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" component="div" color={`${color}.main`}>
              {value}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {title}
            </Typography>
          </Box>
          <Icon sx={{ fontSize: 40, color: `${color}.main`, opacity: 0.7 }} />
        </Box>
      </CardContent>
    </Card>
  );

  const handleExport = () => {
    if (shifts.length > 0) {
      const dataStr = JSON.stringify(shifts, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

      const exportFileDefaultName = `shifts-${new Date().toISOString().split('T')[0]}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    }
  };

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <CircularProgress size={40} sx={{ color: '#6b7280' }} />
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#ffffff' }}>
      <ModernSidebar />

      <Box component="main" sx={{ flexGrow: 1, p: 4 }}>
        {/* Clean Header */}
        <Box sx={{ mb: 6, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 300,
                color: '#1a1a1a',
                mb: 1,
                letterSpacing: '-0.02em',
                fontSize: '2.5rem'
              }}
            >
              Shifts
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#6b7280',
                fontSize: '16px'
              }}
            >
              Manage security shifts and schedules
            </Typography>
          </Box>

          {/* Clean Controls */}
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExport}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Export
            </Button>

            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadData}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Refresh
            </Button>

            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
              sx={{
                backgroundColor: '#3b82f6',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: '#2563eb'
                }
              }}
            >
              Create Shift
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Minimalist KPI Grid */}
        <Box sx={{ mb: 8 }}>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: 4,
              mb: 6
            }}
          >
            {/* Total Shifts */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.total}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Total Shifts
              </Typography>
            </Box>

            {/* Scheduled */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#3b82f6',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.scheduled}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Scheduled
              </Typography>
            </Box>

            {/* In Progress */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#10b981',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.inProgress}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                In Progress
              </Typography>
            </Box>

            {/* Completed */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#8b5cf6',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.completed}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Completed
              </Typography>
            </Box>

            {/* Cancelled */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#ef4444',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.cancelled}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Cancelled
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Clean Shifts Table */}
        <Box sx={{
          borderRadius: 3,
          border: '1px solid #f1f5f9',
          backgroundColor: '#ffffff',
          overflow: 'hidden'
        }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Agent
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Site
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Schedule
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Status
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Duration
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {shifts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography variant="body2" color="textSecondary">
                        No shifts found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  shifts.map((shift) => (
                    <TableRow
                      key={shift.id}
                      sx={{
                        '&:hover': { backgroundColor: '#f9fafb' },
                        borderBottom: '1px solid #f1f5f9'
                      }}
                    >
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500, color: '#1a1a1a' }}>
                            {shift.agentName}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#6b7280' }}>
                            ID: {shift.agentEmployeeId}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                            <LocationOn sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                            {shift.siteName}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#6b7280' }}>
                            {shift.siteAddress}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                            {new Date(shift.shiftDate).toLocaleDateString()}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#6b7280' }}>
                            {new Date(shift.startTime).toLocaleTimeString()} - {new Date(shift.endTime).toLocaleTimeString()}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Chip
                          label={shift.status.replace('_', ' ')}
                          size="small"
                          sx={{
                            backgroundColor:
                              shift.status === 'COMPLETED' ? '#10b981' :
                              shift.status === 'IN_PROGRESS' ? '#f59e0b' :
                              shift.status === 'SCHEDULED' ? '#3b82f6' :
                              shift.status === 'CANCELLED' ? '#ef4444' : '#6b7280',
                            color: 'white',
                            fontSize: '12px'
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                          {shift.totalHours ? `${shift.totalHours.toFixed(1)}h` : 'N/A'}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>
    </Box>
  );
};
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Total Shifts"
              value={stats.total}
              icon={ScheduleIcon}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Scheduled"
              value={stats.scheduled}
              icon={ScheduleIcon}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="In Progress"
              value={stats.inProgress}
              icon={PlayIcon}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Completed"
              value={stats.completed}
              icon={StopIcon}
              color="secondary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Cancelled"
              value={stats.cancelled}
              icon={StopIcon}
              color="error"
            />
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="SCHEDULED">Scheduled</MenuItem>
                  <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                  <MenuItem value="COMPLETED">Completed</MenuItem>
                  <MenuItem value="CANCELLED">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Agent</InputLabel>
                <Select
                  value={filters.agentId}
                  label="Agent"
                  onChange={(e) => setFilters(prev => ({ ...prev, agentId: e.target.value }))}
                >
                  <MenuItem value="">All Agents</MenuItem>
                  {agents.map((agent) => (
                    <MenuItem key={agent.id} value={agent.id}>
                      {agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : 'Unknown'}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Site</InputLabel>
                <Select
                  value={filters.siteId}
                  label="Site"
                  onChange={(e) => setFilters(prev => ({ ...prev, siteId: e.target.value }))}
                >
                  <MenuItem value="">All Sites</MenuItem>
                  {sites.map((site) => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="Start Date"
                value={filters.startDate}
                onChange={(date) => setFilters(prev => ({ ...prev, startDate: date }))}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="End Date"
                value={filters.endDate}
                onChange={(date) => setFilters(prev => ({ ...prev, endDate: date }))}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setFilters({
                  status: '',
                  agentId: '',
                  siteId: '',
                  startDate: null,
                  endDate: null
                })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Shifts Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <ShiftTable
            shifts={shifts}
            loading={loading}
            onEdit={handleEditShift}
            onDelete={handleDeleteShift}
            onStatusChange={handleStatusChange}
            onView={(shiftId) => console.log('View shift:', shiftId)}
          />
        )}

        {/* Create Shift Dialog */}
        <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Create New Shift</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Agent</InputLabel>
                  <Select
                    value={newShift.agentId}
                    label="Agent"
                    onChange={(e) => setNewShift(prev => ({ ...prev, agentId: e.target.value }))}
                  >
                    {agents.map((agent) => (
                      <MenuItem key={agent.id} value={agent.id}>
                        {agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : 'Unknown'}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Site</InputLabel>
                  <Select
                    value={newShift.siteId}
                    label="Site"
                    onChange={(e) => setNewShift(prev => ({ ...prev, siteId: e.target.value }))}
                  >
                    {sites.map((site) => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <DatePicker
                  label="Shift Date"
                  value={newShift.shiftDate}
                  onChange={(date) => setNewShift(prev => ({ ...prev, shiftDate: date }))}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Start Time"
                  type="time"
                  value={newShift.startTime}
                  onChange={(e) => setNewShift(prev => ({ ...prev, startTime: e.target.value }))}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="End Time"
                  type="time"
                  value={newShift.endTime}
                  onChange={(e) => setNewShift(prev => ({ ...prev, endTime: e.target.value }))}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Special Instructions"
                  multiline
                  rows={3}
                  value={newShift.specialInstructions}
                  onChange={(e) => setNewShift(prev => ({ ...prev, specialInstructions: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleCreateShift} variant="contained">Create Shift</Button>
          </DialogActions>
        </Dialog>
        </Box>
        </LocalizationProvider>
      </Box>
    </Box>
  );
};

export default ShiftsPage;
