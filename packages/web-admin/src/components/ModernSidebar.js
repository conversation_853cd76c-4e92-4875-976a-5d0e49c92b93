// BahinLink Modern Sidebar Navigation Component
// ⚠️ CRITICAL: Sophisticated 2024 design with real data integration ONLY

'use client';

import { useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useUser, useClerk } from '@clerk/nextjs';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Avatar,
  IconButton,
  Divider,
  Tooltip,
  Chip,
  useTheme,

  alpha
} from '@mui/material';
import {
  Dashboard,
  People,
  LocationOn,
  Assignment,
  Analytics,
  Settings,
  ExitToApp,
  AccountCircle,
  Shield,
  Assessment,
  ChevronLeft,
  ChevronRight,
  AdminPanelSettings
} from '@mui/icons-material';

const SIDEBAR_WIDTH_EXPANDED = 280;
const SIDEBAR_WIDTH_COLLAPSED = 72;

const ModernSidebar = () => {
  const { user } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();
  const pathname = usePathname();
  const theme = useTheme();
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Modern 2024 color palette - sophisticated neutrals with accent
  const colors = {
    sidebar: '#1a1d29', // Deep charcoal
    sidebarHover: '#252936', // Lighter charcoal
    accent: '#6366f1', // Modern indigo
    accentHover: '#5855eb', // Darker indigo
    text: '#e2e8f0', // Light gray
    textSecondary: '#94a3b8', // Medium gray
    textMuted: '#64748b', // Darker gray
    border: '#334155', // Border gray
    success: '#10b981', // Modern green
    warning: '#f59e0b', // Modern amber
    error: '#ef4444' // Modern red
  };

  const navigationItems = [
    { 
      label: 'Dashboard', 
      icon: Dashboard, 
      path: '/', 
      description: 'Overview & Analytics'
    },
    { 
      label: 'Agents', 
      icon: People, 
      path: '/agents', 
      description: 'Security Personnel'
    },
    { 
      label: 'Sites', 
      icon: LocationOn, 
      path: '/sites', 
      description: 'Client Locations'
    },
    { 
      label: 'Shifts', 
      icon: Assignment, 
      path: '/shifts', 
      description: 'Schedule Management'
    },
    { 
      label: 'Reports', 
      icon: Assessment, 
      path: '/reports', 
      description: 'Security Reports'
    },
    { 
      label: 'Analytics', 
      icon: Analytics, 
      path: '/analytics', 
      description: 'Performance Metrics'
    },
    { 
      label: 'Users', 
      icon: AccountCircle, 
      path: '/users', 
      description: 'User Management'
    }
  ];

  const handleNavigation = (path) => {
    router.push(path);
  };

  const handleSignOut = async () => {
    await signOut();
    router.push('/sign-in');
  };

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const isActive = (path) => {
    return pathname === path;
  };

  const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: sidebarWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: sidebarWidth,
          boxSizing: 'border-box',
          backgroundColor: colors.sidebar,
          borderRight: `1px solid ${colors.border}`,
          transition: theme.transitions.create('width', {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
          overflowX: 'hidden',
          // Modern shadow
          boxShadow: '4px 0 24px rgba(0, 0, 0, 0.12)',
        },
      }}
    >
      {/* Header Section */}
      <Box
        sx={{
          p: 3,
          display: 'flex',
          alignItems: 'center',
          justifyContent: isCollapsed ? 'center' : 'space-between',
          borderBottom: `1px solid ${colors.border}`,
          minHeight: 80
        }}
      >
        {!isCollapsed && (
          <Box display="flex" alignItems="center">
            <Shield 
              sx={{ 
                fontSize: 32, 
                color: colors.accent, 
                mr: 2,
                filter: 'drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))'
              }} 
            />
            <Box>
              <Typography 
                variant="h6" 
                sx={{ 
                  color: colors.text,
                  fontWeight: 700,
                  fontSize: '1.25rem',
                  letterSpacing: '-0.025em'
                }}
              >
                BahinLink
              </Typography>
              <Typography 
                variant="caption" 
                sx={{ 
                  color: colors.textSecondary,
                  fontSize: '0.75rem',
                  fontWeight: 500
                }}
              >
                Security Management
              </Typography>
            </Box>
          </Box>
        )}
        
        {isCollapsed && (
          <Shield 
            sx={{ 
              fontSize: 28, 
              color: colors.accent,
              filter: 'drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))'
            }} 
          />
        )}

        <IconButton
          onClick={toggleSidebar}
          sx={{
            color: colors.textSecondary,
            backgroundColor: alpha(colors.sidebarHover, 0.5),
            width: 32,
            height: 32,
            '&:hover': {
              backgroundColor: alpha(colors.sidebarHover, 0.8),
              color: colors.text
            },
            transition: 'all 0.2s ease-in-out'
          }}
        >
          {isCollapsed ? <ChevronRight /> : <ChevronLeft />}
        </IconButton>
      </Box>

      {/* Live Status Indicator */}
      <Box sx={{ px: 3, py: 2 }}>
        <Chip
          label="LIVE"
          size="small"
          sx={{
            backgroundColor: colors.success,
            color: 'white',
            fontWeight: 600,
            fontSize: '0.75rem',
            height: 24,
            '& .MuiChip-label': {
              px: 1.5
            },
            display: isCollapsed ? 'none' : 'flex'
          }}
        />
      </Box>

      {/* Navigation Items */}
      <Box sx={{ flex: 1, px: 2 }}>
        <List sx={{ py: 0 }}>
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const active = isActive(item.path);
            
            return (
              <ListItem key={item.path} disablePadding sx={{ mb: 0.5 }}>
                <Tooltip 
                  title={isCollapsed ? `${item.label} - ${item.description}` : ''} 
                  placement="right"
                  arrow
                >
                  <ListItemButton
                    onClick={() => handleNavigation(item.path)}
                    sx={{
                      borderRadius: 2,
                      mx: 1,
                      px: 2,
                      py: 1.5,
                      minHeight: 48,
                      backgroundColor: active ? alpha(colors.accent, 0.15) : 'transparent',
                      border: active ? `1px solid ${alpha(colors.accent, 0.3)}` : '1px solid transparent',
                      '&:hover': {
                        backgroundColor: active 
                          ? alpha(colors.accent, 0.2) 
                          : alpha(colors.sidebarHover, 0.8),
                        transform: 'translateX(2px)',
                      },
                      transition: 'all 0.2s ease-in-out',
                      justifyContent: isCollapsed ? 'center' : 'flex-start'
                    }}
                  >
                    <ListItemIcon
                      sx={{
                        color: active ? colors.accent : colors.textSecondary,
                        minWidth: isCollapsed ? 'auto' : 40,
                        mr: isCollapsed ? 0 : 1.5,
                        transition: 'color 0.2s ease-in-out'
                      }}
                    >
                      <Icon sx={{ fontSize: 22 }} />
                    </ListItemIcon>
                    
                    {!isCollapsed && (
                      <ListItemText
                        primary={item.label}
                        secondary={item.description}
                        primaryTypographyProps={{
                          sx: {
                            color: active ? colors.text : colors.textSecondary,
                            fontWeight: active ? 600 : 500,
                            fontSize: '0.875rem',
                            transition: 'color 0.2s ease-in-out'
                          }
                        }}
                        secondaryTypographyProps={{
                          sx: {
                            color: colors.textMuted,
                            fontSize: '0.75rem',
                            mt: 0.25
                          }
                        }}
                      />
                    )}
                  </ListItemButton>
                </Tooltip>
              </ListItem>
            );
          })}
        </List>
      </Box>

      {/* Settings Section */}
      <Box sx={{ px: 2, pb: 1 }}>
        <Divider sx={{ borderColor: colors.border, mb: 2 }} />

        <ListItem disablePadding>
          <Tooltip title={isCollapsed ? 'Settings' : ''} placement="right" arrow>
            <ListItemButton
              onClick={() => handleNavigation('/settings')}
              sx={{
                borderRadius: 2,
                mx: 1,
                px: 2,
                py: 1.5,
                minHeight: 48,
                backgroundColor: pathname === '/settings' ? alpha(colors.accent, 0.15) : 'transparent',
                '&:hover': {
                  backgroundColor: alpha(colors.sidebarHover, 0.8),
                  transform: 'translateX(2px)',
                },
                transition: 'all 0.2s ease-in-out',
                justifyContent: isCollapsed ? 'center' : 'flex-start'
              }}
            >
              <ListItemIcon
                sx={{
                  color: pathname === '/settings' ? colors.accent : colors.textSecondary,
                  minWidth: isCollapsed ? 'auto' : 40,
                  mr: isCollapsed ? 0 : 1.5
                }}
              >
                <Settings sx={{ fontSize: 22 }} />
              </ListItemIcon>

              {!isCollapsed && (
                <ListItemText
                  primary="Settings"
                  primaryTypographyProps={{
                    sx: {
                      color: pathname === '/settings' ? colors.text : colors.textSecondary,
                      fontWeight: pathname === '/settings' ? 600 : 500,
                      fontSize: '0.875rem'
                    }
                  }}
                />
              )}
            </ListItemButton>
          </Tooltip>
        </ListItem>
      </Box>

      {/* User Profile Section */}
      <Box
        sx={{
          px: 2,
          pb: 3,
          borderTop: `1px solid ${colors.border}`,
          pt: 2
        }}
      >
        {!isCollapsed ? (
          // Expanded Profile View
          <Box
            sx={{
              backgroundColor: alpha(colors.sidebarHover, 0.5),
              borderRadius: 3,
              p: 2,
              mx: 1,
              border: `1px solid ${alpha(colors.border, 0.5)}`
            }}
          >
            <Box display="flex" alignItems="center" mb={2}>
              <Avatar
                src={user?.imageUrl}
                alt={user?.fullName}
                sx={{
                  width: 40,
                  height: 40,
                  mr: 2,
                  border: `2px solid ${colors.accent}`,
                  boxShadow: `0 0 0 2px ${alpha(colors.accent, 0.2)}`
                }}
              >
                {user?.firstName?.charAt(0)}
              </Avatar>

              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  sx={{
                    color: colors.text,
                    fontWeight: 600,
                    fontSize: '0.875rem',
                    lineHeight: 1.2,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {user?.firstName} {user?.lastName}
                </Typography>

                <Box display="flex" alignItems="center" mt={0.5}>
                  <AdminPanelSettings
                    sx={{
                      fontSize: 14,
                      color: colors.accent,
                      mr: 0.5
                    }}
                  />
                  <Typography
                    variant="caption"
                    sx={{
                      color: colors.textSecondary,
                      fontSize: '0.75rem',
                      fontWeight: 500
                    }}
                  >
                    Administrator
                  </Typography>
                </Box>

                <Typography
                  variant="caption"
                  sx={{
                    color: colors.textMuted,
                    fontSize: '0.7rem',
                    display: 'block',
                    mt: 0.25,
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {user?.primaryEmailAddress?.emailAddress}
                </Typography>
              </Box>
            </Box>

            <IconButton
              onClick={handleSignOut}
              sx={{
                width: '100%',
                backgroundColor: alpha(colors.error, 0.1),
                color: colors.error,
                borderRadius: 2,
                py: 1,
                '&:hover': {
                  backgroundColor: alpha(colors.error, 0.2),
                  transform: 'translateY(-1px)',
                },
                transition: 'all 0.2s ease-in-out'
              }}
            >
              <ExitToApp sx={{ fontSize: 18, mr: 1 }} />
              <Typography variant="caption" sx={{ fontWeight: 600 }}>
                Sign Out
              </Typography>
            </IconButton>
          </Box>
        ) : (
          // Collapsed Profile View
          <Box display="flex" flexDirection="column" alignItems="center" gap={1}>
            <Tooltip title={`${user?.fullName} - Administrator`} placement="right" arrow>
              <Avatar
                src={user?.imageUrl}
                alt={user?.fullName}
                sx={{
                  width: 36,
                  height: 36,
                  border: `2px solid ${colors.accent}`,
                  boxShadow: `0 0 0 2px ${alpha(colors.accent, 0.2)}`,
                  cursor: 'pointer'
                }}
              >
                {user?.firstName?.charAt(0)}
              </Avatar>
            </Tooltip>

            <Tooltip title="Sign Out" placement="right" arrow>
              <IconButton
                onClick={handleSignOut}
                sx={{
                  width: 36,
                  height: 36,
                  backgroundColor: alpha(colors.error, 0.1),
                  color: colors.error,
                  '&:hover': {
                    backgroundColor: alpha(colors.error, 0.2),
                    transform: 'scale(1.05)',
                  },
                  transition: 'all 0.2s ease-in-out'
                }}
              >
                <ExitToApp sx={{ fontSize: 18 }} />
              </IconButton>
            </Tooltip>
          </Box>
        )}
      </Box>
    </Drawer>
  );
};

export default ModernSidebar;
