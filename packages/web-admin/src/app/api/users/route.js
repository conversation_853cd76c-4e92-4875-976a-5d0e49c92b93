import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Global Prisma instance for serverless optimization
const globalForPrisma = globalThis;

const prisma = globalForPrisma.prisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],
});

// Prevent multiple instances in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let usersData;
    
    try {
      // Build where clause for filtering
      const where = {
        ...(role && { role }),
        ...(status && { isActive: status === 'ACTIVE' }),
        ...(search && {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } }
          ]
        })
      };

      // Fetch users with related data
      const users = await prisma.user.findMany({
        where,
        include: {
          agent: {
            include: {
              shifts: {
                where: {
                  createdAt: {
                    gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
                  }
                },
                take: 5
              }
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Transform data for frontend
      const transformedUsers = users.map(user => ({
        id: user.id,
        clerkId: user.clerkId,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        role: user.role,
        isActive: user.isActive,
        lastLoginAt: user.lastLoginAt,
        agentData: user.agent ? {
          id: user.agent.id,
          employeeId: user.agent.employeeId,
          isAvailable: user.agent.isAvailable,
          certifications: user.agent.certifications || [],
          skills: user.agent.skills || [],
          recentShifts: user.agent.shifts?.length || 0
        } : null,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }));

      usersData = {
        users: transformedUsers,
        total: transformedUsers.length,
        lastUpdated: new Date().toISOString()
      };

    } catch (dbError) {
      console.error('Database query error:', dbError);
      
      // Return sample data for demonstration when database is not available
      usersData = {
        users: [
          {
            id: '1',
            clerkId: 'user_clerk_001',
            firstName: 'Amadou',
            lastName: 'Ba',
            email: '<EMAIL>',
            phone: '+221 77 123 4567',
            role: 'AGENT',
            isActive: true,
            lastLoginAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            agentData: {
              id: 'agent_001',
              employeeId: 'BH001',
              isAvailable: true,
              certifications: ['Security Guard License', 'First Aid'],
              skills: ['Surveillance', 'Emergency Response'],
              recentShifts: 5
            },
            createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            clerkId: 'user_clerk_002',
            firstName: 'Fatou',
            lastName: 'Sow',
            email: '<EMAIL>',
            phone: '+221 77 234 5678',
            role: 'AGENT',
            isActive: true,
            lastLoginAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            agentData: {
              id: 'agent_002',
              employeeId: 'BH002',
              isAvailable: false,
              certifications: ['Security Guard License'],
              skills: ['Patrol', 'Access Control'],
              recentShifts: 3
            },
            createdAt: new Date(Date.now() - 75 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '3',
            clerkId: 'user_clerk_003',
            firstName: 'Moussa',
            lastName: 'Diop',
            email: '<EMAIL>',
            phone: '+221 77 345 6789',
            role: 'AGENT',
            isActive: true,
            lastLoginAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
            agentData: {
              id: 'agent_003',
              employeeId: 'BH003',
              isAvailable: true,
              certifications: ['Security Guard License', 'Fire Safety'],
              skills: ['Investigation', 'Report Writing'],
              recentShifts: 7
            },
            createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '4',
            clerkId: 'user_clerk_004',
            firstName: 'Admin',
            lastName: 'User',
            email: '<EMAIL>',
            phone: '+221 77 456 7890',
            role: 'ADMIN',
            isActive: true,
            lastLoginAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
            agentData: null,
            createdAt: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '5',
            clerkId: 'user_clerk_005',
            firstName: 'Supervisor',
            lastName: 'Manager',
            email: '<EMAIL>',
            phone: '+221 77 567 8901',
            role: 'SUPERVISOR',
            isActive: true,
            lastLoginAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
            agentData: null,
            createdAt: new Date(Date.now() - 150 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          }
        ],
        total: 5,
        lastUpdated: new Date().toISOString(),
        note: 'Sample data - Database connection pending'
      };
    }

    return NextResponse.json(usersData);
    
  } catch (error) {
    console.error('Users API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users data', details: error.message },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
