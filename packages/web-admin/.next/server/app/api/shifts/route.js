"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/shifts/route";
exports.ids = ["app/api/shifts/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fshifts%2Froute&page=%2Fapi%2Fshifts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshifts%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fshifts%2Froute&page=%2Fapi%2Fshifts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshifts%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _workspaces_finalagent_main_final_packages_web_admin_src_app_api_shifts_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/shifts/route.js */ \"(rsc)/./src/app/api/shifts/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/shifts/route\",\n        pathname: \"/api/shifts\",\n        filename: \"route\",\n        bundlePath: \"app/api/shifts/route\"\n    },\n    resolvedPagePath: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/api/shifts/route.js\",\n    nextConfigOutput,\n    userland: _workspaces_finalagent_main_final_packages_web_admin_src_app_api_shifts_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/shifts/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fshifts%2Froute&page=%2Fapi%2Fshifts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshifts%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/shifts/route.js":
/*!*************************************!*\
  !*** ./src/app/api/shifts/route.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Global Prisma instance for serverless optimization\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient({\n    datasources: {\n        db: {\n            url: process.env.DATABASE_URL\n        }\n    },\n    log:  true ? [\n        \"error\"\n    ] : 0\n});\n// Prevent multiple instances in development\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const status = searchParams.get(\"status\");\n        const agentId = searchParams.get(\"agentId\");\n        const siteId = searchParams.get(\"siteId\");\n        const startDate = searchParams.get(\"startDate\");\n        const endDate = searchParams.get(\"endDate\");\n        let shiftsData;\n        try {\n            // Build where clause for filtering\n            const where = {\n                ...status && {\n                    status\n                },\n                ...agentId && {\n                    agentId\n                },\n                ...siteId && {\n                    siteId\n                },\n                ...startDate && endDate && {\n                    shiftDate: {\n                        gte: new Date(startDate),\n                        lte: new Date(endDate)\n                    }\n                }\n            };\n            // Fetch shifts with related data\n            const shifts = await prisma.shift.findMany({\n                where,\n                include: {\n                    agent: {\n                        include: {\n                            user: true\n                        }\n                    },\n                    site: true,\n                    reports: {\n                        orderBy: {\n                            createdAt: \"desc\"\n                        },\n                        take: 3\n                    }\n                },\n                orderBy: {\n                    shiftDate: \"desc\"\n                }\n            });\n            // Transform data for frontend\n            const transformedShifts = shifts.map((shift)=>({\n                    id: shift.id,\n                    agentId: shift.agentId,\n                    agentName: shift.agent ? `${shift.agent.user.firstName} ${shift.agent.user.lastName}` : \"Unknown\",\n                    agentEmployeeId: shift.agent?.employeeId,\n                    siteId: shift.siteId,\n                    siteName: shift.site?.name || \"Unknown Site\",\n                    siteAddress: shift.site?.address,\n                    shiftDate: shift.shiftDate,\n                    startTime: shift.startTime,\n                    endTime: shift.endTime,\n                    status: shift.status,\n                    notes: shift.notes,\n                    checkInTime: shift.checkInTime,\n                    checkOutTime: shift.checkOutTime,\n                    checkInLocation: shift.checkInLocation,\n                    checkOutLocation: shift.checkOutLocation,\n                    totalHours: shift.endTime && shift.startTime ? (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60) : 0,\n                    reportsCount: shift.reports?.length || 0,\n                    createdAt: shift.createdAt,\n                    updatedAt: shift.updatedAt\n                }));\n            shiftsData = {\n                shifts: transformedShifts,\n                total: transformedShifts.length,\n                lastUpdated: new Date().toISOString()\n            };\n        } catch (dbError) {\n            console.error(\"Database query error:\", dbError);\n            // Return sample data for demonstration when database is not available\n            shiftsData = {\n                shifts: [\n                    {\n                        id: \"1\",\n                        agentId: \"1\",\n                        agentName: \"Amadou Ba\",\n                        agentEmployeeId: \"BH001\",\n                        siteId: \"1\",\n                        siteName: \"Sonatel HQ\",\n                        siteAddress: \"Plateau, Dakar\",\n                        shiftDate: new Date().toISOString().split(\"T\")[0],\n                        startTime: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n                        endTime: new Date().toISOString(),\n                        status: \"COMPLETED\",\n                        notes: \"Regular patrol shift completed successfully\",\n                        checkInTime: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),\n                        checkOutTime: new Date().toISOString(),\n                        checkInLocation: {\n                            latitude: 14.6937,\n                            longitude: -17.4441,\n                            address: \"Sonatel HQ, Plateau\"\n                        },\n                        checkOutLocation: {\n                            latitude: 14.6937,\n                            longitude: -17.4441,\n                            address: \"Sonatel HQ, Plateau\"\n                        },\n                        totalHours: 8,\n                        reportsCount: 2,\n                        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date().toISOString()\n                    },\n                    {\n                        id: \"2\",\n                        agentId: \"2\",\n                        agentName: \"Fatou Sow\",\n                        agentEmployeeId: \"BH002\",\n                        siteId: \"2\",\n                        siteName: \"CBAO Bank Dakar\",\n                        siteAddress: \"Avenue L\\xe9opold S\\xe9dar Senghor, Dakar\",\n                        shiftDate: new Date().toISOString().split(\"T\")[0],\n                        startTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                        endTime: new Date(Date.now() + 4 * 60 * 60 * 1000).toISOString(),\n                        status: \"IN_PROGRESS\",\n                        notes: \"Night security shift in progress\",\n                        checkInTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                        checkOutTime: null,\n                        checkInLocation: {\n                            latitude: 14.7167,\n                            longitude: -17.4677,\n                            address: \"CBAO Bank, Plateau\"\n                        },\n                        checkOutLocation: null,\n                        totalHours: 0,\n                        reportsCount: 1,\n                        createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()\n                    },\n                    {\n                        id: \"3\",\n                        agentId: \"3\",\n                        agentName: \"Moussa Diop\",\n                        agentEmployeeId: \"BH003\",\n                        siteId: \"3\",\n                        siteName: \"Orange Senegal\",\n                        siteAddress: \"Rue de Thiong, Dakar\",\n                        shiftDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().split(\"T\")[0],\n                        startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),\n                        endTime: new Date(Date.now() + 32 * 60 * 60 * 1000).toISOString(),\n                        status: \"SCHEDULED\",\n                        notes: \"Morning shift scheduled for tomorrow\",\n                        checkInTime: null,\n                        checkOutTime: null,\n                        checkInLocation: null,\n                        checkOutLocation: null,\n                        totalHours: 8,\n                        reportsCount: 0,\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    },\n                    {\n                        id: \"4\",\n                        agentId: \"1\",\n                        agentName: \"Amadou Ba\",\n                        agentEmployeeId: \"BH001\",\n                        siteId: \"4\",\n                        siteName: \"Ecobank Senegal\",\n                        siteAddress: \"Place de l'Ind\\xe9pendance, Dakar\",\n                        shiftDate: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split(\"T\")[0],\n                        startTime: new Date(Date.now() - 32 * 60 * 60 * 1000).toISOString(),\n                        endTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),\n                        status: \"CANCELLED\",\n                        notes: \"Shift cancelled due to agent illness\",\n                        checkInTime: null,\n                        checkOutTime: null,\n                        checkInLocation: null,\n                        checkOutLocation: null,\n                        totalHours: 0,\n                        reportsCount: 0,\n                        createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()\n                    }\n                ],\n                total: 4,\n                lastUpdated: new Date().toISOString(),\n                note: \"Sample data - Database connection pending\"\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(shiftsData);\n    } catch (error) {\n        console.error(\"Shifts API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch shifts data\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/shifts/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fshifts%2Froute&page=%2Fapi%2Fshifts%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshifts%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();