"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.js":
/*!***********************************!*\
  !*** ./src/app/analytics/page.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/TrendingUp.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Download.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Security.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ModernSidebar */ \"(app-pages-browser)/./src/components/ModernSidebar.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/ApiService */ \"(app-pages-browser)/./src/services/ApiService.js\");\n// BahinLink Web Admin Analytics Dashboard Page\n// ⚠️ CRITICAL: Real analytics with live data visualization ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst AnalyticsPage = ()=>{\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"7d\");\n    const [kpis, setKpis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalShifts: 0,\n        completedShifts: 0,\n        totalHours: 0,\n        averageRating: 0,\n        activeAgents: 0,\n        totalReports: 0,\n        responseTime: 0,\n        efficiency: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAnalyticsData();\n    }, [\n        timeRange\n    ]);\n    const loadAnalyticsData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const [shiftsResponse, reportsResponse, agentsResponse, kpiResponse] = await Promise.all([\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/shifts\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/reports\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/agents\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/kpis\", {\n                    timeRange\n                })\n            ]);\n            // Combine all analytics data\n            const combinedData = {\n                overview: kpiResponse.success ? kpiResponse.data : {},\n                shiftTrends: shiftsResponse.success ? shiftsResponse.data.trends : [],\n                reportTypes: reportsResponse.success ? reportsResponse.data.types : [],\n                agentPerformance: agentsResponse.success ? agentsResponse.data.performance : [],\n                siteActivity: shiftsResponse.success ? shiftsResponse.data.siteActivity : []\n            };\n            setAnalyticsData(combinedData);\n            // Set KPIs\n            if (kpiResponse.success) {\n                setKpis(kpiResponse.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading analytics data:\", error);\n            setError(\"Failed to load analytics data\");\n            // Fallback to sample data for demo\n            setAnalyticsData({\n                overview: {\n                    totalShifts: 156,\n                    activeShifts: 12,\n                    totalAgents: 24,\n                    activeAgents: 18,\n                    totalSites: 8,\n                    activeSites: 6,\n                    totalReports: 89,\n                    pendingReports: 5,\n                    shiftsChange: 12.5,\n                    agentsChange: -2.1,\n                    reportsChange: 8.3\n                },\n                shiftTrends: [\n                    {\n                        date: \"2024-01-01\",\n                        scheduled: 20,\n                        completed: 18,\n                        cancelled: 2\n                    },\n                    {\n                        date: \"2024-01-02\",\n                        scheduled: 22,\n                        completed: 20,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-03\",\n                        scheduled: 18,\n                        completed: 17,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-04\",\n                        scheduled: 25,\n                        completed: 23,\n                        cancelled: 2\n                    },\n                    {\n                        date: \"2024-01-05\",\n                        scheduled: 21,\n                        completed: 19,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-06\",\n                        scheduled: 19,\n                        completed: 18,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-07\",\n                        scheduled: 23,\n                        completed: 21,\n                        cancelled: 2\n                    }\n                ],\n                reportTypes: [\n                    {\n                        name: \"Security\",\n                        value: 35,\n                        color: \"#DC3545\"\n                    },\n                    {\n                        name: \"Maintenance\",\n                        value: 25,\n                        color: \"#FFC107\"\n                    },\n                    {\n                        name: \"Incident\",\n                        value: 20,\n                        color: \"#DC004E\"\n                    },\n                    {\n                        name: \"General\",\n                        value: 15,\n                        color: \"#1976D2\"\n                    },\n                    {\n                        name: \"Safety\",\n                        value: 5,\n                        color: \"#2E7D32\"\n                    }\n                ],\n                agentPerformance: [\n                    {\n                        name: \"John Doe\",\n                        shifts: 15,\n                        onTime: 14,\n                        reports: 8,\n                        rating: 4.8\n                    },\n                    {\n                        name: \"Jane Smith\",\n                        shifts: 12,\n                        onTime: 12,\n                        reports: 6,\n                        rating: 4.9\n                    },\n                    {\n                        name: \"Mike Johnson\",\n                        shifts: 18,\n                        onTime: 16,\n                        reports: 12,\n                        rating: 4.6\n                    },\n                    {\n                        name: \"Sarah Wilson\",\n                        shifts: 14,\n                        onTime: 13,\n                        reports: 9,\n                        rating: 4.7\n                    },\n                    {\n                        name: \"David Brown\",\n                        shifts: 16,\n                        onTime: 15,\n                        reports: 7,\n                        rating: 4.5\n                    }\n                ],\n                siteActivity: [\n                    {\n                        site: \"Downtown Mall\",\n                        shifts: 45,\n                        incidents: 3,\n                        efficiency: 95\n                    },\n                    {\n                        site: \"Office Complex\",\n                        shifts: 38,\n                        incidents: 1,\n                        efficiency: 98\n                    },\n                    {\n                        site: \"Warehouse A\",\n                        shifts: 32,\n                        incidents: 2,\n                        efficiency: 92\n                    },\n                    {\n                        site: \"Retail Center\",\n                        shifts: 28,\n                        incidents: 4,\n                        efficiency: 88\n                    },\n                    {\n                        site: \"Industrial Park\",\n                        shifts: 25,\n                        incidents: 1,\n                        efficiency: 96\n                    }\n                ]\n            });\n            setKpis({\n                totalShifts: 156,\n                completedShifts: 142,\n                totalHours: 1248,\n                averageRating: 4.7,\n                activeAgents: 18,\n                totalReports: 89,\n                responseTime: 12.5,\n                efficiency: 94.2\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleExport = async ()=>{\n        try {\n            // In a real app, this would generate and download analytics report\n            console.log(\"Exporting analytics data...\");\n            alert(\"Export functionality would be implemented here\");\n        } catch (error) {\n            console.error(\"Error exporting analytics:\", error);\n        }\n    };\n    const KPICard = (param)=>{\n        let { title, value, unit = \"\", icon: Icon, color = \"primary\", trend } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            elevation: 2,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: \"\".concat(color, \".main\"),\n                                    children: [\n                                        value,\n                                        unit\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 189,\n                                    columnNumber: 13\n                                }, undefined),\n                                trend !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        mt: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            color: trend >= 0 ? \"success\" : \"error\",\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            variant: \"caption\",\n                                            color: trend >= 0 ? \"success.main\" : \"error.main\",\n                                            sx: {\n                                                ml: 0.5\n                                            },\n                                            children: [\n                                                Math.abs(trend),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: \"\".concat(color, \".main\"),\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 208,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n            lineNumber: 182,\n            columnNumber: 5\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    p: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            mb: 3,\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"h4\",\n                                fontWeight: \"bold\",\n                                children: \"Analytics Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 221,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    gap: 1,\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: \"small\",\n                                        sx: {\n                                            minWidth: 120\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                children: \"Time Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 226,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                value: timeRange,\n                                                label: \"Time Range\",\n                                                onChange: (e)=>setTimeRange(e.target.value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        value: \"1d\",\n                                                        children: \"Last 24 Hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        value: \"7d\",\n                                                        children: \"Last 7 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        value: \"30d\",\n                                                        children: \"Last 30 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 15\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        value: \"90d\",\n                                                        children: \"Last 90 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 15\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 227,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 225,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 241,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        onClick: handleExport,\n                                        children: \"Export\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 239,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 249,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        onClick: loadAnalyticsData,\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 247,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 224,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 220,\n                        columnNumber: 7\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        severity: \"warning\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            error,\n                            \" - Showing sample data for demonstration\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        sx: {\n                            mb: 4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Shifts\",\n                                    value: kpis.totalShifts,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                    color: \"primary\",\n                                    trend: 12.5\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 267,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 266,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Completion Rate\",\n                                    value: (kpis.completedShifts / kpis.totalShifts * 100).toFixed(1),\n                                    unit: \"%\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    color: \"success\",\n                                    trend: 5.2\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 276,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 275,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Hours\",\n                                    value: kpis.totalHours.toLocaleString(),\n                                    unit: \"h\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                    color: \"info\",\n                                    trend: 8.7\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 286,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 285,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Average Rating\",\n                                    value: kpis.averageRating,\n                                    unit: \"/5\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                    color: \"warning\",\n                                    trend: 2.1\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 296,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 295,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 265,\n                        columnNumber: 7\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        sx: {\n                            mb: 4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Active Agents\",\n                                    value: kpis.activeAgents,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                    color: \"secondary\",\n                                    trend: -1.5\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 310,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 309,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Reports\",\n                                    value: kpis.totalReports,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                    color: \"primary\",\n                                    trend: 15.3\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 319,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 318,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Avg Response Time\",\n                                    value: kpis.responseTime,\n                                    unit: \"min\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                    color: \"warning\",\n                                    trend: -8.2\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 328,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 327,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Efficiency\",\n                                    value: kpis.efficiency,\n                                    unit: \"%\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                    color: \"success\",\n                                    trend: 3.4\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 338,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 337,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 308,\n                        columnNumber: 7\n                    }, undefined),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            py: 8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, undefined) : analyticsData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsCharts, {\n                        data: analyticsData,\n                        loading: loading,\n                        timeRange: timeRange,\n                        onTimeRangeChange: setTimeRange\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 355,\n                        columnNumber: 9\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        sx: {\n                            p: 4,\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"h6\",\n                                color: \"textSecondary\",\n                                children: \"No analytics data available\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 363,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"textSecondary\",\n                                sx: {\n                                    mt: 1\n                                },\n                                children: \"Data will appear here once shifts and reports are created\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 362,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnalyticsPage, \"GmQR6AHUiHXyA713mWB6w+LCnB8=\");\n_c = AnalyticsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnalyticsPage);\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.js\n"));

/***/ })

});