// BahinLink Web Admin - Agents Management Page
// ⚠️ CRITICAL: Real agent management with live GPS tracking ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Typography,
  Button,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tooltip
} from '@mui/material';
import {
  Add,
  Edit,
  LocationOn,
  Phone,
  Email,
  Badge,
  TrendingUp,
  Refresh,
  FilterList,
  Download,
  Map
} from '@mui/icons-material';

import ApiService from '../../services/ApiService';
import AgentTable from '../../components/AgentTable';
import RealTimeMap from '../../components/RealTimeMap';
import ModernSidebar from '../../components/ModernSidebar';
import { formatTime, formatDate } from '@bahinlink/shared';

export default function AgentsPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [agents, setAgents] = useState([]);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // Filters and pagination
  const [filters, setFilters] = useState({
    status: '',
    isAvailable: '',
    search: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0
  });

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [mapViewOpen, setMapViewOpen] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    employeeId: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    certifications: [],
    skills: [],
    emergencyContactName: '',
    emergencyContactPhone: ''
  });

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadAgents();
      
      // Auto-refresh every 30 seconds for real-time updates
      const interval = setInterval(loadAgents, 30000);
      return () => clearInterval(interval);
    }
  }, [isLoaded, isSignedIn, filters, pagination.page]);

  const loadAgents = async () => {
    try {
      setError(null);
      
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        includeLocation: 'true',
        includePerformance: 'true',
        ...filters
      };

      const response = await ApiService.get('/agents', params);
      
      if (response.success) {
        setAgents(response.data);
        setFilteredAgents(response.data);
        setPagination(prev => ({
          ...prev,
          total: response.pagination.total
        }));
      } else {
        throw new Error(response.error?.message || 'Failed to load agents');
      }
    } catch (error) {
      console.error('Load agents error:', error);
      setError(error.message || 'Failed to load agents');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAgents();
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCreateAgent = async () => {
    try {
      // First create user, then agent profile
      const userResponse = await ApiService.post('/users', {
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        role: 'AGENT'
      });

      if (userResponse.success) {
        const agentResponse = await ApiService.post('/agents', {
          userId: userResponse.data.id,
          employeeId: formData.employeeId,
          certifications: formData.certifications,
          skills: formData.skills,
          emergencyContactName: formData.emergencyContactName,
          emergencyContactPhone: formData.emergencyContactPhone
        });

        if (agentResponse.success) {
          setCreateDialogOpen(false);
          setFormData({
            employeeId: '',
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            certifications: [],
            skills: [],
            emergencyContactName: '',
            emergencyContactPhone: ''
          });
          await loadAgents();
        }
      }
    } catch (error) {
      console.error('Create agent error:', error);
      setError(error.message || 'Failed to create agent');
    }
  };

  const handleEditAgent = (agent) => {
    setSelectedAgent(agent);
    setFormData({
      employeeId: agent.employeeId,
      firstName: agent.user.firstName,
      lastName: agent.user.lastName,
      email: agent.user.email,
      phone: agent.user.phone,
      certifications: agent.certifications,
      skills: agent.skills,
      emergencyContactName: agent.emergencyContact.name,
      emergencyContactPhone: agent.emergencyContact.phone
    });
    setEditDialogOpen(true);
  };

  const handleUpdateAgent = async () => {
    try {
      // Update user information
      await ApiService.put(`/users/${selectedAgent.user.id}`, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        agentData: {
          certifications: formData.certifications,
          skills: formData.skills
        }
      });

      // Update agent-specific information
      await ApiService.put(`/agents/${selectedAgent.id}`, {
        emergencyContactName: formData.emergencyContactName,
        emergencyContactPhone: formData.emergencyContactPhone
      });

      setEditDialogOpen(false);
      setSelectedAgent(null);
      await loadAgents();
    } catch (error) {
      console.error('Update agent error:', error);
      setError(error.message || 'Failed to update agent');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ON_SHIFT': return 'success';
      case 'AVAILABLE': return 'info';
      case 'OFFLINE': return 'default';
      default: return 'default';
    }
  };

  const getPerformanceColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'error';
  };

  if (!isLoaded || loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <Typography variant="h6">Loading agents...</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      <ModernSidebar />

      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Agent Management
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Map />}
            onClick={() => setMapViewOpen(true)}
          >
            Map View
          </Button>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            disabled={refreshing}
          >
            {refreshing ? 'Refreshing...' : 'Refresh'}
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setCreateDialogOpen(true)}
          >
            Add Agent
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Agents
              </Typography>
              <Typography variant="h4" component="div">
                {agents.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                On Shift
              </Typography>
              <Typography variant="h4" component="div" color="success.main">
                {agents.filter(a => a.status === 'ON_SHIFT').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Available
              </Typography>
              <Typography variant="h4" component="div" color="info.main">
                {agents.filter(a => a.status === 'AVAILABLE').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                With GPS
              </Typography>
              <Typography variant="h4" component="div" color="primary.main">
                {agents.filter(a => a.location).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Search"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Employee ID, name, email..."
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="ON_SHIFT">On Shift</MenuItem>
                  <MenuItem value="AVAILABLE">Available</MenuItem>
                  <MenuItem value="OFFLINE">Offline</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Availability</InputLabel>
                <Select
                  value={filters.isAvailable}
                  onChange={(e) => handleFilterChange('isAvailable', e.target.value)}
                  label="Availability"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="true">Available</MenuItem>
                  <MenuItem value="false">Unavailable</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Download />}
                onClick={() => {/* Export functionality */}}
              >
                Export
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Agents Table */}
      <AgentTable
        agents={filteredAgents}
        onEdit={handleEditAgent}
        onRefresh={loadAgents}
        pagination={pagination}
        onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
      />

      {/* Create Agent Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Agent</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Employee ID"
                value={formData.employeeId}
                onChange={(e) => setFormData(prev => ({ ...prev, employeeId: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Name"
                value={formData.emergencyContactName}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContactName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Emergency Contact Phone"
                value={formData.emergencyContactPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContactPhone: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateAgent} variant="contained">Create Agent</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Agent Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Agent</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Employee ID"
                value={formData.employeeId}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                value={formData.email}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Name"
                value={formData.emergencyContactName}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContactName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Emergency Contact Phone"
                value={formData.emergencyContactPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContactPhone: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleUpdateAgent} variant="contained">Update Agent</Button>
        </DialogActions>
      </Dialog>

      {/* Map View Dialog */}
      <Dialog open={mapViewOpen} onClose={() => setMapViewOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Agent Locations Map</DialogTitle>
        <DialogContent>
          <Box height={600}>
            <RealTimeMap agentLocations={agents.filter(a => a.location).map(agent => ({
              id: agent.id,
              agentName: agent.name,
              employeeId: agent.employeeId,
              latitude: agent.location?.latitude || 14.6928,
              longitude: agent.location?.longitude || -17.4467,
              status: agent.status,
              siteName: agent.currentSite || 'Unknown Site',
              lastUpdate: agent.lastUpdate || new Date().toISOString()
            }))} />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMapViewOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
      </Box>
    </Box>
  );
}
