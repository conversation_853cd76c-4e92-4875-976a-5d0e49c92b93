'use client';

import { Box, Typography, Button, Alert, CircularProgress } from '@mui/material';
import { Download as DownloadIcon, Refresh as RefreshIcon } from '@mui/icons-material';
import ModernSidebar from '../ModernSidebar';

/**
 * Clean, minimalistic page layout component
 * Provides consistent design across all BahinLink admin pages
 */
export default function CleanPageLayout({
  title,
  subtitle,
  children,
  loading = false,
  error = null,
  onRefresh,
  onExport,
  actions = [],
  showControls = true
}) {
  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#ffffff' }}>
      <ModernSidebar />

      <Box component="main" sx={{ flexGrow: 1, p: 4 }}>
        {/* Clean Header */}
        <Box sx={{ mb: 6, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography 
              variant="h3" 
              sx={{ 
                fontWeight: 300, 
                color: '#1a1a1a',
                mb: 1,
                letterSpacing: '-0.02em',
                fontSize: '2.5rem'
              }}
            >
              {title}
            </Typography>
            {subtitle && (
              <Typography 
                variant="body1" 
                sx={{ 
                  color: '#6b7280',
                  fontSize: '16px'
                }}
              >
                {subtitle}
              </Typography>
            )}
          </Box>

          {/* Clean Controls */}
          {showControls && (
            <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
              {onExport && (
                <Button
                  variant="outlined"
                  startIcon={<DownloadIcon />}
                  onClick={onExport}
                  sx={{
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontSize: '14px',
                    textTransform: 'none',
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb'
                    }
                  }}
                >
                  Export
                </Button>
              )}
              
              {onRefresh && (
                <Button
                  variant="outlined"
                  startIcon={<RefreshIcon />}
                  onClick={onRefresh}
                  sx={{
                    borderColor: '#e5e7eb',
                    color: '#6b7280',
                    fontSize: '14px',
                    textTransform: 'none',
                    '&:hover': {
                      borderColor: '#d1d5db',
                      backgroundColor: '#f9fafb'
                    }
                  }}
                >
                  Refresh
                </Button>
              )}

              {/* Custom Actions */}
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'contained'}
                  startIcon={action.icon}
                  onClick={action.onClick}
                  sx={{
                    fontSize: '14px',
                    textTransform: 'none',
                    ...(action.variant === 'outlined' ? {
                      borderColor: '#e5e7eb',
                      color: '#6b7280',
                      '&:hover': {
                        borderColor: '#d1d5db',
                        backgroundColor: '#f9fafb'
                      }
                    } : {})
                  }}
                >
                  {action.label}
                </Button>
              ))}
            </Box>
          )}
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Loading State */}
        {loading ? (
          <Box sx={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center',
            minHeight: '400px'
          }}>
            <CircularProgress size={40} sx={{ color: '#6b7280' }} />
          </Box>
        ) : (
          /* Page Content */
          children
        )}
      </Box>
    </Box>
  );
}
