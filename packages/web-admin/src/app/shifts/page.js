// BahinLink Web Admin Shifts Management Page
// ⚠️ CRITICAL: Real shift management with live data ONLY

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Download as DownloadIcon,
  Schedule as ScheduleIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Group as GroupIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFnsV3';
import ShiftTable from '../../components/ShiftTable';
import ApiService from '../../services/ApiService';

const ShiftsPage = () => {
  const [shifts, setShifts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    agentId: '',
    siteId: '',
    startDate: null,
    endDate: null
  });
  const [agents, setAgents] = useState([]);
  const [sites, setSites] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    scheduled: 0,
    inProgress: 0,
    completed: 0,
    cancelled: 0
  });
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newShift, setNewShift] = useState({
    agentId: '',
    siteId: '',
    shiftDate: new Date(),
    startTime: '',
    endTime: '',
    specialInstructions: ''
  });

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = {};
      if (filters.status) params.status = filters.status;
      if (filters.agentId) params.agentId = filters.agentId;
      if (filters.siteId) params.siteId = filters.siteId;
      if (filters.startDate) params.startDate = filters.startDate.toISOString();
      if (filters.endDate) params.endDate = filters.endDate.toISOString();

      const [shiftsResponse, agentsResponse, sitesResponse] = await Promise.all([
        ApiService.get('/shifts', params),
        ApiService.get('/agents'),
        ApiService.get('/sites')
      ]);

      if (shiftsResponse.success) {
        setShifts(shiftsResponse.data);
        
        // Calculate stats
        const newStats = {
          total: shiftsResponse.data.length,
          scheduled: shiftsResponse.data.filter(s => s.status === 'SCHEDULED').length,
          inProgress: shiftsResponse.data.filter(s => s.status === 'IN_PROGRESS').length,
          completed: shiftsResponse.data.filter(s => s.status === 'COMPLETED').length,
          cancelled: shiftsResponse.data.filter(s => s.status === 'CANCELLED').length
        };
        setStats(newStats);
      }

      if (agentsResponse.success) {
        setAgents(agentsResponse.data);
      }

      if (sitesResponse.success) {
        setSites(sitesResponse.data);
      }
    } catch (error) {
      console.error('Error loading shifts data:', error);
      setError('Failed to load shifts data');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateShift = async () => {
    try {
      const shiftData = {
        ...newShift,
        shiftDate: newShift.shiftDate.toISOString().split('T')[0],
        startTime: `${newShift.shiftDate.toISOString().split('T')[0]}T${newShift.startTime}:00`,
        endTime: `${newShift.shiftDate.toISOString().split('T')[0]}T${newShift.endTime}:00`
      };

      const response = await ApiService.post('/shifts', shiftData);
      
      if (response.success) {
        setCreateDialogOpen(false);
        setNewShift({
          agentId: '',
          siteId: '',
          shiftDate: new Date(),
          startTime: '',
          endTime: '',
          specialInstructions: ''
        });
        loadData();
      }
    } catch (error) {
      console.error('Error creating shift:', error);
      setError('Failed to create shift');
    }
  };

  const handleEditShift = async (shiftId, updateData) => {
    try {
      const response = await ApiService.put(`/shifts/${shiftId}`, updateData);
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error updating shift:', error);
      setError('Failed to update shift');
    }
  };

  const handleDeleteShift = async (shiftId) => {
    try {
      const response = await ApiService.delete(`/shifts/${shiftId}`);
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error deleting shift:', error);
      setError('Failed to delete shift');
    }
  };

  const handleStatusChange = async (shiftId, newStatus) => {
    await handleEditShift(shiftId, { status: newStatus });
  };

  const handleExport = async () => {
    try {
      // In a real app, this would generate and download a CSV/Excel file
      console.log('Exporting shifts data...');
      alert('Export functionality would be implemented here');
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const StatCard = ({ title, value, icon: Icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" component="div" color={`${color}.main`}>
              {value}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {title}
            </Typography>
          </Box>
          <Icon sx={{ fontSize: 40, color: `${color}.main`, opacity: 0.7 }} />
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h4" fontWeight="bold">
            Shift Management
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExport}
            >
              Export
            </Button>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadData}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
            >
              Create Shift
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Stats Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Total Shifts"
              value={stats.total}
              icon={ScheduleIcon}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Scheduled"
              value={stats.scheduled}
              icon={ScheduleIcon}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="In Progress"
              value={stats.inProgress}
              icon={PlayIcon}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Completed"
              value={stats.completed}
              icon={StopIcon}
              color="secondary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={2.4}>
            <StatCard
              title="Cancelled"
              value={stats.cancelled}
              icon={StopIcon}
              color="error"
            />
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="SCHEDULED">Scheduled</MenuItem>
                  <MenuItem value="IN_PROGRESS">In Progress</MenuItem>
                  <MenuItem value="COMPLETED">Completed</MenuItem>
                  <MenuItem value="CANCELLED">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Agent</InputLabel>
                <Select
                  value={filters.agentId}
                  label="Agent"
                  onChange={(e) => setFilters(prev => ({ ...prev, agentId: e.target.value }))}
                >
                  <MenuItem value="">All Agents</MenuItem>
                  {agents.map((agent) => (
                    <MenuItem key={agent.id} value={agent.id}>
                      {agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : 'Unknown'}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Site</InputLabel>
                <Select
                  value={filters.siteId}
                  label="Site"
                  onChange={(e) => setFilters(prev => ({ ...prev, siteId: e.target.value }))}
                >
                  <MenuItem value="">All Sites</MenuItem>
                  {sites.map((site) => (
                    <MenuItem key={site.id} value={site.id}>
                      {site.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="Start Date"
                value={filters.startDate}
                onChange={(date) => setFilters(prev => ({ ...prev, startDate: date }))}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="End Date"
                value={filters.endDate}
                onChange={(date) => setFilters(prev => ({ ...prev, endDate: date }))}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setFilters({
                  status: '',
                  agentId: '',
                  siteId: '',
                  startDate: null,
                  endDate: null
                })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Shifts Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <ShiftTable
            shifts={shifts}
            loading={loading}
            onEdit={handleEditShift}
            onDelete={handleDeleteShift}
            onStatusChange={handleStatusChange}
            onView={(shiftId) => console.log('View shift:', shiftId)}
          />
        )}

        {/* Create Shift Dialog */}
        <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
          <DialogTitle>Create New Shift</DialogTitle>
          <DialogContent>
            <Grid container spacing={2} sx={{ mt: 1 }}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Agent</InputLabel>
                  <Select
                    value={newShift.agentId}
                    label="Agent"
                    onChange={(e) => setNewShift(prev => ({ ...prev, agentId: e.target.value }))}
                  >
                    {agents.map((agent) => (
                      <MenuItem key={agent.id} value={agent.id}>
                        {agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : 'Unknown'}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>Site</InputLabel>
                  <Select
                    value={newShift.siteId}
                    label="Site"
                    onChange={(e) => setNewShift(prev => ({ ...prev, siteId: e.target.value }))}
                  >
                    {sites.map((site) => (
                      <MenuItem key={site.id} value={site.id}>
                        {site.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <DatePicker
                  label="Shift Date"
                  value={newShift.shiftDate}
                  onChange={(date) => setNewShift(prev => ({ ...prev, shiftDate: date }))}
                  slotProps={{ textField: { fullWidth: true } }}
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="Start Time"
                  type="time"
                  value={newShift.startTime}
                  onChange={(e) => setNewShift(prev => ({ ...prev, startTime: e.target.value }))}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  label="End Time"
                  type="time"
                  value={newShift.endTime}
                  onChange={(e) => setNewShift(prev => ({ ...prev, endTime: e.target.value }))}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Special Instructions"
                  multiline
                  rows={3}
                  value={newShift.specialInstructions}
                  onChange={(e) => setNewShift(prev => ({ ...prev, specialInstructions: e.target.value }))}
                />
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleCreateShift} variant="contained">Create Shift</Button>
          </DialogActions>
        </Dialog>
      </Box>
    </LocalizationProvider>
  );
};

export default ShiftsPage;
