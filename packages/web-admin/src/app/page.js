// BahinLink Admin Dashboard
// ⚠️ CRITICAL: Real-time monitoring with production data ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Button
} from '@mui/material';
import {
  LocationOn,
  Schedule,
  Assignment,
  People,
  Refresh
} from '@mui/icons-material';
import ModernSidebar from '../components/ModernSidebar';

export default function Dashboard() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadDashboardData();
      
      // Auto-refresh every 30 seconds for real-time data
      const interval = setInterval(loadDashboardData, 30000);
      return () => clearInterval(interval);
    }
  }, [isLoaded, isSignedIn]);

  const loadDashboardData = async () => {
    try {
      setError(null);

      // Get real dashboard data
      const response = await fetch('/api/dashboard');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setDashboardData(data);
    } catch (error) {
      console.error('Dashboard data error:', error);
      setError(error.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
  };

  if (!isLoaded || loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ ml: 2 }}>
          Loading BahinLink Dashboard...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error" action={
          <Button color="inherit" size="small" onClick={handleRefresh}>
            Retry
          </Button>
        }>
          {error}
        </Alert>
      </Box>
    );
  }

  // Get KPIs from dashboard data or use defaults
  const kpis = dashboardData?.kpis || {
    totalAgents: 0,
    activeAgents: 0,
    todayShifts: 0,
    activeShifts: 0,
    pendingReports: 0,
    activeSites: 0,
    completionRate: 0,
    averageRating: 0
  };

  const handleExport = () => {
    if (dashboardData) {
      const dataStr = JSON.stringify(dashboardData, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

      const exportFileDefaultName = `dashboard-${new Date().toISOString().split('T')[0]}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    }
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#ffffff' }}>
      <ModernSidebar />

      <Box component="main" sx={{ flexGrow: 1, p: 4 }}>
        {/* Clean Header */}
        <Box sx={{ mb: 6, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 300,
                color: '#1a1a1a',
                mb: 1,
                letterSpacing: '-0.02em',
                fontSize: '2.5rem'
              }}
            >
              Dashboard
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#6b7280',
                fontSize: '16px'
              }}
            >
              Real-time monitoring for Bahin SARL security operations
            </Typography>
          </Box>

          {/* Clean Controls */}
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={handleRefresh}
              disabled={refreshing}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Minimalist KPI Grid */}
        <Box sx={{ mb: 8 }}>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(240px, 1fr))',
              gap: 4,
              mb: 6
            }}
          >
            {/* Total Agents */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {kpis.totalAgents}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Total Agents
              </Typography>
            </Box>

            {/* Active Agents */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {kpis.activeAgents}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Active Agents
              </Typography>
            </Box>

            {/* Active Shifts */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {kpis.activeShifts}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Active Shifts
              </Typography>
            </Box>

            {/* Pending Reports */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {kpis.pendingReports}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Pending Reports
              </Typography>
            </Box>
          </Box>
        </Box>
        {/* Recent Activity Section */}
        {loading ? (
          <Box sx={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '300px'
          }}>
            <CircularProgress size={40} sx={{ color: '#6b7280' }} />
          </Box>
        ) : dashboardData ? (
          <Box sx={{
            p: 6,
            borderRadius: 3,
            border: '1px solid #f1f5f9',
            backgroundColor: '#ffffff'
          }}>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 500,
                color: '#1a1a1a',
                mb: 4,
                fontSize: '1.75rem'
              }}
            >
              Recent Activity
            </Typography>

            {dashboardData.recentActivity && dashboardData.recentActivity.length > 0 ? (
              <List>
                {dashboardData.recentActivity.map((activity, index) => (
                  <ListItem key={index} sx={{ px: 0, py: 2 }}>
                    <ListItemIcon>
                      {activity.type === 'shift' ? <Schedule /> : <Assignment />}
                    </ListItemIcon>
                    <ListItemText
                      primary={activity.title}
                      secondary={`${activity.agent} • ${new Date(activity.time).toLocaleString()}`}
                      sx={{
                        '& .MuiListItemText-primary': {
                          fontSize: '16px',
                          fontWeight: 500,
                          color: '#1a1a1a'
                        },
                        '& .MuiListItemText-secondary': {
                          fontSize: '14px',
                          color: '#6b7280'
                        }
                      }}
                    />
                  </ListItem>
                ))}
              </List>
            ) : (
              <Typography variant="body2" sx={{ color: '#6b7280', textAlign: 'center', py: 4 }}>
                No recent activity
              </Typography>
            )}
          </Box>
        ) : (
          <Box sx={{
            p: 8,
            textAlign: 'center',
            borderRadius: 3,
            border: '1px solid #f1f5f9',
            backgroundColor: '#ffffff'
          }}>
            <Typography variant="h5" sx={{
              color: '#6b7280',
              mb: 2,
              fontWeight: 400
            }}>
              No data available
            </Typography>
            <Typography variant="body1" sx={{ color: '#9ca3af' }}>
              Dashboard data will appear here once the system is active
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}
