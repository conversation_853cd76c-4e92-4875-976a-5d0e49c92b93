/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/shifts/page";
exports.ids = ["app/shifts/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshifts%2Fpage&page=%2Fshifts%2Fpage&appPaths=%2Fshifts%2Fpage&pagePath=private-next-app-dir%2Fshifts%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshifts%2Fpage&page=%2Fshifts%2Fpage&appPaths=%2Fshifts%2Fpage&pagePath=private-next-app-dir%2Fshifts%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1c3a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'shifts',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/shifts/page.js */ \"(rsc)/./src/app/shifts/page.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/shifts/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/shifts/page\",\n        pathname: \"/shifts\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshifts%2Fpage&page=%2Fshifts%2Fpage&appPaths=%2Fshifts%2Fpage&pagePath=private-next-app-dir%2Fshifts%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fshifts%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fshifts%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/shifts/page.js */ \"(ssr)/./src/app/shifts/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGcGFja2FnZXMlMkZ3ZWItYWRtaW4lMkZzcmMlMkZhcHAlMkZzaGlmdHMlMkZwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBZ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYmFoaW5saW5rL3dlYi1hZG1pbi8/MWU2MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi93b3Jrc3BhY2VzL2ZpbmFsYWdlbnQtbWFpbi1maW5hbC9wYWNrYWdlcy93ZWItYWRtaW4vc3JjL2FwcC9zaGlmdHMvcGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fshifts%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/shifts/page.js":
/*!********************************!*\
  !*** ./src/app/shifts/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Download.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/PlayArrow.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Stop.js\");\n/* harmony import */ var _mui_x_date_pickers_DatePicker__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @mui/x-date-pickers/DatePicker */ \"(ssr)/../../node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/x-date-pickers/LocalizationProvider */ \"(ssr)/../../node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers_AdapterDateFnsV3__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDateFnsV3 */ \"(ssr)/../../node_modules/@mui/x-date-pickers/AdapterDateFnsV3/AdapterDateFnsV3.js\");\n/* harmony import */ var _components_ShiftTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ShiftTable */ \"(ssr)/./src/components/ShiftTable.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ModernSidebar */ \"(ssr)/./src/components/ModernSidebar.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/ApiService */ \"(ssr)/./src/services/ApiService.js\");\n// BahinLink Web Admin Shifts Management Page\n// ⚠️ CRITICAL: Real shift management with live data ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst ShiftsPage = ()=>{\n    const [shifts, setShifts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        status: \"\",\n        agentId: \"\",\n        siteId: \"\",\n        startDate: null,\n        endDate: null\n    });\n    const [agents, setAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        scheduled: 0,\n        inProgress: 0,\n        completed: 0,\n        cancelled: 0\n    });\n    const [createDialogOpen, setCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newShift, setNewShift] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        agentId: \"\",\n        siteId: \"\",\n        shiftDate: new Date(),\n        startTime: \"\",\n        endTime: \"\",\n        specialInstructions: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        filters\n    ]);\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Build query parameters\n            const params = {};\n            if (filters.status) params.status = filters.status;\n            if (filters.agentId) params.agentId = filters.agentId;\n            if (filters.siteId) params.siteId = filters.siteId;\n            if (filters.startDate) params.startDate = filters.startDate.toISOString();\n            if (filters.endDate) params.endDate = filters.endDate.toISOString();\n            const [shiftsResponse, agentsResponse, sitesResponse] = await Promise.all([\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/shifts\", params),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/agents\"),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/sites\")\n            ]);\n            if (shiftsResponse.success) {\n                setShifts(shiftsResponse.data);\n                // Calculate stats\n                const newStats = {\n                    total: shiftsResponse.data.length,\n                    scheduled: shiftsResponse.data.filter((s)=>s.status === \"SCHEDULED\").length,\n                    inProgress: shiftsResponse.data.filter((s)=>s.status === \"IN_PROGRESS\").length,\n                    completed: shiftsResponse.data.filter((s)=>s.status === \"COMPLETED\").length,\n                    cancelled: shiftsResponse.data.filter((s)=>s.status === \"CANCELLED\").length\n                };\n                setStats(newStats);\n            }\n            if (agentsResponse.success) {\n                setAgents(agentsResponse.data);\n            }\n            if (sitesResponse.success) {\n                setSites(sitesResponse.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading shifts data:\", error);\n            setError(\"Failed to load shifts data\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateShift = async ()=>{\n        try {\n            const shiftData = {\n                ...newShift,\n                shiftDate: newShift.shiftDate.toISOString().split(\"T\")[0],\n                startTime: `${newShift.shiftDate.toISOString().split(\"T\")[0]}T${newShift.startTime}:00`,\n                endTime: `${newShift.shiftDate.toISOString().split(\"T\")[0]}T${newShift.endTime}:00`\n            };\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"/shifts\", shiftData);\n            if (response.success) {\n                setCreateDialogOpen(false);\n                setNewShift({\n                    agentId: \"\",\n                    siteId: \"\",\n                    shiftDate: new Date(),\n                    startTime: \"\",\n                    endTime: \"\",\n                    specialInstructions: \"\"\n                });\n                loadData();\n            }\n        } catch (error) {\n            console.error(\"Error creating shift:\", error);\n            setError(\"Failed to create shift\");\n        }\n    };\n    const handleEditShift = async (shiftId, updateData)=>{\n        try {\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].put(`/shifts/${shiftId}`, updateData);\n            if (response.success) {\n                loadData();\n            }\n        } catch (error) {\n            console.error(\"Error updating shift:\", error);\n            setError(\"Failed to update shift\");\n        }\n    };\n    const handleDeleteShift = async (shiftId)=>{\n        try {\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].delete(`/shifts/${shiftId}`);\n            if (response.success) {\n                loadData();\n            }\n        } catch (error) {\n            console.error(\"Error deleting shift:\", error);\n            setError(\"Failed to delete shift\");\n        }\n    };\n    const handleStatusChange = async (shiftId, newStatus)=>{\n        await handleEditShift(shiftId, {\n            status: newStatus\n        });\n    };\n    const handleExport = async ()=>{\n        try {\n            // In a real app, this would generate and download a CSV/Excel file\n            console.log(\"Exporting shifts data...\");\n            alert(\"Export functionality would be implemented here\");\n        } catch (error) {\n            console.error(\"Error exporting data:\", error);\n        }\n    };\n    const StatCard = ({ title, value, icon: Icon, color = \"primary\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: `${color}.main`,\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 203,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 199,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: `${color}.main`,\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 198,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n            lineNumber: 196,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_9__.LocalizationProvider, {\n                    dateAdapter: _mui_x_date_pickers_AdapterDateFnsV3__WEBPACK_IMPORTED_MODULE_10__.AdapterDateFns,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        sx: {\n                            p: 3\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                sx: {\n                                    mb: 3,\n                                    display: \"flex\",\n                                    justifyContent: \"space-between\",\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        variant: \"h4\",\n                                        fontWeight: \"bold\",\n                                        children: \"Shift Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 222,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        sx: {\n                                            display: \"flex\",\n                                            gap: 1\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 26\n                                                }, void 0),\n                                                onClick: handleExport,\n                                                children: \"Export\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 226,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"outlined\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 26\n                                                }, void 0),\n                                                onClick: loadData,\n                                                children: \"Refresh\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 233,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"contained\",\n                                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 26\n                                                }, void 0),\n                                                onClick: ()=>setCreateDialogOpen(true),\n                                                children: \"Create Shift\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 240,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 225,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 221,\n                                columnNumber: 9\n                            }, undefined),\n                            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                severity: \"error\",\n                                sx: {\n                                    mb: 2\n                                },\n                                onClose: ()=>setError(null),\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                container: true,\n                                spacing: 3,\n                                sx: {\n                                    mb: 3\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 2.4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                            title: \"Total Shifts\",\n                                            value: stats.total,\n                                            icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                            color: \"primary\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 260,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 259,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 2.4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                            title: \"Scheduled\",\n                                            value: stats.scheduled,\n                                            icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                            color: \"info\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 268,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 267,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 2.4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                            title: \"In Progress\",\n                                            value: stats.inProgress,\n                                            icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                            color: \"success\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 276,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 275,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 2.4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                            title: \"Completed\",\n                                            value: stats.completed,\n                                            icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                            color: \"secondary\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 283,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        md: 2.4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                            title: \"Cancelled\",\n                                            value: stats.cancelled,\n                                            icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                                            color: \"error\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 292,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 291,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 258,\n                                columnNumber: 9\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                sx: {\n                                    p: 2,\n                                    mb: 3\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    container: true,\n                                    spacing: 2,\n                                    alignItems: \"center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            md: 2,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                fullWidth: true,\n                                                size: \"small\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 306,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: filters.status,\n                                                        label: \"Status\",\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    status: e.target.value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                value: \"\",\n                                                                children: \"All\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                value: \"SCHEDULED\",\n                                                                children: \"Scheduled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                value: \"IN_PROGRESS\",\n                                                                children: \"In Progress\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                value: \"COMPLETED\",\n                                                                children: \"Completed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                value: \"CANCELLED\",\n                                                                children: \"Cancelled\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 305,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 304,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            md: 2,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                fullWidth: true,\n                                                size: \"small\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: \"Agent\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: filters.agentId,\n                                                        label: \"Agent\",\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    agentId: e.target.value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                value: \"\",\n                                                                children: \"All Agents\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    value: agent.id,\n                                                                    children: agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : \"Unknown\"\n                                                                }, agent.id, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                    lineNumber: 331,\n                                                                    columnNumber: 21\n                                                                }, undefined))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 321,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            md: 2,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                fullWidth: true,\n                                                size: \"small\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        children: \"Site\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 17\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: filters.siteId,\n                                                        label: \"Site\",\n                                                        onChange: (e)=>setFilters((prev)=>({\n                                                                    ...prev,\n                                                                    siteId: e.target.value\n                                                                })),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                value: \"\",\n                                                                children: \"All Sites\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 347,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    value: site.id,\n                                                                    children: site.name\n                                                                }, site.id, false, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 21\n                                                                }, undefined))\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 340,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 339,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            md: 2,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_DatePicker__WEBPACK_IMPORTED_MODULE_25__.DatePicker, {\n                                                label: \"Start Date\",\n                                                value: filters.startDate,\n                                                onChange: (date)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            startDate: date\n                                                        })),\n                                                slotProps: {\n                                                    textField: {\n                                                        size: \"small\",\n                                                        fullWidth: true\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 358,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 357,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            md: 2,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_DatePicker__WEBPACK_IMPORTED_MODULE_25__.DatePicker, {\n                                                label: \"End Date\",\n                                                value: filters.endDate,\n                                                onChange: (date)=>setFilters((prev)=>({\n                                                            ...prev,\n                                                            endDate: date\n                                                        })),\n                                                slotProps: {\n                                                    textField: {\n                                                        size: \"small\",\n                                                        fullWidth: true\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 367,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 366,\n                                            columnNumber: 13\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            item: true,\n                                            xs: 12,\n                                            sm: 6,\n                                            md: 2,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                variant: \"outlined\",\n                                                fullWidth: true,\n                                                onClick: ()=>setFilters({\n                                                        status: \"\",\n                                                        agentId: \"\",\n                                                        siteId: \"\",\n                                                        startDate: null,\n                                                        endDate: null\n                                                    }),\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 376,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 375,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 303,\n                                    columnNumber: 11\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 302,\n                                columnNumber: 9\n                            }, undefined),\n                            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    justifyContent: \"center\",\n                                    py: 4\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 396,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShiftTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                shifts: shifts,\n                                loading: loading,\n                                onEdit: handleEditShift,\n                                onDelete: handleDeleteShift,\n                                onStatusChange: handleStatusChange,\n                                onView: (shiftId)=>console.log(\"View shift:\", shiftId)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                open: createDialogOpen,\n                                onClose: ()=>setCreateDialogOpen(false),\n                                maxWidth: \"md\",\n                                fullWidth: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        children: \"Create New Shift\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 411,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            container: true,\n                                            spacing: 2,\n                                            sx: {\n                                                mt: 1\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    item: true,\n                                                    xs: 12,\n                                                    sm: 6,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        fullWidth: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                children: \"Agent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: newShift.agentId,\n                                                                label: \"Agent\",\n                                                                onChange: (e)=>setNewShift((prev)=>({\n                                                                            ...prev,\n                                                                            agentId: e.target.value\n                                                                        })),\n                                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        value: agent.id,\n                                                                        children: agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : \"Unknown\"\n                                                                    }, agent.id, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 23\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 414,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    item: true,\n                                                    xs: 12,\n                                                    sm: 6,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        fullWidth: true,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                children: \"Site\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 19\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                value: newShift.siteId,\n                                                                label: \"Site\",\n                                                                onChange: (e)=>setNewShift((prev)=>({\n                                                                            ...prev,\n                                                                            siteId: e.target.value\n                                                                        })),\n                                                                children: sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        value: site.id,\n                                                                        children: site.name\n                                                                    }, site.id, false, {\n                                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                        lineNumber: 440,\n                                                                        columnNumber: 23\n                                                                    }, undefined))\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                                lineNumber: 434,\n                                                                columnNumber: 19\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 432,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    item: true,\n                                                    xs: 12,\n                                                    sm: 4,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_DatePicker__WEBPACK_IMPORTED_MODULE_25__.DatePicker, {\n                                                        label: \"Shift Date\",\n                                                        value: newShift.shiftDate,\n                                                        onChange: (date)=>setNewShift((prev)=>({\n                                                                    ...prev,\n                                                                    shiftDate: date\n                                                                })),\n                                                        slotProps: {\n                                                            textField: {\n                                                                fullWidth: true\n                                                            }\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    item: true,\n                                                    xs: 12,\n                                                    sm: 4,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        fullWidth: true,\n                                                        label: \"Start Time\",\n                                                        type: \"time\",\n                                                        value: newShift.startTime,\n                                                        onChange: (e)=>setNewShift((prev)=>({\n                                                                    ...prev,\n                                                                    startTime: e.target.value\n                                                                })),\n                                                        InputLabelProps: {\n                                                            shrink: true\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    item: true,\n                                                    xs: 12,\n                                                    sm: 4,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        fullWidth: true,\n                                                        label: \"End Time\",\n                                                        type: \"time\",\n                                                        value: newShift.endTime,\n                                                        onChange: (e)=>setNewShift((prev)=>({\n                                                                    ...prev,\n                                                                    endTime: e.target.value\n                                                                })),\n                                                        InputLabelProps: {\n                                                            shrink: true\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 15\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    item: true,\n                                                    xs: 12,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                        fullWidth: true,\n                                                        label: \"Special Instructions\",\n                                                        multiline: true,\n                                                        rows: 3,\n                                                        value: newShift.specialInstructions,\n                                                        onChange: (e)=>setNewShift((prev)=>({\n                                                                    ...prev,\n                                                                    specialInstructions: e.target.value\n                                                                }))\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 17\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 15\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 413,\n                                            columnNumber: 13\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 412,\n                                        columnNumber: 11\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onClick: ()=>setCreateDialogOpen(false),\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 492,\n                                                columnNumber: 13\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                onClick: handleCreateShift,\n                                                variant: \"contained\",\n                                                children: \"Create Shift\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                lineNumber: 493,\n                                                columnNumber: 13\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 491,\n                                        columnNumber: 11\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 410,\n                                columnNumber: 9\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n        lineNumber: 214,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShiftsPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/shifts/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ModernSidebar.js":
/*!*****************************************!*\
  !*** ./src/components/ModernSidebar.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/system/esm/colorManipulator.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemButton/ListItemButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assessment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Analytics.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Shield.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ChevronLeft.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ExitToApp.js\");\n// BahinLink Modern Sidebar Navigation Component\n// ⚠️ CRITICAL: Sophisticated 2024 design with real data integration ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst SIDEBAR_WIDTH_EXPANDED = 280;\nconst SIDEBAR_WIDTH_COLLAPSED = 72;\nconst ModernSidebar = ()=>{\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const { signOut } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modern 2024 color palette - sophisticated neutrals with accent\n    const colors = {\n        sidebar: \"#1a1d29\",\n        sidebarHover: \"#252936\",\n        accent: \"#6366f1\",\n        accentHover: \"#5855eb\",\n        text: \"#e2e8f0\",\n        textSecondary: \"#94a3b8\",\n        textMuted: \"#64748b\",\n        border: \"#334155\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\" // Modern red\n    };\n    const navigationItems = [\n        {\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            path: \"/\",\n            description: \"Overview & Analytics\"\n        },\n        {\n            label: \"Agents\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            path: \"/agents\",\n            description: \"Security Personnel\"\n        },\n        {\n            label: \"Sites\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/sites\",\n            description: \"Client Locations\"\n        },\n        {\n            label: \"Shifts\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            path: \"/shifts\",\n            description: \"Schedule Management\"\n        },\n        {\n            label: \"Reports\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            path: \"/reports\",\n            description: \"Security Reports\"\n        },\n        {\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            path: \"/analytics\",\n            description: \"Performance Metrics\"\n        },\n        {\n            label: \"Users\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            path: \"/users\",\n            description: \"User Management\"\n        }\n    ];\n    const handleNavigation = (path)=>{\n        router.push(path);\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/sign-in\");\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        variant: \"permanent\",\n        sx: {\n            width: sidebarWidth,\n            flexShrink: 0,\n            \"& .MuiDrawer-paper\": {\n                width: sidebarWidth,\n                boxSizing: \"border-box\",\n                backgroundColor: colors.sidebar,\n                borderRight: `1px solid ${colors.border}`,\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.enteringScreen\n                }),\n                overflowX: \"hidden\",\n                // Modern shadow\n                boxShadow: \"4px 0 24px rgba(0, 0, 0, 0.12)\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    p: 3,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: isCollapsed ? \"center\" : \"space-between\",\n                    borderBottom: `1px solid ${colors.border}`,\n                    minHeight: 80\n                },\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                sx: {\n                                    fontSize: 32,\n                                    color: colors.accent,\n                                    mr: 2,\n                                    filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            color: colors.text,\n                                            fontWeight: 700,\n                                            fontSize: \"1.25rem\",\n                                            letterSpacing: \"-0.025em\"\n                                        },\n                                        children: \"BahinLink\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: colors.textSecondary,\n                                            fontSize: \"0.75rem\",\n                                            fontWeight: 500\n                                        },\n                                        children: \"Security Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, undefined),\n                    isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        sx: {\n                            fontSize: 28,\n                            color: colors.accent,\n                            filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: toggleSidebar,\n                        sx: {\n                            color: colors.textSecondary,\n                            backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                            width: 32,\n                            height: 32,\n                            \"&:hover\": {\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                color: colors.text\n                            },\n                            transition: \"all 0.2s ease-in-out\"\n                        },\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 225,\n                            columnNumber: 26\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 225,\n                            columnNumber: 45\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    label: \"LIVE\",\n                    size: \"small\",\n                    sx: {\n                        backgroundColor: colors.success,\n                        color: \"white\",\n                        fontWeight: 600,\n                        fontSize: \"0.75rem\",\n                        height: 24,\n                        \"& .MuiChip-label\": {\n                            px: 1.5\n                        },\n                        display: isCollapsed ? \"none\" : \"flex\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    px: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        py: 0\n                    },\n                    children: navigationItems.map((item)=>{\n                        const Icon = item.icon;\n                        const active = isActive(item.path);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            disablePadding: true,\n                            sx: {\n                                mb: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                title: isCollapsed ? `${item.label} - ${item.description}` : \"\",\n                                placement: \"right\",\n                                arrow: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    onClick: ()=>handleNavigation(item.path),\n                                    sx: {\n                                        borderRadius: 2,\n                                        mx: 1,\n                                        px: 2,\n                                        py: 1.5,\n                                        minHeight: 48,\n                                        backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                        border: active ? `1px solid ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.3)}` : \"1px solid transparent\",\n                                        \"&:hover\": {\n                                            backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2) : (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                            transform: \"translateX(2px)\"\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            sx: {\n                                                color: active ? colors.accent : colors.textSecondary,\n                                                minWidth: isCollapsed ? \"auto\" : 40,\n                                                mr: isCollapsed ? 0 : 1.5,\n                                                transition: \"color 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                sx: {\n                                                    fontSize: 22\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                lineNumber: 290,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            primary: item.label,\n                                            secondary: item.description,\n                                            primaryTypographyProps: {\n                                                sx: {\n                                                    color: active ? colors.text : colors.textSecondary,\n                                                    fontWeight: active ? 600 : 500,\n                                                    fontSize: \"0.875rem\",\n                                                    transition: \"color 0.2s ease-in-out\"\n                                                }\n                                            },\n                                            secondaryTypographyProps: {\n                                                sx: {\n                                                    color: colors.textMuted,\n                                                    fontSize: \"0.75rem\",\n                                                    mt: 0.25\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 294,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 262,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 257,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.path, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 256,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        sx: {\n                            borderColor: colors.border,\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        disablePadding: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: isCollapsed ? \"Settings\" : \"\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: ()=>handleNavigation(\"/settings\"),\n                                sx: {\n                                    borderRadius: 2,\n                                    mx: 1,\n                                    px: 2,\n                                    py: 1.5,\n                                    minHeight: 48,\n                                    backgroundColor: pathname === \"/settings\" ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                        transform: \"translateX(2px)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\",\n                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        sx: {\n                                            color: pathname === \"/settings\" ? colors.accent : colors.textSecondary,\n                                            minWidth: isCollapsed ? \"auto\" : 40,\n                                            mr: isCollapsed ? 0 : 1.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            sx: {\n                                                fontSize: 22\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        primary: \"Settings\",\n                                        primaryTypographyProps: {\n                                            sx: {\n                                                color: pathname === \"/settings\" ? colors.text : colors.textSecondary,\n                                                fontWeight: pathname === \"/settings\" ? 600 : 500,\n                                                fontSize: \"0.875rem\"\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 3,\n                    borderTop: `1px solid ${colors.border}`,\n                    pt: 2\n                },\n                children: !isCollapsed ? // Expanded Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    sx: {\n                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                        borderRadius: 3,\n                        p: 2,\n                        mx: 1,\n                        border: `1px solid ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.border, 0.5)}`\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            mb: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    src: user?.imageUrl,\n                                    alt: user?.fullName,\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        mr: 2,\n                                        border: `2px solid ${colors.accent}`,\n                                        boxShadow: `0 0 0 2px ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)}`\n                                    },\n                                    children: user?.firstName?.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    sx: {\n                                        flex: 1,\n                                        minWidth: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: colors.text,\n                                                fontWeight: 600,\n                                                fontSize: \"0.875rem\",\n                                                lineHeight: 1.2,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            mt: 0.5,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: 14,\n                                                        color: colors.accent,\n                                                        mr: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    sx: {\n                                                        color: colors.textSecondary,\n                                                        fontSize: \"0.75rem\",\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"Administrator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: colors.textMuted,\n                                                fontSize: \"0.7rem\",\n                                                display: \"block\",\n                                                mt: 0.25,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: user?.primaryEmailAddress?.emailAddress\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            onClick: handleSignOut,\n                            sx: {\n                                width: \"100%\",\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                color: colors.error,\n                                borderRadius: 2,\n                                py: 1,\n                                \"&:hover\": {\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                    transform: \"translateY(-1px)\"\n                                },\n                                transition: \"all 0.2s ease-in-out\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18,\n                                        mr: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    variant: \"caption\",\n                                    sx: {\n                                        fontWeight: 600\n                                    },\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, undefined) : // Collapsed Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: `${user?.fullName} - Administrator`,\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                src: user?.imageUrl,\n                                alt: user?.fullName,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    border: `2px solid ${colors.accent}`,\n                                    boxShadow: `0 0 0 2px ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)}`,\n                                    cursor: \"pointer\"\n                                },\n                                children: user?.firstName?.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: \"Sign Out\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                onClick: handleSignOut,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                    color: colors.error,\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                        transform: \"scale(1.05)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 515,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 500,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 483,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ModernSidebar.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ShiftTable.js":
/*!**************************************!*\
  !*** ./src/components/ShiftTable.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/PlayArrow.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Stop.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/MoreVert.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Visibility.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Edit.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday!=!date-fns */ \"(ssr)/../../node_modules/date-fns/isToday.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday!=!date-fns */ \"(ssr)/../../node_modules/date-fns/isTomorrow.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday!=!date-fns */ \"(ssr)/../../node_modules/date-fns/isYesterday.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday!=!date-fns */ \"(ssr)/../../node_modules/date-fns/format.mjs\");\n// BahinLink Web Admin Shift Table Component\n// ⚠️ CRITICAL: Real shift management with live data ONLY\n\n\n\n\n\nconst ShiftTable = ({ shifts = [], loading = false, onEdit, onDelete, onView, onStatusChange, showActions = true, compact = false })=>{\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedShift, setSelectedShift] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editDialogOpen, setEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFormData, setEditFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleMenuOpen = (event, shift)=>{\n        setAnchorEl(event.currentTarget);\n        setSelectedShift(shift);\n    };\n    const handleMenuClose = ()=>{\n        setAnchorEl(null);\n        setSelectedShift(null);\n    };\n    const handleEdit = ()=>{\n        if (selectedShift) {\n            setEditFormData({\n                id: selectedShift.id,\n                status: selectedShift.status,\n                specialInstructions: selectedShift.specialInstructions || \"\"\n            });\n            setEditDialogOpen(true);\n        }\n        handleMenuClose();\n    };\n    const handleEditSave = ()=>{\n        if (onEdit && editFormData.id) {\n            onEdit(editFormData.id, editFormData);\n            setEditDialogOpen(false);\n            setEditFormData({});\n        }\n    };\n    const handleStatusChange = (shiftId, newStatus)=>{\n        if (onStatusChange) {\n            onStatusChange(shiftId, newStatus);\n        }\n        handleMenuClose();\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"SCHEDULED\":\n                return \"primary\";\n            case \"IN_PROGRESS\":\n                return \"success\";\n            case \"COMPLETED\":\n                return \"default\";\n            case \"CANCELLED\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"SCHEDULED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 109,\n                    columnNumber: 32\n                }, undefined);\n            case \"IN_PROGRESS\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 110,\n                    columnNumber: 34\n                }, undefined);\n            case \"COMPLETED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 111,\n                    columnNumber: 32\n                }, undefined);\n            case \"CANCELLED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 112,\n                    columnNumber: 32\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 113,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_6__.isToday)(date)) return \"Today\";\n        if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_7__.isTomorrow)(date)) return \"Tomorrow\";\n        if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_8__.isYesterday)(date)) return \"Yesterday\";\n        return (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_9__.format)(date, \"MMM dd, yyyy\");\n    };\n    const formatTime = (dateString)=>{\n        return (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_9__.format)(new Date(dateString), \"HH:mm\");\n    };\n    const calculateProgress = (shift)=>{\n        if (shift.status !== \"IN_PROGRESS\") return 0;\n        const now = new Date();\n        const start = new Date(shift.startTime);\n        const end = new Date(shift.endTime);\n        if (now < start) return 0;\n        if (now > end) return 100;\n        const total = end - start;\n        const elapsed = now - start;\n        return Math.round(elapsed / total * 100);\n    };\n    const getAgentInitials = (agent)=>{\n        if (!agent || !agent.user) return \"?\";\n        const { firstName, lastName } = agent.user;\n        return `${firstName?.[0] || \"\"}${lastName?.[0] || \"\"}`.toUpperCase();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            sx: {\n                width: \"100%\",\n                mt: 2\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    variant: \"body2\",\n                    sx: {\n                        mt: 1,\n                        textAlign: \"center\"\n                    },\n                    children: \"Loading shifts...\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                component: _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                elevation: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: compact ? \"small\" : \"medium\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Agent\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Site\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Date\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Time\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Duration\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        align: \"right\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 174,\n                                        columnNumber: 31\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            children: shifts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    colSpan: showActions ? 8 : 7,\n                                    align: \"center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"textSecondary\",\n                                        sx: {\n                                            py: 4\n                                        },\n                                        children: \"No shifts found\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, undefined) : shifts.map((shift)=>{\n                                const progress = calculateProgress(shift);\n                                const isActive = shift.status === \"IN_PROGRESS\";\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        \"&:hover\": {\n                                            backgroundColor: \"action.hover\"\n                                        },\n                                        backgroundColor: isActive ? \"success.light\" : \"inherit\",\n                                        opacity: shift.status === \"CANCELLED\" ? 0.6 : 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        sx: {\n                                                            width: 32,\n                                                            height: 32,\n                                                            fontSize: \"0.875rem\",\n                                                            bgcolor: isActive ? \"success.main\" : \"primary.main\"\n                                                        },\n                                                        children: getAgentInitials(shift.agent)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                fontWeight: \"medium\",\n                                                                children: shift.agent?.user ? `${shift.agent.user.firstName} ${shift.agent.user.lastName}` : \"Unassigned\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            shift.agent?.employeeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                color: \"textSecondary\",\n                                                                children: [\n                                                                    \"ID: \",\n                                                                    shift.agent.employeeId\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 202,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        fontWeight: \"medium\",\n                                                        children: shift.site?.name || \"Unknown Site\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        variant: \"caption\",\n                                                        color: \"textSecondary\",\n                                                        children: shift.site?.client?.companyName || \"Unknown Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 231,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                variant: \"body2\",\n                                                children: formatDate(shift.shiftDate)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 243,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 242,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    children: [\n                                                        formatTime(shift.startTime),\n                                                        \" - \",\n                                                        formatTime(shift.endTime)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                shift.actualStartTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"textSecondary\",\n                                                    display: \"block\",\n                                                    children: [\n                                                        \"Started: \",\n                                                        formatTime(shift.actualStartTime)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 249,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    children: [\n                                                        shift.scheduledDuration?.toFixed(1) || 0,\n                                                        \"h\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                shift.hoursWorked > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"textSecondary\",\n                                                    display: \"block\",\n                                                    children: [\n                                                        \"Worked: \",\n                                                        shift.hoursWorked.toFixed(1),\n                                                        \"h\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 261,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                icon: getStatusIcon(shift.status),\n                                                label: shift.status,\n                                                color: getStatusColor(shift.status),\n                                                size: \"small\",\n                                                variant: isActive ? \"filled\" : \"outlined\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 274,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 273,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                sx: {\n                                                    width: \"100%\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: progress,\n                                                        sx: {\n                                                            mb: 0.5\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        variant: \"caption\",\n                                                        color: \"textSecondary\",\n                                                        children: [\n                                                            progress,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 286,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"textSecondary\",\n                                                children: shift.status === \"COMPLETED\" ? \"100%\" : \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 297,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            align: \"right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: \"small\",\n                                                onClick: (e)=>handleMenuOpen(e, shift),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 305,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, shift.id, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                    lineNumber: 192,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                anchorEl: anchorEl,\n                open: Boolean(anchorEl),\n                onClose: handleMenuClose,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: ()=>onView && onView(selectedShift?.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, undefined),\n                            \"View Details\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: handleEdit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Edit Shift\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedShift?.status === \"SCHEDULED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: ()=>handleStatusChange(selectedShift.id, \"IN_PROGRESS\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Start Shift\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedShift?.status === \"IN_PROGRESS\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: ()=>handleStatusChange(selectedShift.id, \"COMPLETED\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Complete Shift\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: ()=>onDelete && onDelete(selectedShift?.id),\n                        sx: {\n                            color: \"error.main\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Cancel Shift\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                open: editDialogOpen,\n                onClose: ()=>setEditDialogOpen(false),\n                maxWidth: \"sm\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: \"Edit Shift\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            sx: {\n                                pt: 1,\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                gap: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    fullWidth: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                            value: editFormData.status || \"\",\n                                            label: \"Status\",\n                                            onChange: (e)=>setEditFormData((prev)=>({\n                                                        ...prev,\n                                                        status: e.target.value\n                                                    })),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: \"SCHEDULED\",\n                                                    children: \"Scheduled\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: \"IN_PROGRESS\",\n                                                    children: \"In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: \"COMPLETED\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: \"CANCELLED\",\n                                                    children: \"Cancelled\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                    fullWidth: true,\n                                    label: \"Special Instructions\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: editFormData.specialInstructions || \"\",\n                                    onChange: (e)=>setEditFormData((prev)=>({\n                                                ...prev,\n                                                specialInstructions: e.target.value\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                onClick: ()=>setEditDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                onClick: handleEditSave,\n                                variant: \"contained\",\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShiftTable);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ShiftTable.js\n");

/***/ }),

/***/ "(ssr)/./src/services/ApiService.js":
/*!************************************!*\
  !*** ./src/services/ApiService.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../../node_modules/axios/lib/axios.js\");\n// BahinLink Web Admin API Service\n// ⚠️ CRITICAL: Real production API integration with Clerk authentication ONLY\n\n// Real production API base URL\nconst API_BASE_URL =  true ? \"http://localhost:3001/api\" : 0;\nclass ApiService {\n    constructor(){\n        this.baseURL = API_BASE_URL;\n        this.setupInterceptors();\n    }\n    /**\n   * Setup axios interceptors for real authentication\n   */ setupInterceptors() {\n        // Request interceptor to add real Clerk token\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.request.use(async (config)=>{\n            try {\n                // For client-side requests, we'll handle auth in the component\n                // This is a simplified version for development\n                config.headers[\"Content-Type\"] = \"application/json\";\n                config.baseURL = this.baseURL;\n                console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n                return config;\n            } catch (error) {\n                console.error(\"Request interceptor error:\", error);\n                return config;\n            }\n        }, (error)=>{\n            console.error(\"Request interceptor error:\", error);\n            return Promise.reject(error);\n        });\n        // Response interceptor for error handling\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.response.use((response)=>{\n            console.log(`API Response: ${response.status} ${response.config.url}`);\n            return response.data;\n        }, (error)=>{\n            console.error(\"API Error:\", error.response?.data || error.message);\n            if (error.response?.status === 401) {\n                // Redirect to sign-in\n                window.location.href = \"/sign-in\";\n            }\n            return Promise.reject(error.response?.data || error);\n        });\n    }\n    /**\n   * GET request to real API\n   */ async get(endpoint, params = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(endpoint, {\n                params\n            });\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * POST request to real API\n   */ async post(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * PUT request to real API\n   */ async put(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * DELETE request to real API\n   */ async delete(endpoint) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(endpoint);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Dashboard Analytics\n    async getDashboardAnalytics() {\n        return this.get(\"/analytics/dashboard\");\n    }\n    async getPerformanceMetrics(params = {}) {\n        return this.get(\"/analytics/performance\", params);\n    }\n    async getAgentLocations() {\n        return this.get(\"/analytics/locations\");\n    }\n    // User Management\n    async getUsers(params = {}) {\n        return this.get(\"/users\", params);\n    }\n    async getUser(userId) {\n        return this.get(`/users/${userId}`);\n    }\n    async updateUser(userId, data) {\n        return this.put(`/users/${userId}`, data);\n    }\n    // Agent Management\n    async getAgents(params = {}) {\n        return this.get(\"/agents\", params);\n    }\n    async getAgent(agentId) {\n        return this.get(`/agents/${agentId}`);\n    }\n    async updateAgent(agentId, data) {\n        return this.put(`/agents/${agentId}`, data);\n    }\n    async getNearbyAgents(latitude, longitude, radius) {\n        return this.get(\"/agents/nearby\", {\n            latitude,\n            longitude,\n            radius\n        });\n    }\n    // Site Management\n    async getSites(params = {}) {\n        return this.get(\"/sites\", params);\n    }\n    async createSite(data) {\n        return this.post(\"/sites\", data);\n    }\n    async getSite(siteId) {\n        return this.get(`/sites/${siteId}`);\n    }\n    async updateSite(siteId, data) {\n        return this.put(`/sites/${siteId}`, data);\n    }\n    async generateSiteQR(siteId) {\n        return this.post(`/sites/${siteId}/generate-qr`);\n    }\n    // Shift Management\n    async getShifts(params = {}) {\n        return this.get(\"/shifts\", params);\n    }\n    async createShift(data) {\n        return this.post(\"/shifts\", data);\n    }\n    async getShift(shiftId) {\n        return this.get(`/shifts/${shiftId}`);\n    }\n    async updateShift(shiftId, data) {\n        return this.put(`/shifts/${shiftId}`, data);\n    }\n    async assignShift(shiftId, agentId) {\n        return this.put(`/shifts/${shiftId}`, {\n            agentId\n        });\n    }\n    // Time Tracking\n    async getTimeEntries(params = {}) {\n        return this.get(\"/time/entries\", params);\n    }\n    async verifyTimeEntry(timeEntryId, data) {\n        return this.put(`/time/entries/${timeEntryId}/verify`, data);\n    }\n    // Reports Management\n    async getReports(params = {}) {\n        return this.get(\"/reports\", params);\n    }\n    async getReport(reportId) {\n        return this.get(`/reports/${reportId}`);\n    }\n    async approveReport(reportId, data = {}) {\n        return this.post(`/reports/${reportId}/approve`, data);\n    }\n    async rejectReport(reportId, data) {\n        return this.post(`/reports/${reportId}/reject`, data);\n    }\n    // Notifications\n    async getNotifications(params = {}) {\n        return this.get(\"/notifications\", params);\n    }\n    async sendNotification(data) {\n        return this.post(\"/notifications\", data);\n    }\n    async broadcastNotification(data) {\n        return this.post(\"/notifications/broadcast\", data);\n    }\n    // Communications\n    async getMessages(params = {}) {\n        return this.get(\"/communications\", params);\n    }\n    async sendMessage(data) {\n        return this.post(\"/communications\", data);\n    }\n    async getMessageThreads(params = {}) {\n        return this.get(\"/communications/threads\", params);\n    }\n    // Client Management\n    async getClients(params = {}) {\n        return this.get(\"/clients\", params);\n    }\n    async getClientRequests(params = {}) {\n        return this.get(\"/client/requests\", params);\n    }\n    async updateClientRequest(requestId, data) {\n        return this.put(`/client/requests/${requestId}`, data);\n    }\n    // Geofencing\n    async checkGeofence(data) {\n        return this.post(\"/geofence/check\", data);\n    }\n    async getGeofenceViolations(params = {}) {\n        return this.get(\"/geofence/violations\", params);\n    }\n    async resolveGeofenceViolation(violationId, data) {\n        return this.put(`/geofence/violations/${violationId}/resolve`, data);\n    }\n    // File Management\n    async uploadFile(file, type = \"document\") {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const endpoint = `/upload/${type}`;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                },\n                baseURL: this.baseURL\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Bulk Operations\n    async bulkUpdateShifts(shiftIds, data) {\n        return this.put(\"/shifts/bulk\", {\n            shiftIds,\n            ...data\n        });\n    }\n    async bulkNotifyAgents(agentIds, notification) {\n        return this.post(\"/notifications/bulk\", {\n            agentIds,\n            ...notification\n        });\n    }\n    // Export Functions\n    async exportReports(params = {}) {\n        return this.get(\"/reports/export\", params);\n    }\n    async exportTimeEntries(params = {}) {\n        return this.get(\"/time/entries/export\", params);\n    }\n    async exportAgentPerformance(params = {}) {\n        return this.get(\"/analytics/performance/export\", params);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new ApiService());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/ApiService.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8d498114e33b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRjMjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZDQ5ODExNGUzM2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"(rsc)/../../node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n// BahinLink Web Admin Layout\n// ⚠️ CRITICAL: Real Clerk authentication and production data integration ONLY\n\n\n\n\n// Real Clerk publishable key - MUST be production key\nconst CLERK_PUBLISHABLE_KEY = \"pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk\";\nconst metadata = {\n    title: \"BahinLink Admin - Security Workforce Management\",\n    description: \"Real-time security workforce management for Bahin SARL\",\n    keywords: \"security, workforce, management, GPS tracking, Senegal, Bahin SARL\",\n    authors: [\n        {\n            name: \"Bahin SARL\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.ClerkProvider, {\n        publishableKey: CLERK_PUBLISHABLE_KEY,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/shifts/page.js":
/*!********************************!*\
  !*** ./src/app/shifts/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/shifts/page.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/node-fetch-native","vendor-chunks/@clerk","vendor-chunks/@peculiar","vendor-chunks/asn1js","vendor-chunks/@opentelemetry","vendor-chunks/webcrypto-core","vendor-chunks/swr","vendor-chunks/pvtsutils","vendor-chunks/tslib","vendor-chunks/pvutils","vendor-chunks/cookie","vendor-chunks/deepmerge","vendor-chunks/use-sync-external-store","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/react-is","vendor-chunks/hoist-non-react-statics","vendor-chunks/@babel","vendor-chunks/object-assign","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@popperjs","vendor-chunks/react-transition-group","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/date-fns","vendor-chunks/dom-helpers"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshifts%2Fpage&page=%2Fshifts%2Fpage&appPaths=%2Fshifts%2Fpage&pagePath=private-next-app-dir%2Fshifts%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();