'use client';

import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Typography,
  Chip,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import { MoreVert as MoreVertIcon } from '@mui/icons-material';
import { useState } from 'react';

/**
 * Clean, minimalistic data table component
 * Provides consistent table design across all pages
 */
export default function CleanDataTable({
  data = [],
  columns = [],
  loading = false,
  pagination = { page: 0, rowsPerPage: 10, total: 0 },
  onPageChange,
  onRowsPerPageChange,
  actions = [],
  emptyMessage = 'No data available'
}) {
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);

  const handleMenuOpen = (event, row) => {
    setAnchorEl(event.currentTarget);
    setSelectedRow(row);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRow(null);
  };

  const handleActionClick = (action) => {
    if (action.onClick && selectedRow) {
      action.onClick(selectedRow);
    }
    handleMenuClose();
  };

  const renderCellContent = (value, column) => {
    if (column.type === 'status') {
      const statusColors = {
        ACTIVE: '#10b981',
        INACTIVE: '#6b7280',
        SCHEDULED: '#3b82f6',
        IN_PROGRESS: '#f59e0b',
        COMPLETED: '#10b981',
        CANCELLED: '#ef4444',
        PENDING: '#f59e0b',
        APPROVED: '#10b981',
        REJECTED: '#ef4444'
      };
      
      return (
        <Chip
          label={value}
          size="small"
          sx={{
            backgroundColor: statusColors[value] || '#6b7280',
            color: 'white',
            fontSize: '12px',
            fontWeight: 500
          }}
        />
      );
    }
    
    if (column.type === 'date') {
      return new Date(value).toLocaleDateString();
    }
    
    if (column.type === 'datetime') {
      return new Date(value).toLocaleString();
    }
    
    if (column.render) {
      return column.render(value);
    }
    
    return value;
  };

  return (
    <Box sx={{ 
      borderRadius: 3,
      border: '1px solid #f1f5f9',
      backgroundColor: '#ffffff',
      overflow: 'hidden'
    }}>
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f8fafc' }}>
              {columns.map((column) => (
                <TableCell
                  key={column.id}
                  sx={{
                    fontWeight: 600,
                    color: '#374151',
                    fontSize: '14px',
                    borderBottom: '1px solid #f1f5f9'
                  }}
                >
                  {column.label}
                </TableCell>
              ))}
              {actions.length > 0 && (
                <TableCell
                  sx={{
                    fontWeight: 600,
                    color: '#374151',
                    fontSize: '14px',
                    borderBottom: '1px solid #f1f5f9',
                    width: 60
                  }}
                >
                  Actions
                </TableCell>
              )}
            </TableRow>
          </TableHead>
          <TableBody>
            {data.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length + (actions.length > 0 ? 1 : 0)}
                  sx={{ textAlign: 'center', py: 4 }}
                >
                  <Typography variant="body2" color="textSecondary">
                    {emptyMessage}
                  </Typography>
                </TableCell>
              </TableRow>
            ) : (
              data.map((row, index) => (
                <TableRow
                  key={row.id || index}
                  sx={{
                    '&:hover': {
                      backgroundColor: '#f9fafb'
                    },
                    borderBottom: '1px solid #f1f5f9'
                  }}
                >
                  {columns.map((column) => (
                    <TableCell
                      key={column.id}
                      sx={{
                        fontSize: '14px',
                        color: '#374151',
                        borderBottom: 'none'
                      }}
                    >
                      {renderCellContent(row[column.id], column)}
                    </TableCell>
                  ))}
                  {actions.length > 0 && (
                    <TableCell sx={{ borderBottom: 'none' }}>
                      <IconButton
                        size="small"
                        onClick={(e) => handleMenuOpen(e, row)}
                        sx={{ color: '#6b7280' }}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </TableCell>
                  )}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>

      {pagination && (
        <TablePagination
          component="div"
          count={pagination.total}
          page={pagination.page}
          onPageChange={onPageChange}
          rowsPerPage={pagination.rowsPerPage}
          onRowsPerPageChange={onRowsPerPageChange}
          sx={{
            borderTop: '1px solid #f1f5f9',
            '& .MuiTablePagination-toolbar': {
              fontSize: '14px'
            }
          }}
        />
      )}

      {/* Actions Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        {actions.map((action, index) => (
          <MenuItem
            key={index}
            onClick={() => handleActionClick(action)}
            sx={{ fontSize: '14px' }}
          >
            {action.icon && <Box sx={{ mr: 1, display: 'flex' }}>{action.icon}</Box>}
            {action.label}
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
}
