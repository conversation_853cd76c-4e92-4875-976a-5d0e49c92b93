/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/shifts/page";
exports.ids = ["app/shifts/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshifts%2Fpage&page=%2Fshifts%2Fpage&appPaths=%2Fshifts%2Fpage&pagePath=private-next-app-dir%2Fshifts%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshifts%2Fpage&page=%2Fshifts%2Fpage&appPaths=%2Fshifts%2Fpage&pagePath=private-next-app-dir%2Fshifts%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1c3a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'shifts',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/shifts/page.js */ \"(rsc)/./src/app/shifts/page.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/shifts/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/shifts/page\",\n        pathname: \"/shifts\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshifts%2Fpage&page=%2Fshifts%2Fpage&appPaths=%2Fshifts%2Fpage&pagePath=private-next-app-dir%2Fshifts%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fshifts%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fshifts%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/shifts/page.js */ \"(ssr)/./src/app/shifts/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGcGFja2FnZXMlMkZ3ZWItYWRtaW4lMkZzcmMlMkZhcHAlMkZzaGlmdHMlMkZwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0SkFBZ0giLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYmFoaW5saW5rL3dlYi1hZG1pbi8/MWU2MSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi93b3Jrc3BhY2VzL2ZpbmFsYWdlbnQtbWFpbi1maW5hbC9wYWNrYWdlcy93ZWItYWRtaW4vc3JjL2FwcC9zaGlmdHMvcGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fshifts%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/shifts/page.js":
/*!********************************!*\
  !*** ./src/app/shifts/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,Chip,CircularProgress,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,Grid,IconButton,InputLabel,MenuItem,Paper,Select,TextField,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Download.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/PlayArrow.js\");\n/* harmony import */ var _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Add,Download,FilterList,Group,PlayArrow,Refresh,Schedule,Stop!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Stop.js\");\n/* harmony import */ var _mui_x_date_pickers_DatePicker__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @mui/x-date-pickers/DatePicker */ \"(ssr)/../../node_modules/@mui/x-date-pickers/DatePicker/DatePicker.js\");\n/* harmony import */ var _mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @mui/x-date-pickers/LocalizationProvider */ \"(ssr)/../../node_modules/@mui/x-date-pickers/LocalizationProvider/LocalizationProvider.js\");\n/* harmony import */ var _mui_x_date_pickers_AdapterDateFnsV3__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @mui/x-date-pickers/AdapterDateFnsV3 */ \"(ssr)/../../node_modules/@mui/x-date-pickers/AdapterDateFnsV3/AdapterDateFnsV3.js\");\n/* harmony import */ var _components_ShiftTable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ShiftTable */ \"(ssr)/./src/components/ShiftTable.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/ApiService */ \"(ssr)/./src/services/ApiService.js\");\n// BahinLink Web Admin Shifts Management Page\n// ⚠️ CRITICAL: Real shift management with live data ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst ShiftsPage = ()=>{\n    const [shifts, setShifts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        status: \"\",\n        agentId: \"\",\n        siteId: \"\",\n        startDate: null,\n        endDate: null\n    });\n    const [agents, setAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sites, setSites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        scheduled: 0,\n        inProgress: 0,\n        completed: 0,\n        cancelled: 0\n    });\n    const [createDialogOpen, setCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newShift, setNewShift] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        agentId: \"\",\n        siteId: \"\",\n        shiftDate: new Date(),\n        startTime: \"\",\n        endTime: \"\",\n        specialInstructions: \"\"\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadData();\n    }, [\n        filters\n    ]);\n    const loadData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Build query parameters\n            const params = {};\n            if (filters.status) params.status = filters.status;\n            if (filters.agentId) params.agentId = filters.agentId;\n            if (filters.siteId) params.siteId = filters.siteId;\n            if (filters.startDate) params.startDate = filters.startDate.toISOString();\n            if (filters.endDate) params.endDate = filters.endDate.toISOString();\n            const [shiftsResponse, agentsResponse, sitesResponse] = await Promise.all([\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/shifts\", params),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/agents\"),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/sites\")\n            ]);\n            if (shiftsResponse.success) {\n                setShifts(shiftsResponse.data);\n                // Calculate stats\n                const newStats = {\n                    total: shiftsResponse.data.length,\n                    scheduled: shiftsResponse.data.filter((s)=>s.status === \"SCHEDULED\").length,\n                    inProgress: shiftsResponse.data.filter((s)=>s.status === \"IN_PROGRESS\").length,\n                    completed: shiftsResponse.data.filter((s)=>s.status === \"COMPLETED\").length,\n                    cancelled: shiftsResponse.data.filter((s)=>s.status === \"CANCELLED\").length\n                };\n                setStats(newStats);\n            }\n            if (agentsResponse.success) {\n                setAgents(agentsResponse.data);\n            }\n            if (sitesResponse.success) {\n                setSites(sitesResponse.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading shifts data:\", error);\n            setError(\"Failed to load shifts data\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCreateShift = async ()=>{\n        try {\n            const shiftData = {\n                ...newShift,\n                shiftDate: newShift.shiftDate.toISOString().split(\"T\")[0],\n                startTime: `${newShift.shiftDate.toISOString().split(\"T\")[0]}T${newShift.startTime}:00`,\n                endTime: `${newShift.shiftDate.toISOString().split(\"T\")[0]}T${newShift.endTime}:00`\n            };\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].post(\"/shifts\", shiftData);\n            if (response.success) {\n                setCreateDialogOpen(false);\n                setNewShift({\n                    agentId: \"\",\n                    siteId: \"\",\n                    shiftDate: new Date(),\n                    startTime: \"\",\n                    endTime: \"\",\n                    specialInstructions: \"\"\n                });\n                loadData();\n            }\n        } catch (error) {\n            console.error(\"Error creating shift:\", error);\n            setError(\"Failed to create shift\");\n        }\n    };\n    const handleEditShift = async (shiftId, updateData)=>{\n        try {\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].put(`/shifts/${shiftId}`, updateData);\n            if (response.success) {\n                loadData();\n            }\n        } catch (error) {\n            console.error(\"Error updating shift:\", error);\n            setError(\"Failed to update shift\");\n        }\n    };\n    const handleDeleteShift = async (shiftId)=>{\n        try {\n            const response = await _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].delete(`/shifts/${shiftId}`);\n            if (response.success) {\n                loadData();\n            }\n        } catch (error) {\n            console.error(\"Error deleting shift:\", error);\n            setError(\"Failed to delete shift\");\n        }\n    };\n    const handleStatusChange = async (shiftId, newStatus)=>{\n        await handleEditShift(shiftId, {\n            status: newStatus\n        });\n    };\n    const handleExport = async ()=>{\n        try {\n            // In a real app, this would generate and download a CSV/Excel file\n            console.log(\"Exporting shifts data...\");\n            alert(\"Export functionality would be implemented here\");\n        } catch (error) {\n            console.error(\"Error exporting data:\", error);\n        }\n    };\n    const StatCard = ({ title, value, icon: Icon, color = \"primary\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: `${color}.main`,\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 198,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: `${color}.main`,\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 206,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 197,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n            lineNumber: 195,\n            columnNumber: 5\n        }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_LocalizationProvider__WEBPACK_IMPORTED_MODULE_8__.LocalizationProvider, {\n        dateAdapter: _mui_x_date_pickers_AdapterDateFnsV3__WEBPACK_IMPORTED_MODULE_9__.AdapterDateFns,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            sx: {\n                p: 3\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        mb: 3,\n                        display: \"flex\",\n                        justifyContent: \"space-between\",\n                        alignItems: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            variant: \"h4\",\n                            fontWeight: \"bold\",\n                            children: \"Shift Management\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                display: \"flex\",\n                                gap: 1\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"outlined\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 223,\n                                        columnNumber: 26\n                                    }, void 0),\n                                    onClick: handleExport,\n                                    children: \"Export\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"outlined\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 230,\n                                        columnNumber: 26\n                                    }, void 0),\n                                    onClick: loadData,\n                                    children: \"Refresh\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"contained\",\n                                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 237,\n                                        columnNumber: 26\n                                    }, void 0),\n                                    onClick: ()=>setCreateDialogOpen(true),\n                                    children: \"Create Shift\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                    severity: \"error\",\n                    sx: {\n                        mb: 2\n                    },\n                    onClose: ()=>setError(null),\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 247,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    container: true,\n                    spacing: 3,\n                    sx: {\n                        mb: 3\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 6,\n                            md: 2.4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                title: \"Total Shifts\",\n                                value: stats.total,\n                                icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                color: \"primary\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 255,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 254,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 6,\n                            md: 2.4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                title: \"Scheduled\",\n                                value: stats.scheduled,\n                                icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                color: \"info\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 6,\n                            md: 2.4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                title: \"In Progress\",\n                                value: stats.inProgress,\n                                icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                color: \"success\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 271,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 6,\n                            md: 2.4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                title: \"Completed\",\n                                value: stats.completed,\n                                icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                color: \"secondary\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 279,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            item: true,\n                            xs: 12,\n                            sm: 6,\n                            md: 2.4,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatCard, {\n                                title: \"Cancelled\",\n                                value: stats.cancelled,\n                                icon: _barrel_optimize_names_Add_Download_FilterList_Group_PlayArrow_Refresh_Schedule_Stop_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                color: \"error\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                    sx: {\n                        p: 2,\n                        mb: 3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        container: true,\n                        spacing: 2,\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    fullWidth: true,\n                                    size: \"small\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            value: filters.status,\n                                            label: \"Status\",\n                                            onChange: (e)=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        status: e.target.value\n                                                    })),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    value: \"\",\n                                                    children: \"All\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    value: \"SCHEDULED\",\n                                                    children: \"Scheduled\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    value: \"IN_PROGRESS\",\n                                                    children: \"In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    value: \"COMPLETED\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    value: \"CANCELLED\",\n                                                    children: \"Cancelled\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 299,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    fullWidth: true,\n                                    size: \"small\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            children: \"Agent\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 318,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            value: filters.agentId,\n                                            label: \"Agent\",\n                                            onChange: (e)=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        agentId: e.target.value\n                                                    })),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    value: \"\",\n                                                    children: \"All Agents\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: agent.id,\n                                                        children: agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : \"Unknown\"\n                                                    }, agent.id, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 319,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 317,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    fullWidth: true,\n                                    size: \"small\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                            children: \"Site\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 336,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            value: filters.siteId,\n                                            label: \"Site\",\n                                            onChange: (e)=>setFilters((prev)=>({\n                                                        ...prev,\n                                                        siteId: e.target.value\n                                                    })),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    value: \"\",\n                                                    children: \"All Sites\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        value: site.id,\n                                                        children: site.name\n                                                    }, site.id, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 21\n                                                    }, undefined))\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 334,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_DatePicker__WEBPACK_IMPORTED_MODULE_24__.DatePicker, {\n                                    label: \"Start Date\",\n                                    value: filters.startDate,\n                                    onChange: (date)=>setFilters((prev)=>({\n                                                ...prev,\n                                                startDate: date\n                                            })),\n                                    slotProps: {\n                                        textField: {\n                                            size: \"small\",\n                                            fullWidth: true\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_DatePicker__WEBPACK_IMPORTED_MODULE_24__.DatePicker, {\n                                    label: \"End Date\",\n                                    value: filters.endDate,\n                                    onChange: (date)=>setFilters((prev)=>({\n                                                ...prev,\n                                                endDate: date\n                                            })),\n                                    slotProps: {\n                                        textField: {\n                                            size: \"small\",\n                                            fullWidth: true\n                                        }\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 2,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"outlined\",\n                                    fullWidth: true,\n                                    onClick: ()=>setFilters({\n                                            status: \"\",\n                                            agentId: \"\",\n                                            siteId: \"\",\n                                            startDate: null,\n                                            endDate: null\n                                        }),\n                                    children: \"Clear Filters\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                        lineNumber: 298,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 297,\n                    columnNumber: 9\n                }, undefined),\n                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        justifyContent: \"center\",\n                        py: 4\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                        lineNumber: 391,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 390,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ShiftTable__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    shifts: shifts,\n                    loading: loading,\n                    onEdit: handleEditShift,\n                    onDelete: handleDeleteShift,\n                    onStatusChange: handleStatusChange,\n                    onView: (shiftId)=>console.log(\"View shift:\", shiftId)\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 394,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                    open: createDialogOpen,\n                    onClose: ()=>setCreateDialogOpen(false),\n                    maxWidth: \"md\",\n                    fullWidth: true,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                            children: \"Create New Shift\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 406,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                container: true,\n                                spacing: 2,\n                                sx: {\n                                    mt: 1\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            fullWidth: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    children: \"Agent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    value: newShift.agentId,\n                                                    label: \"Agent\",\n                                                    onChange: (e)=>setNewShift((prev)=>({\n                                                                ...prev,\n                                                                agentId: e.target.value\n                                                            })),\n                                                    children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            value: agent.id,\n                                                            children: agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : \"Unknown\"\n                                                        }, agent.id, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 409,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 6,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            fullWidth: true,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    children: \"Site\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 428,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    value: newShift.siteId,\n                                                    label: \"Site\",\n                                                    onChange: (e)=>setNewShift((prev)=>({\n                                                                ...prev,\n                                                                siteId: e.target.value\n                                                            })),\n                                                    children: sites.map((site)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            value: site.id,\n                                                            children: site.name\n                                                        }, site.id, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_mui_x_date_pickers_DatePicker__WEBPACK_IMPORTED_MODULE_24__.DatePicker, {\n                                            label: \"Shift Date\",\n                                            value: newShift.shiftDate,\n                                            onChange: (date)=>setNewShift((prev)=>({\n                                                        ...prev,\n                                                        shiftDate: date\n                                                    })),\n                                            slotProps: {\n                                                textField: {\n                                                    fullWidth: true\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 443,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            fullWidth: true,\n                                            label: \"Start Time\",\n                                            type: \"time\",\n                                            value: newShift.startTime,\n                                            onChange: (e)=>setNewShift((prev)=>({\n                                                        ...prev,\n                                                        startTime: e.target.value\n                                                    })),\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 452,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        sm: 4,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            fullWidth: true,\n                                            label: \"End Time\",\n                                            type: \"time\",\n                                            value: newShift.endTime,\n                                            onChange: (e)=>setNewShift((prev)=>({\n                                                        ...prev,\n                                                        endTime: e.target.value\n                                                    })),\n                                            InputLabelProps: {\n                                                shrink: true\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        item: true,\n                                        xs: 12,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                            fullWidth: true,\n                                            label: \"Special Instructions\",\n                                            multiline: true,\n                                            rows: 3,\n                                            value: newShift.specialInstructions,\n                                            onChange: (e)=>setNewShift((prev)=>({\n                                                        ...prev,\n                                                        specialInstructions: e.target.value\n                                                    }))\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                            lineNumber: 475,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                        lineNumber: 474,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                lineNumber: 408,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 407,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    onClick: ()=>setCreateDialogOpen(false),\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_Chip_CircularProgress_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_Grid_IconButton_InputLabel_MenuItem_Paper_Select_TextField_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    onClick: handleCreateShift,\n                                    variant: \"contained\",\n                                    children: \"Create Shift\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                                    lineNumber: 488,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                            lineNumber: 486,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShiftsPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/shifts/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ShiftTable.js":
/*!**************************************!*\
  !*** ./src/components/ShiftTable.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Menu/Menu.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Dialog/Dialog.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogTitle/DialogTitle.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogContent/DialogContent.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TextField/TextField.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/DialogActions/DialogActions.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Button,Chip,Dialog,DialogActions,DialogContent,DialogTitle,FormControl,IconButton,InputLabel,LinearProgress,Menu,MenuItem,Paper,Select,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,TextField,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/PlayArrow.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Stop.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Delete.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/MoreVert.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Visibility.js\");\n/* harmony import */ var _barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Delete,Edit,LocationOn,MoreVert,Person,PlayArrow,Schedule,Stop,Visibility!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Edit.js\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday!=!date-fns */ \"(ssr)/../../node_modules/date-fns/isToday.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday!=!date-fns */ \"(ssr)/../../node_modules/date-fns/isTomorrow.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday!=!date-fns */ \"(ssr)/../../node_modules/date-fns/isYesterday.mjs\");\n/* harmony import */ var _barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format,isToday,isTomorrow,isYesterday!=!date-fns */ \"(ssr)/../../node_modules/date-fns/format.mjs\");\n// BahinLink Web Admin Shift Table Component\n// ⚠️ CRITICAL: Real shift management with live data ONLY\n\n\n\n\n\nconst ShiftTable = ({ shifts = [], loading = false, onEdit, onDelete, onView, onStatusChange, showActions = true, compact = false })=>{\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedShift, setSelectedShift] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editDialogOpen, setEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editFormData, setEditFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const handleMenuOpen = (event, shift)=>{\n        setAnchorEl(event.currentTarget);\n        setSelectedShift(shift);\n    };\n    const handleMenuClose = ()=>{\n        setAnchorEl(null);\n        setSelectedShift(null);\n    };\n    const handleEdit = ()=>{\n        if (selectedShift) {\n            setEditFormData({\n                id: selectedShift.id,\n                status: selectedShift.status,\n                specialInstructions: selectedShift.specialInstructions || \"\"\n            });\n            setEditDialogOpen(true);\n        }\n        handleMenuClose();\n    };\n    const handleEditSave = ()=>{\n        if (onEdit && editFormData.id) {\n            onEdit(editFormData.id, editFormData);\n            setEditDialogOpen(false);\n            setEditFormData({});\n        }\n    };\n    const handleStatusChange = (shiftId, newStatus)=>{\n        if (onStatusChange) {\n            onStatusChange(shiftId, newStatus);\n        }\n        handleMenuClose();\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"SCHEDULED\":\n                return \"primary\";\n            case \"IN_PROGRESS\":\n                return \"success\";\n            case \"COMPLETED\":\n                return \"default\";\n            case \"CANCELLED\":\n                return \"error\";\n            default:\n                return \"default\";\n        }\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"SCHEDULED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 109,\n                    columnNumber: 32\n                }, undefined);\n            case \"IN_PROGRESS\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 110,\n                    columnNumber: 34\n                }, undefined);\n            case \"COMPLETED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 111,\n                    columnNumber: 32\n                }, undefined);\n            case \"CANCELLED\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 112,\n                    columnNumber: 32\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 113,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_6__.isToday)(date)) return \"Today\";\n        if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_7__.isTomorrow)(date)) return \"Tomorrow\";\n        if ((0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_8__.isYesterday)(date)) return \"Yesterday\";\n        return (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_9__.format)(date, \"MMM dd, yyyy\");\n    };\n    const formatTime = (dateString)=>{\n        return (0,_barrel_optimize_names_format_isToday_isTomorrow_isYesterday_date_fns__WEBPACK_IMPORTED_MODULE_9__.format)(new Date(dateString), \"HH:mm\");\n    };\n    const calculateProgress = (shift)=>{\n        if (shift.status !== \"IN_PROGRESS\") return 0;\n        const now = new Date();\n        const start = new Date(shift.startTime);\n        const end = new Date(shift.endTime);\n        if (now < start) return 0;\n        if (now > end) return 100;\n        const total = end - start;\n        const elapsed = now - start;\n        return Math.round(elapsed / total * 100);\n    };\n    const getAgentInitials = (agent)=>{\n        if (!agent || !agent.user) return \"?\";\n        const { firstName, lastName } = agent.user;\n        return `${firstName?.[0] || \"\"}${lastName?.[0] || \"\"}`.toUpperCase();\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            sx: {\n                width: \"100%\",\n                mt: 2\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 153,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    variant: \"body2\",\n                    sx: {\n                        mt: 1,\n                        textAlign: \"center\"\n                    },\n                    children: \"Loading shifts...\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 154,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n            lineNumber: 152,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                component: _barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n                elevation: 1,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                    size: compact ? \"small\" : \"medium\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Agent\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Site\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Date\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Time\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Duration\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 171,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: \"Progress\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        align: \"right\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 174,\n                                        columnNumber: 31\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                            children: shifts.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    colSpan: showActions ? 8 : 7,\n                                    align: \"center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        variant: \"body2\",\n                                        color: \"textSecondary\",\n                                        sx: {\n                                            py: 4\n                                        },\n                                        children: \"No shifts found\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                        lineNumber: 181,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                    lineNumber: 180,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, undefined) : shifts.map((shift)=>{\n                                const progress = calculateProgress(shift);\n                                const isActive = shift.status === \"IN_PROGRESS\";\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    sx: {\n                                        \"&:hover\": {\n                                            backgroundColor: \"action.hover\"\n                                        },\n                                        backgroundColor: isActive ? \"success.light\" : \"inherit\",\n                                        opacity: shift.status === \"CANCELLED\" ? 0.6 : 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                sx: {\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    gap: 1\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        sx: {\n                                                            width: 32,\n                                                            height: 32,\n                                                            fontSize: \"0.875rem\",\n                                                            bgcolor: isActive ? \"success.main\" : \"primary.main\"\n                                                        },\n                                                        children: getAgentInitials(shift.agent)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 203,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                fontWeight: \"medium\",\n                                                                children: shift.agent?.user ? `${shift.agent.user.firstName} ${shift.agent.user.lastName}` : \"Unassigned\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                                lineNumber: 214,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            shift.agent?.employeeId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                color: \"textSecondary\",\n                                                                children: [\n                                                                    \"ID: \",\n                                                                    shift.agent.employeeId\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 202,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 201,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        variant: \"body2\",\n                                                        fontWeight: \"medium\",\n                                                        children: shift.site?.name || \"Unknown Site\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        variant: \"caption\",\n                                                        color: \"textSecondary\",\n                                                        children: shift.site?.client?.companyName || \"Unknown Client\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 231,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                variant: \"body2\",\n                                                children: formatDate(shift.shiftDate)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 243,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 242,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    children: [\n                                                        formatTime(shift.startTime),\n                                                        \" - \",\n                                                        formatTime(shift.endTime)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                shift.actualStartTime && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"textSecondary\",\n                                                    display: \"block\",\n                                                    children: [\n                                                        \"Started: \",\n                                                        formatTime(shift.actualStartTime)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 249,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    children: [\n                                                        shift.scheduledDuration?.toFixed(1) || 0,\n                                                        \"h\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                shift.hoursWorked > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    color: \"textSecondary\",\n                                                    display: \"block\",\n                                                    children: [\n                                                        \"Worked: \",\n                                                        shift.hoursWorked.toFixed(1),\n                                                        \"h\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 25\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 261,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                icon: getStatusIcon(shift.status),\n                                                label: shift.status,\n                                                color: getStatusColor(shift.status),\n                                                size: \"small\",\n                                                variant: isActive ? \"filled\" : \"outlined\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 274,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 273,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: isActive ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                sx: {\n                                                    width: \"100%\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        variant: \"determinate\",\n                                                        value: progress,\n                                                        sx: {\n                                                            mb: 0.5\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        variant: \"caption\",\n                                                        color: \"textSecondary\",\n                                                        children: [\n                                                            progress,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 27\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 286,\n                                                columnNumber: 25\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                variant: \"caption\",\n                                                color: \"textSecondary\",\n                                                children: shift.status === \"COMPLETED\" ? \"100%\" : \"-\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 297,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 284,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        showActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            align: \"right\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                size: \"small\",\n                                                onClick: (e)=>handleMenuOpen(e, shift),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 27\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                lineNumber: 306,\n                                                columnNumber: 25\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 305,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, shift.id, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                    lineNumber: 192,\n                                    columnNumber: 19\n                                }, undefined);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                    lineNumber: 164,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                anchorEl: anchorEl,\n                open: Boolean(anchorEl),\n                onClose: handleMenuClose,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: ()=>onView && onView(selectedShift?.id),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, undefined),\n                            \"View Details\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 328,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: handleEdit,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 333,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Edit Shift\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 332,\n                        columnNumber: 9\n                    }, undefined),\n                    selectedShift?.status === \"SCHEDULED\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: ()=>handleStatusChange(selectedShift.id, \"IN_PROGRESS\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Start Shift\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 337,\n                        columnNumber: 11\n                    }, undefined),\n                    selectedShift?.status === \"IN_PROGRESS\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: ()=>handleStatusChange(selectedShift.id, \"COMPLETED\"),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, undefined),\n                            \"Complete Shift\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        onClick: ()=>onDelete && onDelete(selectedShift?.id),\n                        sx: {\n                            color: \"error.main\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Delete_Edit_LocationOn_MoreVert_Person_PlayArrow_Schedule_Stop_Visibility_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                fontSize: \"small\",\n                                sx: {\n                                    mr: 1\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Cancel Shift\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 348,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                open: editDialogOpen,\n                onClose: ()=>setEditDialogOpen(false),\n                maxWidth: \"sm\",\n                fullWidth: true,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                        children: \"Edit Shift\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 359,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                            sx: {\n                                pt: 1,\n                                display: \"flex\",\n                                flexDirection: \"column\",\n                                gap: 2\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    fullWidth: true,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                                            children: \"Status\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_33__[\"default\"], {\n                                            value: editFormData.status || \"\",\n                                            label: \"Status\",\n                                            onChange: (e)=>setEditFormData((prev)=>({\n                                                        ...prev,\n                                                        status: e.target.value\n                                                    })),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: \"SCHEDULED\",\n                                                    children: \"Scheduled\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: \"IN_PROGRESS\",\n                                                    children: \"In Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: \"COMPLETED\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                    value: \"CANCELLED\",\n                                                    children: \"Cancelled\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_34__[\"default\"], {\n                                    fullWidth: true,\n                                    label: \"Special Instructions\",\n                                    multiline: true,\n                                    rows: 3,\n                                    value: editFormData.specialInstructions || \"\",\n                                    onChange: (e)=>setEditFormData((prev)=>({\n                                                ...prev,\n                                                specialInstructions: e.target.value\n                                            }))\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                    lineNumber: 376,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                            lineNumber: 361,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_35__[\"default\"], {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                onClick: ()=>setEditDialogOpen(false),\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Button_Chip_Dialog_DialogActions_DialogContent_DialogTitle_FormControl_IconButton_InputLabel_LinearProgress_Menu_MenuItem_Paper_Select_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_TextField_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_36__[\"default\"], {\n                                onClick: handleEditSave,\n                                variant: \"contained\",\n                                children: \"Save\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                                lineNumber: 388,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ShiftTable.js\",\n                lineNumber: 358,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ShiftTable);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ShiftTable.js\n");

/***/ }),

/***/ "(ssr)/./src/services/ApiService.js":
/*!************************************!*\
  !*** ./src/services/ApiService.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../../node_modules/axios/lib/axios.js\");\n// BahinLink Web Admin API Service\n// ⚠️ CRITICAL: Real production API integration with Clerk authentication ONLY\n\n// Real production API base URL\nconst API_BASE_URL =  true ? \"http://localhost:3001/api\" : 0;\nclass ApiService {\n    constructor(){\n        this.baseURL = API_BASE_URL;\n        this.setupInterceptors();\n    }\n    /**\n   * Setup axios interceptors for real authentication\n   */ setupInterceptors() {\n        // Request interceptor to add real Clerk token\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.request.use(async (config)=>{\n            try {\n                // For client-side requests, we'll handle auth in the component\n                // This is a simplified version for development\n                config.headers[\"Content-Type\"] = \"application/json\";\n                config.baseURL = this.baseURL;\n                console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n                return config;\n            } catch (error) {\n                console.error(\"Request interceptor error:\", error);\n                return config;\n            }\n        }, (error)=>{\n            console.error(\"Request interceptor error:\", error);\n            return Promise.reject(error);\n        });\n        // Response interceptor for error handling\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.response.use((response)=>{\n            console.log(`API Response: ${response.status} ${response.config.url}`);\n            return response.data;\n        }, (error)=>{\n            console.error(\"API Error:\", error.response?.data || error.message);\n            if (error.response?.status === 401) {\n                // Redirect to sign-in\n                window.location.href = \"/sign-in\";\n            }\n            return Promise.reject(error.response?.data || error);\n        });\n    }\n    /**\n   * GET request to real API\n   */ async get(endpoint, params = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(endpoint, {\n                params\n            });\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * POST request to real API\n   */ async post(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * PUT request to real API\n   */ async put(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * DELETE request to real API\n   */ async delete(endpoint) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(endpoint);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Dashboard Analytics\n    async getDashboardAnalytics() {\n        return this.get(\"/analytics/dashboard\");\n    }\n    async getPerformanceMetrics(params = {}) {\n        return this.get(\"/analytics/performance\", params);\n    }\n    async getAgentLocations() {\n        return this.get(\"/analytics/locations\");\n    }\n    // User Management\n    async getUsers(params = {}) {\n        return this.get(\"/users\", params);\n    }\n    async getUser(userId) {\n        return this.get(`/users/${userId}`);\n    }\n    async updateUser(userId, data) {\n        return this.put(`/users/${userId}`, data);\n    }\n    // Agent Management\n    async getAgents(params = {}) {\n        return this.get(\"/agents\", params);\n    }\n    async getAgent(agentId) {\n        return this.get(`/agents/${agentId}`);\n    }\n    async updateAgent(agentId, data) {\n        return this.put(`/agents/${agentId}`, data);\n    }\n    async getNearbyAgents(latitude, longitude, radius) {\n        return this.get(\"/agents/nearby\", {\n            latitude,\n            longitude,\n            radius\n        });\n    }\n    // Site Management\n    async getSites(params = {}) {\n        return this.get(\"/sites\", params);\n    }\n    async createSite(data) {\n        return this.post(\"/sites\", data);\n    }\n    async getSite(siteId) {\n        return this.get(`/sites/${siteId}`);\n    }\n    async updateSite(siteId, data) {\n        return this.put(`/sites/${siteId}`, data);\n    }\n    async generateSiteQR(siteId) {\n        return this.post(`/sites/${siteId}/generate-qr`);\n    }\n    // Shift Management\n    async getShifts(params = {}) {\n        return this.get(\"/shifts\", params);\n    }\n    async createShift(data) {\n        return this.post(\"/shifts\", data);\n    }\n    async getShift(shiftId) {\n        return this.get(`/shifts/${shiftId}`);\n    }\n    async updateShift(shiftId, data) {\n        return this.put(`/shifts/${shiftId}`, data);\n    }\n    async assignShift(shiftId, agentId) {\n        return this.put(`/shifts/${shiftId}`, {\n            agentId\n        });\n    }\n    // Time Tracking\n    async getTimeEntries(params = {}) {\n        return this.get(\"/time/entries\", params);\n    }\n    async verifyTimeEntry(timeEntryId, data) {\n        return this.put(`/time/entries/${timeEntryId}/verify`, data);\n    }\n    // Reports Management\n    async getReports(params = {}) {\n        return this.get(\"/reports\", params);\n    }\n    async getReport(reportId) {\n        return this.get(`/reports/${reportId}`);\n    }\n    async approveReport(reportId, data = {}) {\n        return this.post(`/reports/${reportId}/approve`, data);\n    }\n    async rejectReport(reportId, data) {\n        return this.post(`/reports/${reportId}/reject`, data);\n    }\n    // Notifications\n    async getNotifications(params = {}) {\n        return this.get(\"/notifications\", params);\n    }\n    async sendNotification(data) {\n        return this.post(\"/notifications\", data);\n    }\n    async broadcastNotification(data) {\n        return this.post(\"/notifications/broadcast\", data);\n    }\n    // Communications\n    async getMessages(params = {}) {\n        return this.get(\"/communications\", params);\n    }\n    async sendMessage(data) {\n        return this.post(\"/communications\", data);\n    }\n    async getMessageThreads(params = {}) {\n        return this.get(\"/communications/threads\", params);\n    }\n    // Client Management\n    async getClients(params = {}) {\n        return this.get(\"/clients\", params);\n    }\n    async getClientRequests(params = {}) {\n        return this.get(\"/client/requests\", params);\n    }\n    async updateClientRequest(requestId, data) {\n        return this.put(`/client/requests/${requestId}`, data);\n    }\n    // Geofencing\n    async checkGeofence(data) {\n        return this.post(\"/geofence/check\", data);\n    }\n    async getGeofenceViolations(params = {}) {\n        return this.get(\"/geofence/violations\", params);\n    }\n    async resolveGeofenceViolation(violationId, data) {\n        return this.put(`/geofence/violations/${violationId}/resolve`, data);\n    }\n    // File Management\n    async uploadFile(file, type = \"document\") {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const endpoint = `/upload/${type}`;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                },\n                baseURL: this.baseURL\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Bulk Operations\n    async bulkUpdateShifts(shiftIds, data) {\n        return this.put(\"/shifts/bulk\", {\n            shiftIds,\n            ...data\n        });\n    }\n    async bulkNotifyAgents(agentIds, notification) {\n        return this.post(\"/notifications/bulk\", {\n            agentIds,\n            ...notification\n        });\n    }\n    // Export Functions\n    async exportReports(params = {}) {\n        return this.get(\"/reports/export\", params);\n    }\n    async exportTimeEntries(params = {}) {\n        return this.get(\"/time/entries/export\", params);\n    }\n    async exportAgentPerformance(params = {}) {\n        return this.get(\"/analytics/performance/export\", params);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new ApiService());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/ApiService.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8d498114e33b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRjMjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZDQ5ODExNGUzM2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"(rsc)/../../node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n// BahinLink Web Admin Layout\n// ⚠️ CRITICAL: Real Clerk authentication and production data integration ONLY\n\n\n\n\n// Real Clerk publishable key - MUST be production key\nconst CLERK_PUBLISHABLE_KEY = \"pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk\";\nconst metadata = {\n    title: \"BahinLink Admin - Security Workforce Management\",\n    description: \"Real-time security workforce management for Bahin SARL\",\n    keywords: \"security, workforce, management, GPS tracking, Senegal, Bahin SARL\",\n    authors: [\n        {\n            name: \"Bahin SARL\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.ClerkProvider, {\n        publishableKey: CLERK_PUBLISHABLE_KEY,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/shifts/page.js":
/*!********************************!*\
  !*** ./src/app/shifts/page.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/finalagent-main-final/packages/web-admin/src/app/shifts/page.js#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3NoaWZ0cy9wYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTZDO0FBQzdDLDRNQUF5RCIsInNvdXJjZXMiOlsid2VicGFjazovL0BiYWhpbmxpbmsvd2ViLWFkbWluLy4vc3JjL2FwcC9zaGlmdHMvcGFnZS5qcz9jYmY3Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEJhaGluTGluayBXZWIgQWRtaW4gU2hpZnRzIE1hbmFnZW1lbnQgUGFnZVxuLy8g4pqg77iPIENSSVRJQ0FMOiBSZWFsIHNoaWZ0IG1hbmFnZW1lbnQgd2l0aCBsaXZlIGRhdGEgT05MWVxuXG4ndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHtcbiAgQm94LFxuICBUeXBvZ3JhcGh5LFxuICBCdXR0b24sXG4gIFBhcGVyLFxuICBHcmlkLFxuICBDYXJkLFxuICBDYXJkQ29udGVudCxcbiAgQ2hpcCxcbiAgSWNvbkJ1dHRvbixcbiAgRGlhbG9nLFxuICBEaWFsb2dUaXRsZSxcbiAgRGlhbG9nQ29udGVudCxcbiAgRGlhbG9nQWN0aW9ucyxcbiAgVGV4dEZpZWxkLFxuICBGb3JtQ29udHJvbCxcbiAgSW5wdXRMYWJlbCxcbiAgU2VsZWN0LFxuICBNZW51SXRlbSxcbiAgQWxlcnQsXG4gIENpcmN1bGFyUHJvZ3Jlc3Ncbn0gZnJvbSAnQG11aS9tYXRlcmlhbCc7XG5pbXBvcnQge1xuICBBZGQgYXMgQWRkSWNvbixcbiAgUmVmcmVzaCBhcyBSZWZyZXNoSWNvbixcbiAgRmlsdGVyTGlzdCBhcyBGaWx0ZXJJY29uLFxuICBEb3dubG9hZCBhcyBEb3dubG9hZEljb24sXG4gIFNjaGVkdWxlIGFzIFNjaGVkdWxlSWNvbixcbiAgUGxheUFycm93IGFzIFBsYXlJY29uLFxuICBTdG9wIGFzIFN0b3BJY29uLFxuICBHcm91cCBhcyBHcm91cEljb25cbn0gZnJvbSAnQG11aS9pY29ucy1tYXRlcmlhbCc7XG5pbXBvcnQgeyBEYXRlUGlja2VyIH0gZnJvbSAnQG11aS94LWRhdGUtcGlja2Vycy9EYXRlUGlja2VyJztcbmltcG9ydCB7IExvY2FsaXphdGlvblByb3ZpZGVyIH0gZnJvbSAnQG11aS94LWRhdGUtcGlja2Vycy9Mb2NhbGl6YXRpb25Qcm92aWRlcic7XG5pbXBvcnQgeyBBZGFwdGVyRGF0ZUZucyB9IGZyb20gJ0BtdWkveC1kYXRlLXBpY2tlcnMvQWRhcHRlckRhdGVGbnNWMyc7XG5pbXBvcnQgU2hpZnRUYWJsZSBmcm9tICcuLi8uLi9jb21wb25lbnRzL1NoaWZ0VGFibGUnO1xuaW1wb3J0IEFwaVNlcnZpY2UgZnJvbSAnLi4vLi4vc2VydmljZXMvQXBpU2VydmljZSc7XG5cbmNvbnN0IFNoaWZ0c1BhZ2UgPSAoKSA9PiB7XG4gIGNvbnN0IFtzaGlmdHMsIHNldFNoaWZ0c10gPSB1c2VTdGF0ZShbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpO1xuICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlKG51bGwpO1xuICBjb25zdCBbZmlsdGVycywgc2V0RmlsdGVyc10gPSB1c2VTdGF0ZSh7XG4gICAgc3RhdHVzOiAnJyxcbiAgICBhZ2VudElkOiAnJyxcbiAgICBzaXRlSWQ6ICcnLFxuICAgIHN0YXJ0RGF0ZTogbnVsbCxcbiAgICBlbmREYXRlOiBudWxsXG4gIH0pO1xuICBjb25zdCBbYWdlbnRzLCBzZXRBZ2VudHNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbc2l0ZXMsIHNldFNpdGVzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3N0YXRzLCBzZXRTdGF0c10gPSB1c2VTdGF0ZSh7XG4gICAgdG90YWw6IDAsXG4gICAgc2NoZWR1bGVkOiAwLFxuICAgIGluUHJvZ3Jlc3M6IDAsXG4gICAgY29tcGxldGVkOiAwLFxuICAgIGNhbmNlbGxlZDogMFxuICB9KTtcbiAgY29uc3QgW2NyZWF0ZURpYWxvZ09wZW4sIHNldENyZWF0ZURpYWxvZ09wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbbmV3U2hpZnQsIHNldE5ld1NoaWZ0XSA9IHVzZVN0YXRlKHtcbiAgICBhZ2VudElkOiAnJyxcbiAgICBzaXRlSWQ6ICcnLFxuICAgIHNoaWZ0RGF0ZTogbmV3IERhdGUoKSxcbiAgICBzdGFydFRpbWU6ICcnLFxuICAgIGVuZFRpbWU6ICcnLFxuICAgIHNwZWNpYWxJbnN0cnVjdGlvbnM6ICcnXG4gIH0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgbG9hZERhdGEoKTtcbiAgfSwgW2ZpbHRlcnNdKTtcblxuICBjb25zdCBsb2FkRGF0YSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICAvLyBCdWlsZCBxdWVyeSBwYXJhbWV0ZXJzXG4gICAgICBjb25zdCBwYXJhbXMgPSB7fTtcbiAgICAgIGlmIChmaWx0ZXJzLnN0YXR1cykgcGFyYW1zLnN0YXR1cyA9IGZpbHRlcnMuc3RhdHVzO1xuICAgICAgaWYgKGZpbHRlcnMuYWdlbnRJZCkgcGFyYW1zLmFnZW50SWQgPSBmaWx0ZXJzLmFnZW50SWQ7XG4gICAgICBpZiAoZmlsdGVycy5zaXRlSWQpIHBhcmFtcy5zaXRlSWQgPSBmaWx0ZXJzLnNpdGVJZDtcbiAgICAgIGlmIChmaWx0ZXJzLnN0YXJ0RGF0ZSkgcGFyYW1zLnN0YXJ0RGF0ZSA9IGZpbHRlcnMuc3RhcnREYXRlLnRvSVNPU3RyaW5nKCk7XG4gICAgICBpZiAoZmlsdGVycy5lbmREYXRlKSBwYXJhbXMuZW5kRGF0ZSA9IGZpbHRlcnMuZW5kRGF0ZS50b0lTT1N0cmluZygpO1xuXG4gICAgICBjb25zdCBbc2hpZnRzUmVzcG9uc2UsIGFnZW50c1Jlc3BvbnNlLCBzaXRlc1Jlc3BvbnNlXSA9IGF3YWl0IFByb21pc2UuYWxsKFtcbiAgICAgICAgQXBpU2VydmljZS5nZXQoJy9zaGlmdHMnLCBwYXJhbXMpLFxuICAgICAgICBBcGlTZXJ2aWNlLmdldCgnL2FnZW50cycpLFxuICAgICAgICBBcGlTZXJ2aWNlLmdldCgnL3NpdGVzJylcbiAgICAgIF0pO1xuXG4gICAgICBpZiAoc2hpZnRzUmVzcG9uc2Uuc3VjY2Vzcykge1xuICAgICAgICBzZXRTaGlmdHMoc2hpZnRzUmVzcG9uc2UuZGF0YSk7XG4gICAgICAgIFxuICAgICAgICAvLyBDYWxjdWxhdGUgc3RhdHNcbiAgICAgICAgY29uc3QgbmV3U3RhdHMgPSB7XG4gICAgICAgICAgdG90YWw6IHNoaWZ0c1Jlc3BvbnNlLmRhdGEubGVuZ3RoLFxuICAgICAgICAgIHNjaGVkdWxlZDogc2hpZnRzUmVzcG9uc2UuZGF0YS5maWx0ZXIocyA9PiBzLnN0YXR1cyA9PT0gJ1NDSEVEVUxFRCcpLmxlbmd0aCxcbiAgICAgICAgICBpblByb2dyZXNzOiBzaGlmdHNSZXNwb25zZS5kYXRhLmZpbHRlcihzID0+IHMuc3RhdHVzID09PSAnSU5fUFJPR1JFU1MnKS5sZW5ndGgsXG4gICAgICAgICAgY29tcGxldGVkOiBzaGlmdHNSZXNwb25zZS5kYXRhLmZpbHRlcihzID0+IHMuc3RhdHVzID09PSAnQ09NUExFVEVEJykubGVuZ3RoLFxuICAgICAgICAgIGNhbmNlbGxlZDogc2hpZnRzUmVzcG9uc2UuZGF0YS5maWx0ZXIocyA9PiBzLnN0YXR1cyA9PT0gJ0NBTkNFTExFRCcpLmxlbmd0aFxuICAgICAgICB9O1xuICAgICAgICBzZXRTdGF0cyhuZXdTdGF0cyk7XG4gICAgICB9XG5cbiAgICAgIGlmIChhZ2VudHNSZXNwb25zZS5zdWNjZXNzKSB7XG4gICAgICAgIHNldEFnZW50cyhhZ2VudHNSZXNwb25zZS5kYXRhKTtcbiAgICAgIH1cblxuICAgICAgaWYgKHNpdGVzUmVzcG9uc2Uuc3VjY2Vzcykge1xuICAgICAgICBzZXRTaXRlcyhzaXRlc1Jlc3BvbnNlLmRhdGEpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHNoaWZ0cyBkYXRhOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCBzaGlmdHMgZGF0YScpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlU2hpZnQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNoaWZ0RGF0YSA9IHtcbiAgICAgICAgLi4ubmV3U2hpZnQsXG4gICAgICAgIHNoaWZ0RGF0ZTogbmV3U2hpZnQuc2hpZnREYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgc3RhcnRUaW1lOiBgJHtuZXdTaGlmdC5zaGlmdERhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdfVQke25ld1NoaWZ0LnN0YXJ0VGltZX06MDBgLFxuICAgICAgICBlbmRUaW1lOiBgJHtuZXdTaGlmdC5zaGlmdERhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdfVQke25ld1NoaWZ0LmVuZFRpbWV9OjAwYFxuICAgICAgfTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBcGlTZXJ2aWNlLnBvc3QoJy9zaGlmdHMnLCBzaGlmdERhdGEpO1xuICAgICAgXG4gICAgICBpZiAocmVzcG9uc2Uuc3VjY2Vzcykge1xuICAgICAgICBzZXRDcmVhdGVEaWFsb2dPcGVuKGZhbHNlKTtcbiAgICAgICAgc2V0TmV3U2hpZnQoe1xuICAgICAgICAgIGFnZW50SWQ6ICcnLFxuICAgICAgICAgIHNpdGVJZDogJycsXG4gICAgICAgICAgc2hpZnREYXRlOiBuZXcgRGF0ZSgpLFxuICAgICAgICAgIHN0YXJ0VGltZTogJycsXG4gICAgICAgICAgZW5kVGltZTogJycsXG4gICAgICAgICAgc3BlY2lhbEluc3RydWN0aW9uczogJydcbiAgICAgICAgfSk7XG4gICAgICAgIGxvYWREYXRhKCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHNoaWZ0OicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gY3JlYXRlIHNoaWZ0Jyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUVkaXRTaGlmdCA9IGFzeW5jIChzaGlmdElkLCB1cGRhdGVEYXRhKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgQXBpU2VydmljZS5wdXQoYC9zaGlmdHMvJHtzaGlmdElkfWAsIHVwZGF0ZURhdGEpO1xuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgbG9hZERhdGEoKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgdXBkYXRpbmcgc2hpZnQ6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byB1cGRhdGUgc2hpZnQnKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlU2hpZnQgPSBhc3luYyAoc2hpZnRJZCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IEFwaVNlcnZpY2UuZGVsZXRlKGAvc2hpZnRzLyR7c2hpZnRJZH1gKTtcbiAgICAgIGlmIChyZXNwb25zZS5zdWNjZXNzKSB7XG4gICAgICAgIGxvYWREYXRhKCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIHNoaWZ0OicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIHNoaWZ0Jyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVN0YXR1c0NoYW5nZSA9IGFzeW5jIChzaGlmdElkLCBuZXdTdGF0dXMpID0+IHtcbiAgICBhd2FpdCBoYW5kbGVFZGl0U2hpZnQoc2hpZnRJZCwgeyBzdGF0dXM6IG5ld1N0YXR1cyB9KTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVFeHBvcnQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEluIGEgcmVhbCBhcHAsIHRoaXMgd291bGQgZ2VuZXJhdGUgYW5kIGRvd25sb2FkIGEgQ1NWL0V4Y2VsIGZpbGVcbiAgICAgIGNvbnNvbGUubG9nKCdFeHBvcnRpbmcgc2hpZnRzIGRhdGEuLi4nKTtcbiAgICAgIGFsZXJ0KCdFeHBvcnQgZnVuY3Rpb25hbGl0eSB3b3VsZCBiZSBpbXBsZW1lbnRlZCBoZXJlJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGV4cG9ydGluZyBkYXRhOicsIGVycm9yKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgU3RhdENhcmQgPSAoeyB0aXRsZSwgdmFsdWUsIGljb246IEljb24sIGNvbG9yID0gJ3ByaW1hcnknIH0pID0+IChcbiAgICA8Q2FyZD5cbiAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nIH19PlxuICAgICAgICAgIDxCb3g+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDRcIiBjb21wb25lbnQ9XCJkaXZcIiBjb2xvcj17YCR7Y29sb3J9Lm1haW5gfT5cbiAgICAgICAgICAgICAge3ZhbHVlfVxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0U2Vjb25kYXJ5XCI+XG4gICAgICAgICAgICAgIHt0aXRsZX1cbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgICA8SWNvbiBzeD17eyBmb250U2l6ZTogNDAsIGNvbG9yOiBgJHtjb2xvcn0ubWFpbmAsIG9wYWNpdHk6IDAuNyB9fSAvPlxuICAgICAgICA8L0JveD5cbiAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgPC9DYXJkPlxuICApO1xuXG4gIHJldHVybiAoXG4gICAgPExvY2FsaXphdGlvblByb3ZpZGVyIGRhdGVBZGFwdGVyPXtBZGFwdGVyRGF0ZUZuc30+XG4gICAgICA8Qm94IHN4PXt7IHA6IDMgfX0+XG4gICAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICAgIDxCb3ggc3g9e3sgbWI6IDMsIGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgYWxpZ25JdGVtczogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImg0XCIgZm9udFdlaWdodD1cImJvbGRcIj5cbiAgICAgICAgICAgIFNoaWZ0IE1hbmFnZW1lbnRcbiAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogMSB9fT5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcbiAgICAgICAgICAgICAgc3RhcnRJY29uPXs8RG93bmxvYWRJY29uIC8+fVxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVFeHBvcnR9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIEV4cG9ydFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXG4gICAgICAgICAgICAgIHN0YXJ0SWNvbj17PFJlZnJlc2hJY29uIC8+fVxuICAgICAgICAgICAgICBvbkNsaWNrPXtsb2FkRGF0YX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUmVmcmVzaFxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJjb250YWluZWRcIlxuICAgICAgICAgICAgICBzdGFydEljb249ezxBZGRJY29uIC8+fVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDcmVhdGVEaWFsb2dPcGVuKHRydWUpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBDcmVhdGUgU2hpZnRcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvQm94PlxuICAgICAgICA8L0JveD5cblxuICAgICAgICB7LyogRXJyb3IgQWxlcnQgKi99XG4gICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgPEFsZXJ0IHNldmVyaXR5PVwiZXJyb3JcIiBzeD17eyBtYjogMiB9fSBvbkNsb3NlPXsoKSA9PiBzZXRFcnJvcihudWxsKX0+XG4gICAgICAgICAgICB7ZXJyb3J9XG4gICAgICAgICAgPC9BbGVydD5cbiAgICAgICAgKX1cblxuICAgICAgICB7LyogU3RhdHMgQ2FyZHMgKi99XG4gICAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXszfSBzeD17eyBtYjogMyB9fT5cbiAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXsyLjR9PlxuICAgICAgICAgICAgPFN0YXRDYXJkXG4gICAgICAgICAgICAgIHRpdGxlPVwiVG90YWwgU2hpZnRzXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3N0YXRzLnRvdGFsfVxuICAgICAgICAgICAgICBpY29uPXtTY2hlZHVsZUljb259XG4gICAgICAgICAgICAgIGNvbG9yPVwicHJpbWFyeVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXsyLjR9PlxuICAgICAgICAgICAgPFN0YXRDYXJkXG4gICAgICAgICAgICAgIHRpdGxlPVwiU2NoZWR1bGVkXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3N0YXRzLnNjaGVkdWxlZH1cbiAgICAgICAgICAgICAgaWNvbj17U2NoZWR1bGVJY29ufVxuICAgICAgICAgICAgICBjb2xvcj1cImluZm9cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17Mi40fT5cbiAgICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgICB0aXRsZT1cIkluIFByb2dyZXNzXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3N0YXRzLmluUHJvZ3Jlc3N9XG4gICAgICAgICAgICAgIGljb249e1BsYXlJY29ufVxuICAgICAgICAgICAgICBjb2xvcj1cInN1Y2Nlc3NcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17Mi40fT5cbiAgICAgICAgICAgIDxTdGF0Q2FyZFxuICAgICAgICAgICAgICB0aXRsZT1cIkNvbXBsZXRlZFwiXG4gICAgICAgICAgICAgIHZhbHVlPXtzdGF0cy5jb21wbGV0ZWR9XG4gICAgICAgICAgICAgIGljb249e1N0b3BJY29ufVxuICAgICAgICAgICAgICBjb2xvcj1cInNlY29uZGFyeVwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXsyLjR9PlxuICAgICAgICAgICAgPFN0YXRDYXJkXG4gICAgICAgICAgICAgIHRpdGxlPVwiQ2FuY2VsbGVkXCJcbiAgICAgICAgICAgICAgdmFsdWU9e3N0YXRzLmNhbmNlbGxlZH1cbiAgICAgICAgICAgICAgaWNvbj17U3RvcEljb259XG4gICAgICAgICAgICAgIGNvbG9yPVwiZXJyb3JcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDwvR3JpZD5cblxuICAgICAgICB7LyogRmlsdGVycyAqL31cbiAgICAgICAgPFBhcGVyIHN4PXt7IHA6IDIsIG1iOiAzIH19PlxuICAgICAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXsyfSBhbGlnbkl0ZW1zPVwiY2VudGVyXCI+XG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXsyfT5cbiAgICAgICAgICAgICAgPEZvcm1Db250cm9sIGZ1bGxXaWR0aCBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICAgICAgICA8SW5wdXRMYWJlbD5TdGF0dXM8L0lucHV0TGFiZWw+XG4gICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZpbHRlcnMuc3RhdHVzfVxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJTdGF0dXNcIlxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgc3RhdHVzOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIHZhbHVlPVwiXCI+QWxsPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSB2YWx1ZT1cIlNDSEVEVUxFRFwiPlNjaGVkdWxlZDwvTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgICA8TWVudUl0ZW0gdmFsdWU9XCJJTl9QUk9HUkVTU1wiPkluIFByb2dyZXNzPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSB2YWx1ZT1cIkNPTVBMRVRFRFwiPkNvbXBsZXRlZDwvTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgICA8TWVudUl0ZW0gdmFsdWU9XCJDQU5DRUxMRURcIj5DYW5jZWxsZWQ8L01lbnVJdGVtPlxuICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICA8L0Zvcm1Db250cm9sPlxuICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXsyfT5cbiAgICAgICAgICAgICAgPEZvcm1Db250cm9sIGZ1bGxXaWR0aCBzaXplPVwic21hbGxcIj5cbiAgICAgICAgICAgICAgICA8SW5wdXRMYWJlbD5BZ2VudDwvSW5wdXRMYWJlbD5cbiAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5hZ2VudElkfVxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJBZ2VudFwiXG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEZpbHRlcnMocHJldiA9PiAoeyAuLi5wcmV2LCBhZ2VudElkOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIHZhbHVlPVwiXCI+QWxsIEFnZW50czwvTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgICB7YWdlbnRzLm1hcCgoYWdlbnQpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIGtleT17YWdlbnQuaWR9IHZhbHVlPXthZ2VudC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAge2FnZW50LnVzZXIgPyBgJHthZ2VudC51c2VyLmZpcnN0TmFtZX0gJHthZ2VudC51c2VyLmxhc3ROYW1lfWAgOiAnVW5rbm93bid9XG4gICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cbiAgICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17Mn0+XG4gICAgICAgICAgICAgIDxGb3JtQ29udHJvbCBmdWxsV2lkdGggc2l6ZT1cInNtYWxsXCI+XG4gICAgICAgICAgICAgICAgPElucHV0TGFiZWw+U2l0ZTwvSW5wdXRMYWJlbD5cbiAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5zaXRlSWR9XG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlNpdGVcIlxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgc2l0ZUlkOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIHZhbHVlPVwiXCI+QWxsIFNpdGVzPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgIHtzaXRlcy5tYXAoKHNpdGUpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIGtleT17c2l0ZS5pZH0gdmFsdWU9e3NpdGUuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgIHtzaXRlLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgIDwvTWVudUl0ZW0+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgPC9Gb3JtQ29udHJvbD5cbiAgICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17Mn0+XG4gICAgICAgICAgICAgIDxEYXRlUGlja2VyXG4gICAgICAgICAgICAgICAgbGFiZWw9XCJTdGFydCBEYXRlXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5zdGFydERhdGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhkYXRlKSA9PiBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgc3RhcnREYXRlOiBkYXRlIH0pKX1cbiAgICAgICAgICAgICAgICBzbG90UHJvcHM9e3sgdGV4dEZpZWxkOiB7IHNpemU6ICdzbWFsbCcsIGZ1bGxXaWR0aDogdHJ1ZSB9IH19XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezJ9PlxuICAgICAgICAgICAgICA8RGF0ZVBpY2tlclxuICAgICAgICAgICAgICAgIGxhYmVsPVwiRW5kIERhdGVcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzLmVuZERhdGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhkYXRlKSA9PiBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgZW5kRGF0ZTogZGF0ZSB9KSl9XG4gICAgICAgICAgICAgICAgc2xvdFByb3BzPXt7IHRleHRGaWVsZDogeyBzaXplOiAnc21hbGwnLCBmdWxsV2lkdGg6IHRydWUgfSB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAgXG4gICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXsyfT5cbiAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXG4gICAgICAgICAgICAgICAgZnVsbFdpZHRoXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RmlsdGVycyh7XG4gICAgICAgICAgICAgICAgICBzdGF0dXM6ICcnLFxuICAgICAgICAgICAgICAgICAgYWdlbnRJZDogJycsXG4gICAgICAgICAgICAgICAgICBzaXRlSWQ6ICcnLFxuICAgICAgICAgICAgICAgICAgc3RhcnREYXRlOiBudWxsLFxuICAgICAgICAgICAgICAgICAgZW5kRGF0ZTogbnVsbFxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgQ2xlYXIgRmlsdGVyc1xuICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDwvUGFwZXI+XG5cbiAgICAgICAgey8qIFNoaWZ0cyBUYWJsZSAqL31cbiAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJywgcHk6IDQgfX0+XG4gICAgICAgICAgICA8Q2lyY3VsYXJQcm9ncmVzcyAvPlxuICAgICAgICAgIDwvQm94PlxuICAgICAgICApIDogKFxuICAgICAgICAgIDxTaGlmdFRhYmxlXG4gICAgICAgICAgICBzaGlmdHM9e3NoaWZ0c31cbiAgICAgICAgICAgIGxvYWRpbmc9e2xvYWRpbmd9XG4gICAgICAgICAgICBvbkVkaXQ9e2hhbmRsZUVkaXRTaGlmdH1cbiAgICAgICAgICAgIG9uRGVsZXRlPXtoYW5kbGVEZWxldGVTaGlmdH1cbiAgICAgICAgICAgIG9uU3RhdHVzQ2hhbmdlPXtoYW5kbGVTdGF0dXNDaGFuZ2V9XG4gICAgICAgICAgICBvblZpZXc9eyhzaGlmdElkKSA9PiBjb25zb2xlLmxvZygnVmlldyBzaGlmdDonLCBzaGlmdElkKX1cbiAgICAgICAgICAvPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBDcmVhdGUgU2hpZnQgRGlhbG9nICovfVxuICAgICAgICA8RGlhbG9nIG9wZW49e2NyZWF0ZURpYWxvZ09wZW59IG9uQ2xvc2U9eygpID0+IHNldENyZWF0ZURpYWxvZ09wZW4oZmFsc2UpfSBtYXhXaWR0aD1cIm1kXCIgZnVsbFdpZHRoPlxuICAgICAgICAgIDxEaWFsb2dUaXRsZT5DcmVhdGUgTmV3IFNoaWZ0PC9EaWFsb2dUaXRsZT5cbiAgICAgICAgICA8RGlhbG9nQ29udGVudD5cbiAgICAgICAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXsyfSBzeD17eyBtdDogMSB9fT5cbiAgICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fT5cbiAgICAgICAgICAgICAgICA8Rm9ybUNvbnRyb2wgZnVsbFdpZHRoPlxuICAgICAgICAgICAgICAgICAgPElucHV0TGFiZWw+QWdlbnQ8L0lucHV0TGFiZWw+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdTaGlmdC5hZ2VudElkfVxuICAgICAgICAgICAgICAgICAgICBsYWJlbD1cIkFnZW50XCJcbiAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdTaGlmdChwcmV2ID0+ICh7IC4uLnByZXYsIGFnZW50SWQ6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2FnZW50cy5tYXAoKGFnZW50KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgPE1lbnVJdGVtIGtleT17YWdlbnQuaWR9IHZhbHVlPXthZ2VudC5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7YWdlbnQudXNlciA/IGAke2FnZW50LnVzZXIuZmlyc3ROYW1lfSAke2FnZW50LnVzZXIubGFzdE5hbWV9YCA6ICdVbmtub3duJ31cbiAgICAgICAgICAgICAgICAgICAgICA8L01lbnVJdGVtPlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgICAgICAgIDwvRm9ybUNvbnRyb2w+XG4gICAgICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0+XG4gICAgICAgICAgICAgICAgPEZvcm1Db250cm9sIGZ1bGxXaWR0aD5cbiAgICAgICAgICAgICAgICAgIDxJbnB1dExhYmVsPlNpdGU8L0lucHV0TGFiZWw+XG4gICAgICAgICAgICAgICAgICA8U2VsZWN0XG4gICAgICAgICAgICAgICAgICAgIHZhbHVlPXtuZXdTaGlmdC5zaXRlSWR9XG4gICAgICAgICAgICAgICAgICAgIGxhYmVsPVwiU2l0ZVwiXG4gICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3U2hpZnQocHJldiA9PiAoeyAuLi5wcmV2LCBzaXRlSWQ6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge3NpdGVzLm1hcCgoc2l0ZSkgPT4gKFxuICAgICAgICAgICAgICAgICAgICAgIDxNZW51SXRlbSBrZXk9e3NpdGUuaWR9IHZhbHVlPXtzaXRlLmlkfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtzaXRlLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICA8L0Zvcm1Db250cm9sPlxuICAgICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezR9PlxuICAgICAgICAgICAgICAgIDxEYXRlUGlja2VyXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlNoaWZ0IERhdGVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld1NoaWZ0LnNoaWZ0RGF0ZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZGF0ZSkgPT4gc2V0TmV3U2hpZnQocHJldiA9PiAoeyAuLi5wcmV2LCBzaGlmdERhdGU6IGRhdGUgfSkpfVxuICAgICAgICAgICAgICAgICAgc2xvdFByb3BzPXt7IHRleHRGaWVsZDogeyBmdWxsV2lkdGg6IHRydWUgfSB9fVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvR3JpZD5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17NH0+XG4gICAgICAgICAgICAgICAgPFRleHRGaWVsZFxuICAgICAgICAgICAgICAgICAgZnVsbFdpZHRoXG4gICAgICAgICAgICAgICAgICBsYWJlbD1cIlN0YXJ0IFRpbWVcIlxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRpbWVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e25ld1NoaWZ0LnN0YXJ0VGltZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3U2hpZnQocHJldiA9PiAoeyAuLi5wcmV2LCBzdGFydFRpbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgICAgIElucHV0TGFiZWxQcm9wcz17eyBzaHJpbms6IHRydWUgfX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L0dyaWQ+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezR9PlxuICAgICAgICAgICAgICAgIDxUZXh0RmllbGRcbiAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJFbmQgVGltZVwiXG4gICAgICAgICAgICAgICAgICB0eXBlPVwidGltZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3U2hpZnQuZW5kVGltZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3U2hpZnQocHJldiA9PiAoeyAuLi5wcmV2LCBlbmRUaW1lOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgICBJbnB1dExhYmVsUHJvcHM9e3sgc2hyaW5rOiB0cnVlIH19XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9PlxuICAgICAgICAgICAgICAgIDxUZXh0RmllbGRcbiAgICAgICAgICAgICAgICAgIGZ1bGxXaWR0aFxuICAgICAgICAgICAgICAgICAgbGFiZWw9XCJTcGVjaWFsIEluc3RydWN0aW9uc1wiXG4gICAgICAgICAgICAgICAgICBtdWx0aWxpbmVcbiAgICAgICAgICAgICAgICAgIHJvd3M9ezN9XG4gICAgICAgICAgICAgICAgICB2YWx1ZT17bmV3U2hpZnQuc3BlY2lhbEluc3RydWN0aW9uc31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3U2hpZnQocHJldiA9PiAoeyAuLi5wcmV2LCBzcGVjaWFsSW5zdHJ1Y3Rpb25zOiBlLnRhcmdldC52YWx1ZSB9KSl9XG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgICAgPC9HcmlkPlxuICAgICAgICAgIDwvRGlhbG9nQ29udGVudD5cbiAgICAgICAgICA8RGlhbG9nQWN0aW9ucz5cbiAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17KCkgPT4gc2V0Q3JlYXRlRGlhbG9nT3BlbihmYWxzZSl9PkNhbmNlbDwvQnV0dG9uPlxuICAgICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVDcmVhdGVTaGlmdH0gdmFyaWFudD1cImNvbnRhaW5lZFwiPkNyZWF0ZSBTaGlmdDwvQnV0dG9uPlxuICAgICAgICAgIDwvRGlhbG9nQWN0aW9ucz5cbiAgICAgICAgPC9EaWFsb2c+XG4gICAgICA8L0JveD5cbiAgICA8L0xvY2FsaXphdGlvblByb3ZpZGVyPlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgU2hpZnRzUGFnZTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/shifts/page.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/node-fetch-native","vendor-chunks/@clerk","vendor-chunks/@peculiar","vendor-chunks/asn1js","vendor-chunks/@opentelemetry","vendor-chunks/webcrypto-core","vendor-chunks/swr","vendor-chunks/pvtsutils","vendor-chunks/tslib","vendor-chunks/pvutils","vendor-chunks/cookie","vendor-chunks/deepmerge","vendor-chunks/use-sync-external-store","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/react-is","vendor-chunks/hoist-non-react-statics","vendor-chunks/@babel","vendor-chunks/object-assign","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/react-transition-group","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@popperjs","vendor-chunks/date-fns","vendor-chunks/dom-helpers"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fshifts%2Fpage&page=%2Fshifts%2Fpage&appPaths=%2Fshifts%2Fpage&pagePath=private-next-app-dir%2Fshifts%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();