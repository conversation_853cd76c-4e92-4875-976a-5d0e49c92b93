// BahinLink Web Admin - Agents Management Page
// ⚠️ CRITICAL: Real agent management with live GPS tracking ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Typography,
  Button,
  CircularProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Menu
} from '@mui/material';
import {
  Add,
  Edit,
  LocationOn,
  Phone,
  Email,
  Refresh,
  Download,
  MoreVert
} from '@mui/icons-material';
import ModernSidebar from '../../components/ModernSidebar';

export default function AgentsPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [agents, setAgents] = useState([]);
  const [filteredAgents, setFilteredAgents] = useState([]);
  const [error, setError] = useState(null);
  const [refreshing, setRefreshing] = useState(false);
  
  // Filters and pagination
  const [filters, setFilters] = useState({
    status: '',
    isAvailable: '',
    search: ''
  });
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0
  });

  // Dialog states
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [mapViewOpen, setMapViewOpen] = useState(false);

  // Form data
  const [formData, setFormData] = useState({
    employeeId: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    certifications: [],
    skills: [],
    emergencyContactName: '',
    emergencyContactPhone: ''
  });

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadAgents();
      
      // Auto-refresh every 30 seconds for real-time updates
      const interval = setInterval(loadAgents, 30000);
      return () => clearInterval(interval);
    }
  }, [isLoaded, isSignedIn, filters, pagination.page]);

  const loadAgents = async () => {
    try {
      setError(null);
      
      const params = {
        page: pagination.page,
        limit: pagination.limit,
        includeLocation: 'true',
        includePerformance: 'true',
        ...filters
      };

      const response = await fetch('/api/agents');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setAgents(data.agents || []);
      setFilteredAgents(data.agents || []);
      setPagination(prev => ({
        ...prev,
        total: data.total || 0
      }));
    } catch (error) {
      console.error('Load agents error:', error);
      setError(error.message || 'Failed to load agents');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAgents();
  };

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleCreateAgent = async () => {
    try {
      // First create user, then agent profile
      const userResponse = await ApiService.post('/users', {
        email: formData.email,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        role: 'AGENT'
      });

      if (userResponse.success) {
        const agentResponse = await ApiService.post('/agents', {
          userId: userResponse.data.id,
          employeeId: formData.employeeId,
          certifications: formData.certifications,
          skills: formData.skills,
          emergencyContactName: formData.emergencyContactName,
          emergencyContactPhone: formData.emergencyContactPhone
        });

        if (agentResponse.success) {
          setCreateDialogOpen(false);
          setFormData({
            employeeId: '',
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            certifications: [],
            skills: [],
            emergencyContactName: '',
            emergencyContactPhone: ''
          });
          await loadAgents();
        }
      }
    } catch (error) {
      console.error('Create agent error:', error);
      setError(error.message || 'Failed to create agent');
    }
  };

  const handleEditAgent = (agent) => {
    setSelectedAgent(agent);
    setFormData({
      employeeId: agent.employeeId,
      firstName: agent.user.firstName,
      lastName: agent.user.lastName,
      email: agent.user.email,
      phone: agent.user.phone,
      certifications: agent.certifications,
      skills: agent.skills,
      emergencyContactName: agent.emergencyContact.name,
      emergencyContactPhone: agent.emergencyContact.phone
    });
    setEditDialogOpen(true);
  };

  const handleUpdateAgent = async () => {
    try {
      // Update user information
      await ApiService.put(`/users/${selectedAgent.user.id}`, {
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        agentData: {
          certifications: formData.certifications,
          skills: formData.skills
        }
      });

      // Update agent-specific information
      await ApiService.put(`/agents/${selectedAgent.id}`, {
        emergencyContactName: formData.emergencyContactName,
        emergencyContactPhone: formData.emergencyContactPhone
      });

      setEditDialogOpen(false);
      setSelectedAgent(null);
      await loadAgents();
    } catch (error) {
      console.error('Update agent error:', error);
      setError(error.message || 'Failed to update agent');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ON_SHIFT': return 'success';
      case 'AVAILABLE': return 'info';
      case 'OFFLINE': return 'default';
      default: return 'default';
    }
  };

  const getPerformanceColor = (score) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'error';
  };

  if (!isLoaded || loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <CircularProgress size={40} sx={{ color: '#6b7280' }} />
      </Box>
    );
  }

  const handleExport = () => {
    if (agents.length > 0) {
      const dataStr = JSON.stringify(agents, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

      const exportFileDefaultName = `agents-${new Date().toISOString().split('T')[0]}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    }
  };

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#ffffff' }}>
      <ModernSidebar />

      <Box component="main" sx={{ flexGrow: 1, p: 4 }}>
        {/* Clean Header */}
        <Box sx={{ mb: 6, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 300,
                color: '#1a1a1a',
                mb: 1,
                letterSpacing: '-0.02em',
                fontSize: '2.5rem'
              }}
            >
              Agents
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#6b7280',
                fontSize: '16px'
              }}
            >
              Manage security agents and track performance
            </Typography>
          </Box>

          {/* Clean Controls */}
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<Download />}
              onClick={handleExport}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Export
            </Button>

            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={handleRefresh}
              disabled={refreshing}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              {refreshing ? 'Refreshing...' : 'Refresh'}
            </Button>

            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => setCreateDialogOpen(true)}
              sx={{
                backgroundColor: '#3b82f6',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: '#2563eb'
                }
              }}
            >
              Add Agent
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Minimalist KPI Grid */}
        <Box sx={{ mb: 8 }}>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(240px, 1fr))',
              gap: 4,
              mb: 6
            }}
          >
            {/* Total Agents */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {agents.length}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Total Agents
              </Typography>
            </Box>

            {/* Active Agents */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {agents.filter(a => a.isActive).length}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Active Agents
              </Typography>
            </Box>

            {/* Available Agents */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {agents.filter(a => a.isAvailable).length}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Available Now
              </Typography>
            </Box>

            {/* Average Rating */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {agents.length > 0 ?
                  (agents.reduce((sum, a) => sum + (a.performance?.averageRating || 0), 0) / agents.length).toFixed(1)
                  : '0.0'
                }
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500,
                mb: 1
              }}>
                Average Rating
              </Typography>
            </Box>
          </Box>
        </Box>
        {/* Clean Agents Table */}
        <Box sx={{
          borderRadius: 3,
          border: '1px solid #f1f5f9',
          backgroundColor: '#ffffff',
          overflow: 'hidden'
        }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Agent
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Contact
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Status
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Performance
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Location
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px', width: 60 }}>
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {agents.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography variant="body2" color="textSecondary">
                        No agents found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  agents.map((agent) => (
                    <TableRow
                      key={agent.id}
                      sx={{
                        '&:hover': { backgroundColor: '#f9fafb' },
                        borderBottom: '1px solid #f1f5f9'
                      }}
                    >
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500, color: '#1a1a1a' }}>
                            {agent.firstName} {agent.lastName}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#6b7280' }}>
                            ID: {agent.employeeId}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                            <Email sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                            {agent.email}
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                            <Phone sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                            {agent.phone}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Chip
                            label={agent.isActive ? 'Active' : 'Inactive'}
                            size="small"
                            sx={{
                              backgroundColor: agent.isActive ? '#10b981' : '#6b7280',
                              color: 'white',
                              fontSize: '12px',
                              mb: 0.5
                            }}
                          />
                          <br />
                          <Chip
                            label={agent.isAvailable ? 'Available' : 'Busy'}
                            size="small"
                            sx={{
                              backgroundColor: agent.isAvailable ? '#3b82f6' : '#f59e0b',
                              color: 'white',
                              fontSize: '12px'
                            }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                            Rating: {agent.performance?.averageRating || 0}/5
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#6b7280' }}>
                            {agent.performance?.completionRate || 0}% completion
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                            <LocationOn sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                            {agent.currentLocation?.address || 'Unknown'}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#6b7280' }}>
                            {agent.lastLocationUpdate ?
                              new Date(agent.lastLocationUpdate).toLocaleTimeString() :
                              'No update'
                            }
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <IconButton
                          size="small"
                          onClick={() => handleEditAgent(agent)}
                          sx={{ color: '#6b7280' }}
                        >
                          <Edit />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>
    </Box>
  );
}
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Total Agents
              </Typography>
              <Typography variant="h4" component="div">
                {agents.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                On Shift
              </Typography>
              <Typography variant="h4" component="div" color="success.main">
                {agents.filter(a => a.status === 'ON_SHIFT').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                Available
              </Typography>
              <Typography variant="h4" component="div" color="info.main">
                {agents.filter(a => a.status === 'AVAILABLE').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography color="text.secondary" gutterBottom>
                With GPS
              </Typography>
              <Typography variant="h4" component="div" color="primary.main">
                {agents.filter(a => a.location).length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                label="Search"
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Employee ID, name, email..."
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="ON_SHIFT">On Shift</MenuItem>
                  <MenuItem value="AVAILABLE">Available</MenuItem>
                  <MenuItem value="OFFLINE">Offline</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Availability</InputLabel>
                <Select
                  value={filters.isAvailable}
                  onChange={(e) => handleFilterChange('isAvailable', e.target.value)}
                  label="Availability"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="true">Available</MenuItem>
                  <MenuItem value="false">Unavailable</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<Download />}
                onClick={() => {/* Export functionality */}}
              >
                Export
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Agents Table */}
      <AgentTable
        agents={filteredAgents}
        onEdit={handleEditAgent}
        onRefresh={loadAgents}
        pagination={pagination}
        onPageChange={(page) => setPagination(prev => ({ ...prev, page }))}
      />

      {/* Create Agent Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Agent</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Employee ID"
                value={formData.employeeId}
                onChange={(e) => setFormData(prev => ({ ...prev, employeeId: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Name"
                value={formData.emergencyContactName}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContactName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Emergency Contact Phone"
                value={formData.emergencyContactPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContactPhone: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateAgent} variant="contained">Create Agent</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Agent Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Agent</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Employee ID"
                value={formData.employeeId}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                value={formData.email}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="First Name"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Last Name"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Emergency Contact Name"
                value={formData.emergencyContactName}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContactName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Emergency Contact Phone"
                value={formData.emergencyContactPhone}
                onChange={(e) => setFormData(prev => ({ ...prev, emergencyContactPhone: e.target.value }))}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleUpdateAgent} variant="contained">Update Agent</Button>
        </DialogActions>
      </Dialog>

      {/* Map View Dialog */}
      <Dialog open={mapViewOpen} onClose={() => setMapViewOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>Agent Locations Map</DialogTitle>
        <DialogContent>
          <Box height={600}>
            <RealTimeMap agentLocations={agents.filter(a => a.location).map(agent => ({
              id: agent.id,
              agentName: agent.name,
              employeeId: agent.employeeId,
              latitude: agent.location?.latitude || 14.6928,
              longitude: agent.location?.longitude || -17.4467,
              status: agent.status,
              siteName: agent.currentSite || 'Unknown Site',
              lastUpdate: agent.lastUpdate || new Date().toISOString()
            }))} />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMapViewOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
      </Box>
    </Box>
  );
}
