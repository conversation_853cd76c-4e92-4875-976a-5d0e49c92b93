import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Global Prisma instance for serverless optimization
const globalForPrisma = globalThis;

const prisma = globalForPrisma.prisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],
});

// Prevent multiple instances in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || '7d';
    
    // Calculate date range
    const now = new Date();
    let startDate = new Date();
    
    switch (timeRange) {
      case '1d':
        startDate.setDate(now.getDate() - 1);
        break;
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      default:
        startDate.setDate(now.getDate() - 7);
    }

    // Fetch analytics data from database with error handling
    let totalShifts, completedShifts, cancelledShifts, activeAgents, totalReports, sites, shifts, reports;

    try {
      [
        totalShifts,
        completedShifts,
        cancelledShifts,
        activeAgents,
        totalReports,
        sites,
        shifts,
        reports
      ] = await Promise.all([
      // Total shifts in time range
      prisma.shift.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: now
          }
        }
      }),
      
      // Completed shifts
      prisma.shift.count({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: startDate,
            lte: now
          }
        }
      }),
      
      // Cancelled shifts
      prisma.shift.count({
        where: {
          status: 'CANCELLED',
          createdAt: {
            gte: startDate,
            lte: now
          }
        }
      }),
      
      // Active agents (agents with shifts in time range)
      prisma.user.count({
        where: {
          role: 'AGENT',
          shifts: {
            some: {
              createdAt: {
                gte: startDate,
                lte: now
              }
            }
          }
        }
      }),
      
      // Total reports in time range
      prisma.report.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: now
          }
        }
      }),
      
      // Total active sites
      prisma.site.count({
        where: {
          status: 'ACTIVE'
        }
      }),
      
      // Shifts with details for calculations
      prisma.shift.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: now
          }
        },
        include: {
          agent: true,
          site: true
        }
      }),
      
      // Reports for analysis
      prisma.report.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: now
          }
        },
        include: {
          agent: true,
          shift: true
        }
      })
    ]);
    } catch (dbError) {
      console.error('Database query error:', dbError);
      // Return sample data for demonstration when database is not available
      return NextResponse.json({
        kpis: {
          totalShifts: 156,
          completedShifts: 142,
          cancelledShifts: 14,
          activeAgents: 18,
          activeSites: 12,
          totalReports: 89,
          totalHours: 1248,
          averageRating: 4.7,
          responseTime: 12.5,
          efficiency: 91.0,
          completionRate: 91.0
        },
        trends: {
          shifts: [
            { date: '2024-01-15', completed: 12, cancelled: 2, total: 14 },
            { date: '2024-01-16', completed: 15, cancelled: 1, total: 16 },
            { date: '2024-01-17', completed: 18, cancelled: 0, total: 18 },
            { date: '2024-01-18', completed: 14, cancelled: 3, total: 17 },
            { date: '2024-01-19', completed: 16, cancelled: 1, total: 17 },
            { date: '2024-01-20', completed: 20, cancelled: 2, total: 22 },
            { date: '2024-01-21', completed: 17, cancelled: 1, total: 18 }
          ],
          agentPerformance: [
            { name: 'Amadou Ba', totalShifts: 15, completedShifts: 14, onTimeShifts: 13 },
            { name: 'Fatou Sow', totalShifts: 12, completedShifts: 11, onTimeShifts: 10 },
            { name: 'Moussa Diop', totalShifts: 18, completedShifts: 16, onTimeShifts: 15 }
          ],
          siteActivity: [
            { name: 'Sonatel HQ', shifts: 25, incidents: 3 },
            { name: 'CBAO Bank Dakar', shifts: 20, incidents: 1 },
            { name: 'Orange Senegal', shifts: 18, incidents: 2 }
          ]
        },
        timeRange,
        lastUpdated: new Date().toISOString(),
        note: 'Sample data - Database connection pending'
      });
    }

    // Calculate total hours worked
    const totalHours = shifts.reduce((total, shift) => {
      if (shift.endTime && shift.startTime) {
        const hours = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);
        return total + hours;
      }
      return total;
    }, 0);

    // Calculate average rating from reports
    const ratingsReports = reports.filter(report => report.rating && report.rating > 0);
    const averageRating = ratingsReports.length > 0 
      ? ratingsReports.reduce((sum, report) => sum + report.rating, 0) / ratingsReports.length
      : 0;

    // Calculate average response time (mock calculation for now)
    const responseTime = 12.5; // This would need actual incident response data

    // Calculate efficiency rate
    const efficiency = totalShifts > 0 ? (completedShifts / totalShifts) * 100 : 0;

    // Prepare shift trends data
    const shiftTrends = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dayStart = new Date(date.setHours(0, 0, 0, 0));
      const dayEnd = new Date(date.setHours(23, 59, 59, 999));
      
      const dayShifts = shifts.filter(shift => 
        new Date(shift.createdAt) >= dayStart && new Date(shift.createdAt) <= dayEnd
      );
      
      const completed = dayShifts.filter(shift => shift.status === 'COMPLETED').length;
      const cancelled = dayShifts.filter(shift => shift.status === 'CANCELLED').length;
      
      shiftTrends.push({
        date: dayStart.toISOString().split('T')[0],
        completed,
        cancelled,
        total: dayShifts.length
      });
    }

    // Agent performance data
    const agentPerformance = {};
    shifts.forEach(shift => {
      if (shift.agent) {
        const agentName = shift.agent.name;
        if (!agentPerformance[agentName]) {
          agentPerformance[agentName] = {
            totalShifts: 0,
            completedShifts: 0,
            onTimeShifts: 0
          };
        }
        agentPerformance[agentName].totalShifts++;
        if (shift.status === 'COMPLETED') {
          agentPerformance[agentName].completedShifts++;
          // Assume on-time if completed (would need actual timing data)
          agentPerformance[agentName].onTimeShifts++;
        }
      }
    });

    // Site activity data
    const siteActivity = {};
    shifts.forEach(shift => {
      if (shift.site) {
        const siteName = shift.site.name;
        if (!siteActivity[siteName]) {
          siteActivity[siteName] = {
            shifts: 0,
            incidents: 0
          };
        }
        siteActivity[siteName].shifts++;
      }
    });

    reports.forEach(report => {
      if (report.shift && report.shift.site) {
        const siteName = report.shift.site.name;
        if (siteActivity[siteName]) {
          siteActivity[siteName].incidents++;
        }
      }
    });

    const analyticsData = {
      kpis: {
        totalShifts,
        completedShifts,
        cancelledShifts,
        activeAgents,
        activeSites: sites,
        totalReports,
        totalHours: Math.round(totalHours),
        averageRating: Math.round(averageRating * 10) / 10,
        responseTime,
        efficiency: Math.round(efficiency * 10) / 10,
        completionRate: totalShifts > 0 ? Math.round((completedShifts / totalShifts) * 1000) / 10 : 0
      },
      trends: {
        shifts: shiftTrends,
        agentPerformance: Object.entries(agentPerformance).map(([name, data]) => ({
          name,
          ...data
        })),
        siteActivity: Object.entries(siteActivity).map(([name, data]) => ({
          name,
          ...data
        }))
      },
      timeRange,
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json(analyticsData);
    
  } catch (error) {
    console.error('Analytics API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data', details: error.message },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
