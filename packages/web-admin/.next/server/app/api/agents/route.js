"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/agents/route";
exports.ids = ["app/api/agents/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _workspaces_finalagent_main_final_packages_web_admin_src_app_api_agents_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/agents/route.js */ \"(rsc)/./src/app/api/agents/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/agents/route\",\n        pathname: \"/api/agents\",\n        filename: \"route\",\n        bundlePath: \"app/api/agents/route\"\n    },\n    resolvedPagePath: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/api/agents/route.js\",\n    nextConfigOutput,\n    userland: _workspaces_finalagent_main_final_packages_web_admin_src_app_api_agents_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/agents/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/agents/route.js":
/*!*************************************!*\
  !*** ./src/app/api/agents/route.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Global Prisma instance for serverless optimization\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient({\n    datasources: {\n        db: {\n            url: process.env.DATABASE_URL\n        }\n    },\n    log:  true ? [\n        \"error\"\n    ] : 0\n});\n// Prevent multiple instances in development\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\") || \"1\");\n        const limit = parseInt(searchParams.get(\"limit\") || \"50\");\n        const status = searchParams.get(\"status\");\n        const search = searchParams.get(\"search\");\n        let agentsData;\n        try {\n            // Build where clause for filtering\n            const where = {\n                role: \"AGENT\",\n                ...status && {\n                    isActive: status === \"ACTIVE\"\n                },\n                ...search && {\n                    OR: [\n                        {\n                            firstName: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        },\n                        {\n                            lastName: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        },\n                        {\n                            email: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        }\n                    ]\n                }\n            };\n            // Fetch agents with related data\n            const [agents, totalCount] = await Promise.all([\n                prisma.user.findMany({\n                    where,\n                    include: {\n                        agent: {\n                            include: {\n                                shifts: {\n                                    where: {\n                                        createdAt: {\n                                            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                                        }\n                                    },\n                                    orderBy: {\n                                        createdAt: \"desc\"\n                                    },\n                                    take: 5\n                                },\n                                reports: {\n                                    where: {\n                                        createdAt: {\n                                            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                                        }\n                                    },\n                                    orderBy: {\n                                        createdAt: \"desc\"\n                                    },\n                                    take: 5\n                                }\n                            }\n                        }\n                    },\n                    skip: (page - 1) * limit,\n                    take: limit,\n                    orderBy: {\n                        createdAt: \"desc\"\n                    }\n                }),\n                prisma.user.count({\n                    where\n                })\n            ]);\n            // Calculate performance metrics for each agent\n            const agentsWithMetrics = agents.map((user)=>{\n                const agent = user.agent;\n                if (!agent) return null;\n                const shifts = agent.shifts || [];\n                const reports = agent.reports || [];\n                // Calculate completion rate\n                const completedShifts = shifts.filter((s)=>s.status === \"COMPLETED\").length;\n                const completionRate = shifts.length > 0 ? completedShifts / shifts.length * 100 : 0;\n                // Calculate average rating from reports\n                const ratingsReports = reports.filter((r)=>r.rating && r.rating > 0);\n                const averageRating = ratingsReports.length > 0 ? ratingsReports.reduce((sum, r)=>sum + r.rating, 0) / ratingsReports.length : 0;\n                // Calculate total hours worked\n                const totalHours = shifts.reduce((total, shift)=>{\n                    if (shift.endTime && shift.startTime) {\n                        const hours = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);\n                        return total + hours;\n                    }\n                    return total;\n                }, 0);\n                return {\n                    id: user.id,\n                    employeeId: agent.employeeId,\n                    firstName: user.firstName,\n                    lastName: user.lastName,\n                    email: user.email,\n                    phone: user.phone,\n                    isActive: user.isActive,\n                    isAvailable: agent.isAvailable,\n                    certifications: agent.certifications || [],\n                    skills: agent.skills || [],\n                    emergencyContactName: agent.emergencyContactName,\n                    emergencyContactPhone: agent.emergencyContactPhone,\n                    currentLocation: agent.currentLocation,\n                    lastLocationUpdate: agent.lastLocationUpdate,\n                    performance: {\n                        totalShifts: shifts.length,\n                        completedShifts,\n                        completionRate: Math.round(completionRate * 10) / 10,\n                        totalReports: reports.length,\n                        averageRating: Math.round(averageRating * 10) / 10,\n                        totalHours: Math.round(totalHours)\n                    },\n                    recentShifts: shifts.slice(0, 3),\n                    recentReports: reports.slice(0, 3),\n                    createdAt: user.createdAt,\n                    updatedAt: user.updatedAt\n                };\n            }).filter(Boolean);\n            agentsData = {\n                agents: agentsWithMetrics,\n                total: totalCount,\n                page,\n                limit,\n                totalPages: Math.ceil(totalCount / limit),\n                lastUpdated: new Date().toISOString()\n            };\n        } catch (dbError) {\n            console.error(\"Database query error:\", dbError);\n            // Return sample data for demonstration when database is not available\n            agentsData = {\n                agents: [\n                    {\n                        id: \"1\",\n                        employeeId: \"BH001\",\n                        firstName: \"Amadou\",\n                        lastName: \"Ba\",\n                        email: \"<EMAIL>\",\n                        phone: \"+221 77 123 4567\",\n                        isActive: true,\n                        isAvailable: true,\n                        certifications: [\n                            \"Security Guard License\",\n                            \"First Aid\"\n                        ],\n                        skills: [\n                            \"Surveillance\",\n                            \"Emergency Response\"\n                        ],\n                        emergencyContactName: \"Fatou Ba\",\n                        emergencyContactPhone: \"+221 77 987 6543\",\n                        currentLocation: {\n                            latitude: 14.6937,\n                            longitude: -17.4441,\n                            address: \"Dakar, Senegal\"\n                        },\n                        lastLocationUpdate: new Date().toISOString(),\n                        performance: {\n                            totalShifts: 45,\n                            completedShifts: 42,\n                            completionRate: 93.3,\n                            totalReports: 12,\n                            averageRating: 4.8,\n                            totalHours: 360\n                        },\n                        recentShifts: [],\n                        recentReports: [],\n                        createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date().toISOString()\n                    },\n                    {\n                        id: \"2\",\n                        employeeId: \"BH002\",\n                        firstName: \"Fatou\",\n                        lastName: \"Sow\",\n                        email: \"<EMAIL>\",\n                        phone: \"+221 77 234 5678\",\n                        isActive: true,\n                        isAvailable: false,\n                        certifications: [\n                            \"Security Guard License\"\n                        ],\n                        skills: [\n                            \"Patrol\",\n                            \"Access Control\"\n                        ],\n                        emergencyContactName: \"Moussa Sow\",\n                        emergencyContactPhone: \"+221 77 876 5432\",\n                        currentLocation: {\n                            latitude: 14.7167,\n                            longitude: -17.4677,\n                            address: \"Plateau, Dakar\"\n                        },\n                        lastLocationUpdate: new Date(Date.now() - 15 * 60 * 1000).toISOString(),\n                        performance: {\n                            totalShifts: 38,\n                            completedShifts: 35,\n                            completionRate: 92.1,\n                            totalReports: 8,\n                            averageRating: 4.6,\n                            totalHours: 304\n                        },\n                        recentShifts: [],\n                        recentReports: [],\n                        createdAt: new Date(Date.now() - 75 * 24 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date().toISOString()\n                    },\n                    {\n                        id: \"3\",\n                        employeeId: \"BH003\",\n                        firstName: \"Moussa\",\n                        lastName: \"Diop\",\n                        email: \"<EMAIL>\",\n                        phone: \"+221 77 345 6789\",\n                        isActive: true,\n                        isAvailable: true,\n                        certifications: [\n                            \"Security Guard License\",\n                            \"Fire Safety\"\n                        ],\n                        skills: [\n                            \"Investigation\",\n                            \"Report Writing\"\n                        ],\n                        emergencyContactName: \"Aissatou Diop\",\n                        emergencyContactPhone: \"+221 77 765 4321\",\n                        currentLocation: {\n                            latitude: 14.6928,\n                            longitude: -17.4467,\n                            address: \"Medina, Dakar\"\n                        },\n                        lastLocationUpdate: new Date(Date.now() - 5 * 60 * 1000).toISOString(),\n                        performance: {\n                            totalShifts: 52,\n                            completedShifts: 48,\n                            completionRate: 92.3,\n                            totalReports: 15,\n                            averageRating: 4.7,\n                            totalHours: 416\n                        },\n                        recentShifts: [],\n                        recentReports: [],\n                        createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date().toISOString()\n                    }\n                ],\n                total: 3,\n                page: 1,\n                limit: 50,\n                totalPages: 1,\n                lastUpdated: new Date().toISOString(),\n                note: \"Sample data - Database connection pending\"\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(agentsData);\n    } catch (error) {\n        console.error(\"Agents API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch agents data\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/agents/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fagents%2Froute&page=%2Fapi%2Fagents%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fagents%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();