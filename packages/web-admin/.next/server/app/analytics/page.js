/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/analytics/page";
exports.ids = ["app/analytics/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fanalytics%2Fpage&page=%2Fanalytics%2Fpage&appPaths=%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fanalytics%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fanalytics%2Fpage&page=%2Fanalytics%2Fpage&appPaths=%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fanalytics%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1c3a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'analytics',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/analytics/page.js */ \"(rsc)/./src/app/analytics/page.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/analytics/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/analytics/page\",\n        pathname: \"/analytics\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fanalytics%2Fpage&page=%2Fanalytics%2Fpage&appPaths=%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fanalytics%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fanalytics%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fanalytics%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/analytics/page.js */ \"(ssr)/./src/app/analytics/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGcGFja2FnZXMlMkZ3ZWItYWRtaW4lMkZzcmMlMkZhcHAlMkZhbmFseXRpY3MlMkZwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrS0FBbUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYmFoaW5saW5rL3dlYi1hZG1pbi8/MmI4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi93b3Jrc3BhY2VzL2ZpbmFsYWdlbnQtbWFpbi1maW5hbC9wYWNrYWdlcy93ZWItYWRtaW4vc3JjL2FwcC9hbmFseXRpY3MvcGFnZS5qc1wiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fanalytics%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/analytics/page.js":
/*!***********************************!*\
  !*** ./src/app/analytics/page.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/TrendingUp.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Download.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Security.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ModernSidebar */ \"(ssr)/./src/components/ModernSidebar.js\");\n/* harmony import */ var _components_AnalyticsCharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/AnalyticsCharts */ \"(ssr)/./src/components/AnalyticsCharts.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../services/ApiService */ \"(ssr)/./src/services/ApiService.js\");\n// BahinLink Web Admin Analytics Dashboard Page\n// ⚠️ CRITICAL: Real analytics with live data visualization ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction AnalyticsPage() {\n    const { isLoaded, isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_6__.useUser)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"7d\");\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Real analytics data from database\n    const [dashboardMetrics, setDashboardMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalAgents: 0,\n        activeShifts: 0,\n        completedShifts: 0,\n        totalSites: 0,\n        totalReports: 0,\n        averageResponseTime: 0,\n        efficiency: 0,\n        clientSatisfaction: 0\n    });\n    const [performanceData, setPerformanceData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [agentPerformance, setAgentPerformance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [siteActivity, setSiteActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentAlerts, setRecentAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [shiftTrends, setShiftTrends] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [kpis, setKpis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalShifts: 0,\n        completedShifts: 0,\n        totalHours: 0,\n        averageRating: 0,\n        activeAgents: 0,\n        totalReports: 0,\n        responseTime: 0,\n        efficiency: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && !isSignedIn) {\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/sign-in\");\n        }\n        if (isLoaded && isSignedIn) {\n            loadAnalyticsData();\n            // Auto-refresh every 30 seconds for real-time data\n            const interval = setInterval(loadAnalyticsData, 30000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isLoaded,\n        isSignedIn,\n        timeRange\n    ]);\n    const loadAnalyticsData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const [shiftsResponse, reportsResponse, agentsResponse, kpiResponse] = await Promise.all([\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/analytics/shifts\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/analytics/reports\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/analytics/agents\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/analytics/kpis\", {\n                    timeRange\n                })\n            ]);\n            // Combine all analytics data\n            const combinedData = {\n                overview: kpiResponse.success ? kpiResponse.data : {},\n                shiftTrends: shiftsResponse.success ? shiftsResponse.data.trends : [],\n                reportTypes: reportsResponse.success ? reportsResponse.data.types : [],\n                agentPerformance: agentsResponse.success ? agentsResponse.data.performance : [],\n                siteActivity: shiftsResponse.success ? shiftsResponse.data.siteActivity : []\n            };\n            setAnalyticsData(combinedData);\n            // Set KPIs\n            if (kpiResponse.success) {\n                setKpis(kpiResponse.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading analytics data:\", error);\n            setError(\"Failed to load analytics data\");\n            // Fallback to sample data for demo\n            setAnalyticsData({\n                overview: {\n                    totalShifts: 156,\n                    activeShifts: 12,\n                    totalAgents: 24,\n                    activeAgents: 18,\n                    totalSites: 8,\n                    activeSites: 6,\n                    totalReports: 89,\n                    pendingReports: 5,\n                    shiftsChange: 12.5,\n                    agentsChange: -2.1,\n                    reportsChange: 8.3\n                },\n                shiftTrends: [\n                    {\n                        date: \"2024-01-01\",\n                        scheduled: 20,\n                        completed: 18,\n                        cancelled: 2\n                    },\n                    {\n                        date: \"2024-01-02\",\n                        scheduled: 22,\n                        completed: 20,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-03\",\n                        scheduled: 18,\n                        completed: 17,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-04\",\n                        scheduled: 25,\n                        completed: 23,\n                        cancelled: 2\n                    },\n                    {\n                        date: \"2024-01-05\",\n                        scheduled: 21,\n                        completed: 19,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-06\",\n                        scheduled: 19,\n                        completed: 18,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-07\",\n                        scheduled: 23,\n                        completed: 21,\n                        cancelled: 2\n                    }\n                ],\n                reportTypes: [\n                    {\n                        name: \"Security\",\n                        value: 35,\n                        color: \"#DC3545\"\n                    },\n                    {\n                        name: \"Maintenance\",\n                        value: 25,\n                        color: \"#FFC107\"\n                    },\n                    {\n                        name: \"Incident\",\n                        value: 20,\n                        color: \"#DC004E\"\n                    },\n                    {\n                        name: \"General\",\n                        value: 15,\n                        color: \"#1976D2\"\n                    },\n                    {\n                        name: \"Safety\",\n                        value: 5,\n                        color: \"#2E7D32\"\n                    }\n                ],\n                agentPerformance: [\n                    {\n                        name: \"John Doe\",\n                        shifts: 15,\n                        onTime: 14,\n                        reports: 8,\n                        rating: 4.8\n                    },\n                    {\n                        name: \"Jane Smith\",\n                        shifts: 12,\n                        onTime: 12,\n                        reports: 6,\n                        rating: 4.9\n                    },\n                    {\n                        name: \"Mike Johnson\",\n                        shifts: 18,\n                        onTime: 16,\n                        reports: 12,\n                        rating: 4.6\n                    },\n                    {\n                        name: \"Sarah Wilson\",\n                        shifts: 14,\n                        onTime: 13,\n                        reports: 9,\n                        rating: 4.7\n                    },\n                    {\n                        name: \"David Brown\",\n                        shifts: 16,\n                        onTime: 15,\n                        reports: 7,\n                        rating: 4.5\n                    }\n                ],\n                siteActivity: [\n                    {\n                        site: \"Downtown Mall\",\n                        shifts: 45,\n                        incidents: 3,\n                        efficiency: 95\n                    },\n                    {\n                        site: \"Office Complex\",\n                        shifts: 38,\n                        incidents: 1,\n                        efficiency: 98\n                    },\n                    {\n                        site: \"Warehouse A\",\n                        shifts: 32,\n                        incidents: 2,\n                        efficiency: 92\n                    },\n                    {\n                        site: \"Retail Center\",\n                        shifts: 28,\n                        incidents: 4,\n                        efficiency: 88\n                    },\n                    {\n                        site: \"Industrial Park\",\n                        shifts: 25,\n                        incidents: 1,\n                        efficiency: 96\n                    }\n                ]\n            });\n            setKpis({\n                totalShifts: 156,\n                completedShifts: 142,\n                totalHours: 1248,\n                averageRating: 4.7,\n                activeAgents: 18,\n                totalReports: 89,\n                responseTime: 12.5,\n                efficiency: 94.2\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleExport = async ()=>{\n        try {\n            // In a real app, this would generate and download analytics report\n            console.log(\"Exporting analytics data...\");\n            alert(\"Export functionality would be implemented here\");\n        } catch (error) {\n            console.error(\"Error exporting analytics:\", error);\n        }\n    };\n    const KPICard = ({ title, value, unit = \"\", icon: Icon, color = \"primary\", trend })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n            elevation: 2,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: `${color}.main`,\n                                    children: [\n                                        value,\n                                        unit\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this),\n                                trend !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        mt: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            color: trend >= 0 ? \"success\" : \"error\",\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            variant: \"caption\",\n                                            color: trend >= 0 ? \"success.main\" : \"error.main\",\n                                            sx: {\n                                                ml: 0.5\n                                            },\n                                            children: [\n                                                Math.abs(trend),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 230,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: `${color}.main`,\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 240,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 215,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n            lineNumber: 214,\n            columnNumber: 5\n        }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    p: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            mb: 3,\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"h4\",\n                                fontWeight: \"bold\",\n                                children: \"Analytics Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 253,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    gap: 1,\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        size: \"small\",\n                                        sx: {\n                                            minWidth: 120\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                children: \"Time Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                value: timeRange,\n                                                label: \"Time Range\",\n                                                onChange: (e)=>setTimeRange(e.target.value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: \"1d\",\n                                                        children: \"Last 24 Hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: \"7d\",\n                                                        children: \"Last 7 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: \"30d\",\n                                                        children: \"Last 30 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        value: \"90d\",\n                                                        children: \"Last 90 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 259,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 257,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 273,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        onClick: handleExport,\n                                        children: \"Export\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 271,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 281,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        onClick: loadAnalyticsData,\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 279,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 256,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 252,\n                        columnNumber: 7\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        severity: \"warning\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            error,\n                            \" - Showing sample data for demonstration\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 291,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        sx: {\n                            mb: 4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Shifts\",\n                                    value: kpis.totalShifts,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                    color: \"primary\",\n                                    trend: 12.5\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 299,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 298,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Completion Rate\",\n                                    value: (kpis.completedShifts / kpis.totalShifts * 100).toFixed(1),\n                                    unit: \"%\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    color: \"success\",\n                                    trend: 5.2\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 308,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 307,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Hours\",\n                                    value: kpis.totalHours.toLocaleString(),\n                                    unit: \"h\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                    color: \"info\",\n                                    trend: 8.7\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 318,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 317,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Average Rating\",\n                                    value: kpis.averageRating,\n                                    unit: \"/5\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    color: \"warning\",\n                                    trend: 2.1\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 328,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 327,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 297,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        sx: {\n                            mb: 4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Active Agents\",\n                                    value: kpis.activeAgents,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                    color: \"secondary\",\n                                    trend: -1.5\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 342,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 341,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Reports\",\n                                    value: kpis.totalReports,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                    color: \"primary\",\n                                    trend: 15.3\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 351,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 350,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Avg Response Time\",\n                                    value: kpis.responseTime,\n                                    unit: \"min\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                    color: \"warning\",\n                                    trend: -8.2\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 360,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 359,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Efficiency\",\n                                    value: kpis.efficiency,\n                                    unit: \"%\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n                                    color: \"success\",\n                                    trend: 3.4\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 370,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 369,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 340,\n                        columnNumber: 7\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            py: 8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 383,\n                        columnNumber: 9\n                    }, this) : analyticsData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalyticsCharts__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        data: analyticsData,\n                        loading: loading,\n                        timeRange: timeRange,\n                        onTimeRangeChange: setTimeRange\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 387,\n                        columnNumber: 9\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                        sx: {\n                            p: 4,\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"h6\",\n                                color: \"textSecondary\",\n                                children: \"No analytics data available\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 395,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"textSecondary\",\n                                sx: {\n                                    mt: 1\n                                },\n                                children: \"Data will appear here once shifts and reports are created\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 398,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 394,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 250,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2FuYWx5dGljcy9wYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLCtDQUErQztBQUMvQyxnRUFBZ0U7O0FBSWY7QUFDTDtBQUNEO0FBcUJwQjtBQWtCTTtBQUN5SjtBQUMzSDtBQUNJO0FBQ1o7QUFFcEMsU0FBUzBFO0lBQ3RCLE1BQU0sRUFBRUMsUUFBUSxFQUFFQyxVQUFVLEVBQUUsR0FBRzVFLHNEQUFPQTtJQUN4QyxNQUFNLEVBQUU2RSxJQUFJLEVBQUUsR0FBRzVFLHNEQUFPQTtJQUN4QixNQUFNLENBQUM2RSxTQUFTQyxXQUFXLEdBQUc3RSwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUM4RSxPQUFPQyxTQUFTLEdBQUcvRSwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNnRixXQUFXQyxhQUFhLEdBQUdqRiwrQ0FBUUEsQ0FBQztJQUMzQyxNQUFNLENBQUNrRixZQUFZQyxjQUFjLEdBQUduRiwrQ0FBUUEsQ0FBQztJQUU3QyxvQ0FBb0M7SUFDcEMsTUFBTSxDQUFDb0Ysa0JBQWtCQyxvQkFBb0IsR0FBR3JGLCtDQUFRQSxDQUFDO1FBQ3ZEc0YsYUFBYTtRQUNiQyxjQUFjO1FBQ2RDLGlCQUFpQjtRQUNqQkMsWUFBWTtRQUNaQyxjQUFjO1FBQ2RDLHFCQUFxQjtRQUNyQkMsWUFBWTtRQUNaQyxvQkFBb0I7SUFDdEI7SUFFQSxNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUcvRiwrQ0FBUUEsQ0FBQyxFQUFFO0lBQ3pELE1BQU0sQ0FBQ2dHLGtCQUFrQkMsb0JBQW9CLEdBQUdqRywrQ0FBUUEsQ0FBQyxFQUFFO0lBQzNELE1BQU0sQ0FBQ2tHLGNBQWNDLGdCQUFnQixHQUFHbkcsK0NBQVFBLENBQUMsRUFBRTtJQUNuRCxNQUFNLENBQUNvRyxjQUFjQyxnQkFBZ0IsR0FBR3JHLCtDQUFRQSxDQUFDLEVBQUU7SUFDbkQsTUFBTSxDQUFDc0csYUFBYUMsZUFBZSxHQUFHdkcsK0NBQVFBLENBQUMsRUFBRTtJQUNqRCxNQUFNLENBQUN3RyxlQUFlQyxpQkFBaUIsR0FBR3pHLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQzBHLE1BQU1DLFFBQVEsR0FBRzNHLCtDQUFRQSxDQUFDO1FBQy9CNEcsYUFBYTtRQUNicEIsaUJBQWlCO1FBQ2pCcUIsWUFBWTtRQUNaQyxlQUFlO1FBQ2ZDLGNBQWM7UUFDZHJCLGNBQWM7UUFDZHNCLGNBQWM7UUFDZHBCLFlBQVk7SUFDZDtJQUVBM0YsZ0RBQVNBLENBQUM7UUFDUixJQUFJd0UsWUFBWSxDQUFDQyxZQUFZO1lBQzNCeEUseURBQVFBLENBQUM7UUFDWDtRQUVBLElBQUl1RSxZQUFZQyxZQUFZO1lBQzFCdUM7WUFFQSxtREFBbUQ7WUFDbkQsTUFBTUMsV0FBV0MsWUFBWUYsbUJBQW1CO1lBQ2hELE9BQU8sSUFBTUcsY0FBY0Y7UUFDN0I7SUFDRixHQUFHO1FBQUN6QztRQUFVQztRQUFZTTtLQUFVO0lBRXBDLE1BQU1pQyxvQkFBb0I7UUFDeEIsSUFBSTtZQUNGcEMsV0FBVztZQUNYRSxTQUFTO1lBRVQsTUFBTSxDQUNKc0MsZ0JBQ0FDLGlCQUNBQyxnQkFDQUMsWUFDRCxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztnQkFDcEJuRCw0REFBVUEsQ0FBQ29ELEdBQUcsQ0FBQyxxQkFBcUI7b0JBQUUzQztnQkFBVTtnQkFDaERULDREQUFVQSxDQUFDb0QsR0FBRyxDQUFDLHNCQUFzQjtvQkFBRTNDO2dCQUFVO2dCQUNqRFQsNERBQVVBLENBQUNvRCxHQUFHLENBQUMscUJBQXFCO29CQUFFM0M7Z0JBQVU7Z0JBQ2hEVCw0REFBVUEsQ0FBQ29ELEdBQUcsQ0FBQyxtQkFBbUI7b0JBQUUzQztnQkFBVTthQUMvQztZQUVELDZCQUE2QjtZQUM3QixNQUFNNEMsZUFBZTtnQkFDbkJDLFVBQVVMLFlBQVlNLE9BQU8sR0FBR04sWUFBWU8sSUFBSSxHQUFHLENBQUM7Z0JBQ3BEekIsYUFBYWUsZUFBZVMsT0FBTyxHQUFHVCxlQUFlVSxJQUFJLENBQUNDLE1BQU0sR0FBRyxFQUFFO2dCQUNyRUMsYUFBYVgsZ0JBQWdCUSxPQUFPLEdBQUdSLGdCQUFnQlMsSUFBSSxDQUFDRyxLQUFLLEdBQUcsRUFBRTtnQkFDdEVsQyxrQkFBa0J1QixlQUFlTyxPQUFPLEdBQUdQLGVBQWVRLElBQUksQ0FBQ0ksV0FBVyxHQUFHLEVBQUU7Z0JBQy9FakMsY0FBY21CLGVBQWVTLE9BQU8sR0FBR1QsZUFBZVUsSUFBSSxDQUFDN0IsWUFBWSxHQUFHLEVBQUU7WUFDOUU7WUFFQU8saUJBQWlCbUI7WUFFakIsV0FBVztZQUNYLElBQUlKLFlBQVlNLE9BQU8sRUFBRTtnQkFDdkJuQixRQUFRYSxZQUFZTyxJQUFJO1lBQzFCO1FBRUYsRUFBRSxPQUFPakQsT0FBTztZQUNkc0QsUUFBUXRELEtBQUssQ0FBQyxpQ0FBaUNBO1lBQy9DQyxTQUFTO1lBRVQsbUNBQW1DO1lBQ25DMEIsaUJBQWlCO2dCQUNmb0IsVUFBVTtvQkFDUmpCLGFBQWE7b0JBQ2JyQixjQUFjO29CQUNkRCxhQUFhO29CQUNieUIsY0FBYztvQkFDZHRCLFlBQVk7b0JBQ1o0QyxhQUFhO29CQUNiM0MsY0FBYztvQkFDZDRDLGdCQUFnQjtvQkFDaEJDLGNBQWM7b0JBQ2RDLGNBQWMsQ0FBQztvQkFDZkMsZUFBZTtnQkFDakI7Z0JBQ0FuQyxhQUFhO29CQUNYO3dCQUFFb0MsTUFBTTt3QkFBY0MsV0FBVzt3QkFBSUMsV0FBVzt3QkFBSUMsV0FBVztvQkFBRTtvQkFDakU7d0JBQUVILE1BQU07d0JBQWNDLFdBQVc7d0JBQUlDLFdBQVc7d0JBQUlDLFdBQVc7b0JBQUU7b0JBQ2pFO3dCQUFFSCxNQUFNO3dCQUFjQyxXQUFXO3dCQUFJQyxXQUFXO3dCQUFJQyxXQUFXO29CQUFFO29CQUNqRTt3QkFBRUgsTUFBTTt3QkFBY0MsV0FBVzt3QkFBSUMsV0FBVzt3QkFBSUMsV0FBVztvQkFBRTtvQkFDakU7d0JBQUVILE1BQU07d0JBQWNDLFdBQVc7d0JBQUlDLFdBQVc7d0JBQUlDLFdBQVc7b0JBQUU7b0JBQ2pFO3dCQUFFSCxNQUFNO3dCQUFjQyxXQUFXO3dCQUFJQyxXQUFXO3dCQUFJQyxXQUFXO29CQUFFO29CQUNqRTt3QkFBRUgsTUFBTTt3QkFBY0MsV0FBVzt3QkFBSUMsV0FBVzt3QkFBSUMsV0FBVztvQkFBRTtpQkFDbEU7Z0JBQ0RaLGFBQWE7b0JBQ1g7d0JBQUVhLE1BQU07d0JBQVlDLE9BQU87d0JBQUlDLE9BQU87b0JBQVU7b0JBQ2hEO3dCQUFFRixNQUFNO3dCQUFlQyxPQUFPO3dCQUFJQyxPQUFPO29CQUFVO29CQUNuRDt3QkFBRUYsTUFBTTt3QkFBWUMsT0FBTzt3QkFBSUMsT0FBTztvQkFBVTtvQkFDaEQ7d0JBQUVGLE1BQU07d0JBQVdDLE9BQU87d0JBQUlDLE9BQU87b0JBQVU7b0JBQy9DO3dCQUFFRixNQUFNO3dCQUFVQyxPQUFPO3dCQUFHQyxPQUFPO29CQUFVO2lCQUM5QztnQkFDRGhELGtCQUFrQjtvQkFDaEI7d0JBQUU4QyxNQUFNO3dCQUFZRyxRQUFRO3dCQUFJQyxRQUFRO3dCQUFJQyxTQUFTO3dCQUFHQyxRQUFRO29CQUFJO29CQUNwRTt3QkFBRU4sTUFBTTt3QkFBY0csUUFBUTt3QkFBSUMsUUFBUTt3QkFBSUMsU0FBUzt3QkFBR0MsUUFBUTtvQkFBSTtvQkFDdEU7d0JBQUVOLE1BQU07d0JBQWdCRyxRQUFRO3dCQUFJQyxRQUFRO3dCQUFJQyxTQUFTO3dCQUFJQyxRQUFRO29CQUFJO29CQUN6RTt3QkFBRU4sTUFBTTt3QkFBZ0JHLFFBQVE7d0JBQUlDLFFBQVE7d0JBQUlDLFNBQVM7d0JBQUdDLFFBQVE7b0JBQUk7b0JBQ3hFO3dCQUFFTixNQUFNO3dCQUFlRyxRQUFRO3dCQUFJQyxRQUFRO3dCQUFJQyxTQUFTO3dCQUFHQyxRQUFRO29CQUFJO2lCQUN4RTtnQkFDRGxELGNBQWM7b0JBQ1o7d0JBQUVtRCxNQUFNO3dCQUFpQkosUUFBUTt3QkFBSUssV0FBVzt3QkFBRzFELFlBQVk7b0JBQUc7b0JBQ2xFO3dCQUFFeUQsTUFBTTt3QkFBa0JKLFFBQVE7d0JBQUlLLFdBQVc7d0JBQUcxRCxZQUFZO29CQUFHO29CQUNuRTt3QkFBRXlELE1BQU07d0JBQWVKLFFBQVE7d0JBQUlLLFdBQVc7d0JBQUcxRCxZQUFZO29CQUFHO29CQUNoRTt3QkFBRXlELE1BQU07d0JBQWlCSixRQUFRO3dCQUFJSyxXQUFXO3dCQUFHMUQsWUFBWTtvQkFBRztvQkFDbEU7d0JBQUV5RCxNQUFNO3dCQUFtQkosUUFBUTt3QkFBSUssV0FBVzt3QkFBRzFELFlBQVk7b0JBQUc7aUJBQ3JFO1lBQ0g7WUFFQWUsUUFBUTtnQkFDTkMsYUFBYTtnQkFDYnBCLGlCQUFpQjtnQkFDakJxQixZQUFZO2dCQUNaQyxlQUFlO2dCQUNmQyxjQUFjO2dCQUNkckIsY0FBYztnQkFDZHNCLGNBQWM7Z0JBQ2RwQixZQUFZO1lBQ2Q7UUFDRixTQUFVO1lBQ1JmLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTTBFLGVBQWU7UUFDbkIsSUFBSTtZQUNGLG1FQUFtRTtZQUNuRW5CLFFBQVFvQixHQUFHLENBQUM7WUFDWkMsTUFBTTtRQUNSLEVBQUUsT0FBTzNFLE9BQU87WUFDZHNELFFBQVF0RCxLQUFLLENBQUMsOEJBQThCQTtRQUM5QztJQUNGO0lBRUEsTUFBTTRFLFVBQVUsQ0FBQyxFQUFFQyxLQUFLLEVBQUVaLEtBQUssRUFBRWEsT0FBTyxFQUFFLEVBQUVDLE1BQU1DLElBQUksRUFBRWQsUUFBUSxTQUFTLEVBQUVlLEtBQUssRUFBRSxpQkFDaEYsOERBQUN6SixpUEFBSUE7WUFBQzBKLFdBQVc7c0JBQ2YsNEVBQUN6SixpUEFBV0E7MEJBQ1YsNEVBQUNKLGlQQUFHQTtvQkFBQzhKLElBQUk7d0JBQUVDLFNBQVM7d0JBQVFDLFlBQVk7d0JBQVVDLGdCQUFnQjtvQkFBZ0I7O3NDQUNoRiw4REFBQ2pLLGlQQUFHQTs7OENBQ0YsOERBQUNDLGtQQUFVQTtvQ0FBQ2lLLFNBQVE7b0NBQUtDLFdBQVU7b0NBQU10QixPQUFPLENBQUMsRUFBRUEsTUFBTSxLQUFLLENBQUM7O3dDQUM1REQ7d0NBQU9hOzs7Ozs7OzhDQUVWLDhEQUFDeEosa1BBQVVBO29DQUFDaUssU0FBUTtvQ0FBUXJCLE9BQU07OENBQy9CVzs7Ozs7O2dDQUVGSSxVQUFVUSwyQkFDVCw4REFBQ3BLLGlQQUFHQTtvQ0FBQzhKLElBQUk7d0NBQUVDLFNBQVM7d0NBQVFDLFlBQVk7d0NBQVVLLElBQUk7b0NBQUU7O3NEQUN0RCw4REFBQzdJLG1PQUFjQTs0Q0FDYnFILE9BQU9lLFNBQVMsSUFBSSxZQUFZOzRDQUNoQ1UsVUFBUzs7Ozs7O3NEQUVYLDhEQUFDckssa1BBQVVBOzRDQUNUaUssU0FBUTs0Q0FDUnJCLE9BQU9lLFNBQVMsSUFBSSxpQkFBaUI7NENBQ3JDRSxJQUFJO2dEQUFFUyxJQUFJOzRDQUFJOztnREFFYkMsS0FBS0MsR0FBRyxDQUFDYjtnREFBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLekIsOERBQUNEOzRCQUFLRyxJQUFJO2dDQUFFUSxVQUFVO2dDQUFJekIsT0FBTyxDQUFDLEVBQUVBLE1BQU0sS0FBSyxDQUFDO2dDQUFFNkIsU0FBUzs0QkFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU12RSxxQkFDRSw4REFBQzFLLGlQQUFHQTtRQUFDOEosSUFBSTtZQUFFQyxTQUFTO1lBQVFZLFdBQVc7WUFBU0MsaUJBQWlCO1FBQVU7OzBCQUN6RSw4REFBQzFHLGlFQUFhQTs7Ozs7MEJBRWQsOERBQUNsRSxpUEFBR0E7Z0JBQUNtSyxXQUFVO2dCQUFPTCxJQUFJO29CQUFFZSxVQUFVO29CQUFHQyxHQUFHO2dCQUFFOztrQ0FFOUMsOERBQUM5SyxpUEFBR0E7d0JBQUM4SixJQUFJOzRCQUFFaUIsSUFBSTs0QkFBR2hCLFNBQVM7NEJBQVFFLGdCQUFnQjs0QkFBaUJELFlBQVk7d0JBQVM7OzBDQUN2Riw4REFBQy9KLGtQQUFVQTtnQ0FBQ2lLLFNBQVE7Z0NBQUtjLFlBQVc7MENBQU87Ozs7OzswQ0FHM0MsOERBQUNoTCxpUEFBR0E7Z0NBQUM4SixJQUFJO29DQUFFQyxTQUFTO29DQUFRa0IsS0FBSztvQ0FBR2pCLFlBQVk7Z0NBQVM7O2tEQUN2RCw4REFBQzNKLGtQQUFXQTt3Q0FBQzZLLE1BQUs7d0NBQVFwQixJQUFJOzRDQUFFcUIsVUFBVTt3Q0FBSTs7MERBQzVDLDhEQUFDN0ssa1BBQVVBOzBEQUFDOzs7Ozs7MERBQ1osOERBQUNDLGtQQUFNQTtnREFDTHFJLE9BQU8vRDtnREFDUHVHLE9BQU07Z0RBQ05DLFVBQVUsQ0FBQ0MsSUFBTXhHLGFBQWF3RyxFQUFFQyxNQUFNLENBQUMzQyxLQUFLOztrRUFFNUMsOERBQUNwSSxrUEFBUUE7d0RBQUNvSSxPQUFNO2tFQUFLOzs7Ozs7a0VBQ3JCLDhEQUFDcEksa1BBQVFBO3dEQUFDb0ksT0FBTTtrRUFBSzs7Ozs7O2tFQUNyQiw4REFBQ3BJLGtQQUFRQTt3REFBQ29JLE9BQU07a0VBQU07Ozs7OztrRUFDdEIsOERBQUNwSSxrUEFBUUE7d0RBQUNvSSxPQUFNO2tFQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSTFCLDhEQUFDakksa1BBQU1BO3dDQUNMdUosU0FBUTt3Q0FDUnNCLHlCQUFXLDhEQUFDbEssbU9BQVlBOzs7Ozt3Q0FDeEJtSyxTQUFTckM7a0RBQ1Y7Ozs7OztrREFJRCw4REFBQ3pJLGtQQUFNQTt3Q0FDTHVKLFNBQVE7d0NBQ1JzQix5QkFBVyw4REFBQ3BLLG1PQUFXQTs7Ozs7d0NBQ3ZCcUssU0FBUzNFO2tEQUNWOzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBT0puQyx1QkFDQyw4REFBQ2xFLGtQQUFLQTt3QkFBQ2lMLFVBQVM7d0JBQVU1QixJQUFJOzRCQUFFaUIsSUFBSTt3QkFBRTs7NEJBQ25DcEc7NEJBQU07Ozs7Ozs7a0NBS1gsOERBQUN6RSxrUEFBSUE7d0JBQUN5TCxTQUFTO3dCQUFDQyxTQUFTO3dCQUFHOUIsSUFBSTs0QkFBRWlCLElBQUk7d0JBQUU7OzBDQUN0Qyw4REFBQzdLLGtQQUFJQTtnQ0FBQzJMLElBQUk7Z0NBQUNDLElBQUk7Z0NBQUlDLElBQUk7Z0NBQUdDLElBQUk7MENBQzVCLDRFQUFDekM7b0NBQ0NDLE9BQU07b0NBQ05aLE9BQU9yQyxLQUFLRSxXQUFXO29DQUN2QmlELE1BQU05SCxtT0FBWUE7b0NBQ2xCaUgsT0FBTTtvQ0FDTmUsT0FBTzs7Ozs7Ozs7Ozs7MENBR1gsOERBQUMxSixrUEFBSUE7Z0NBQUMyTCxJQUFJO2dDQUFDQyxJQUFJO2dDQUFJQyxJQUFJO2dDQUFHQyxJQUFJOzBDQUM1Qiw0RUFBQ3pDO29DQUNDQyxPQUFNO29DQUNOWixPQUFPLENBQUMsS0FBTXZELGVBQWUsR0FBR2tCLEtBQUtFLFdBQVcsR0FBSSxHQUFFLEVBQUd3RixPQUFPLENBQUM7b0NBQ2pFeEMsTUFBSztvQ0FDTEMsTUFBTWxJLG1PQUFjQTtvQ0FDcEJxSCxPQUFNO29DQUNOZSxPQUFPOzs7Ozs7Ozs7OzswQ0FHWCw4REFBQzFKLGtQQUFJQTtnQ0FBQzJMLElBQUk7Z0NBQUNDLElBQUk7Z0NBQUlDLElBQUk7Z0NBQUdDLElBQUk7MENBQzVCLDRFQUFDekM7b0NBQ0NDLE9BQU07b0NBQ05aLE9BQU9yQyxLQUFLRyxVQUFVLENBQUN3RixjQUFjO29DQUNyQ3pDLE1BQUs7b0NBQ0xDLE1BQU05SCxtT0FBWUE7b0NBQ2xCaUgsT0FBTTtvQ0FDTmUsT0FBTzs7Ozs7Ozs7Ozs7MENBR1gsOERBQUMxSixrUEFBSUE7Z0NBQUMyTCxJQUFJO2dDQUFDQyxJQUFJO2dDQUFJQyxJQUFJO2dDQUFHQyxJQUFJOzBDQUM1Qiw0RUFBQ3pDO29DQUNDQyxPQUFNO29DQUNOWixPQUFPckMsS0FBS0ksYUFBYTtvQ0FDekI4QyxNQUFLO29DQUNMQyxNQUFNbEksbU9BQWNBO29DQUNwQnFILE9BQU07b0NBQ05lLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQU1iLDhEQUFDMUosa1BBQUlBO3dCQUFDeUwsU0FBUzt3QkFBQ0MsU0FBUzt3QkFBRzlCLElBQUk7NEJBQUVpQixJQUFJO3dCQUFFOzswQ0FDdEMsOERBQUM3SyxrUEFBSUE7Z0NBQUMyTCxJQUFJO2dDQUFDQyxJQUFJO2dDQUFJQyxJQUFJO2dDQUFHQyxJQUFJOzBDQUM1Qiw0RUFBQ3pDO29DQUNDQyxPQUFNO29DQUNOWixPQUFPckMsS0FBS0ssWUFBWTtvQ0FDeEI4QyxNQUFNMUgsbU9BQVVBO29DQUNoQjZHLE9BQU07b0NBQ05lLE9BQU8sQ0FBQzs7Ozs7Ozs7Ozs7MENBR1osOERBQUMxSixrUEFBSUE7Z0NBQUMyTCxJQUFJO2dDQUFDQyxJQUFJO2dDQUFJQyxJQUFJO2dDQUFHQyxJQUFJOzBDQUM1Qiw0RUFBQ3pDO29DQUNDQyxPQUFNO29DQUNOWixPQUFPckMsS0FBS2hCLFlBQVk7b0NBQ3hCbUUsTUFBTTVILG1PQUFjQTtvQ0FDcEIrRyxPQUFNO29DQUNOZSxPQUFPOzs7Ozs7Ozs7OzswQ0FHWCw4REFBQzFKLGtQQUFJQTtnQ0FBQzJMLElBQUk7Z0NBQUNDLElBQUk7Z0NBQUlDLElBQUk7Z0NBQUdDLElBQUk7MENBQzVCLDRFQUFDekM7b0NBQ0NDLE9BQU07b0NBQ05aLE9BQU9yQyxLQUFLTSxZQUFZO29DQUN4QjRDLE1BQUs7b0NBQ0xDLE1BQU05SCxtT0FBWUE7b0NBQ2xCaUgsT0FBTTtvQ0FDTmUsT0FBTyxDQUFDOzs7Ozs7Ozs7OzswQ0FHWiw4REFBQzFKLGtQQUFJQTtnQ0FBQzJMLElBQUk7Z0NBQUNDLElBQUk7Z0NBQUlDLElBQUk7Z0NBQUdDLElBQUk7MENBQzVCLDRFQUFDekM7b0NBQ0NDLE9BQU07b0NBQ05aLE9BQU9yQyxLQUFLZCxVQUFVO29DQUN0QmdFLE1BQUs7b0NBQ0xDLE1BQU14SCxtT0FBWUE7b0NBQ2xCMkcsT0FBTTtvQ0FDTmUsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBTVpuRix3QkFDQyw4REFBQ3pFLGlQQUFHQTt3QkFBQzhKLElBQUk7NEJBQUVDLFNBQVM7NEJBQVFFLGdCQUFnQjs0QkFBVWtDLElBQUk7d0JBQUU7a0NBQzFELDRFQUFDekwsa1BBQWdCQTs0QkFBQ3dLLE1BQU07Ozs7Ozs7Ozs7K0JBRXhCN0UsOEJBQ0YsOERBQUNsQyxtRUFBZUE7d0JBQ2R5RCxNQUFNdkI7d0JBQ041QixTQUFTQTt3QkFDVEksV0FBV0E7d0JBQ1h1SCxtQkFBbUJ0SDs7Ozs7NkNBR3JCLDhEQUFDNUQsa1BBQUtBO3dCQUFDNEksSUFBSTs0QkFBRWdCLEdBQUc7NEJBQUd1QixXQUFXO3dCQUFTOzswQ0FDckMsOERBQUNwTSxrUEFBVUE7Z0NBQUNpSyxTQUFRO2dDQUFLckIsT0FBTTswQ0FBZ0I7Ozs7OzswQ0FHL0MsOERBQUM1SSxrUEFBVUE7Z0NBQUNpSyxTQUFRO2dDQUFRckIsT0FBTTtnQ0FBZ0JpQixJQUFJO29DQUFFTyxJQUFJO2dDQUFFOzBDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRM0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYmFoaW5saW5rL3dlYi1hZG1pbi8uL3NyYy9hcHAvYW5hbHl0aWNzL3BhZ2UuanM/ODY2YSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBCYWhpbkxpbmsgV2ViIEFkbWluIEFuYWx5dGljcyBEYXNoYm9hcmQgUGFnZVxuLy8g4pqg77iPIENSSVRJQ0FMOiBSZWFsIGFuYWx5dGljcyB3aXRoIGxpdmUgZGF0YSB2aXN1YWxpemF0aW9uIE9OTFlcblxuJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VBdXRoLCB1c2VVc2VyIH0gZnJvbSAnQGNsZXJrL25leHRqcyc7XG5pbXBvcnQgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgcmVkaXJlY3QgfSBmcm9tICduZXh0L25hdmlnYXRpb24nO1xuaW1wb3J0IHtcbiAgQm94LFxuICBUeXBvZ3JhcGh5LFxuICBHcmlkLFxuICBDYXJkLFxuICBDYXJkQ29udGVudCxcbiAgRm9ybUNvbnRyb2wsXG4gIElucHV0TGFiZWwsXG4gIFNlbGVjdCxcbiAgTWVudUl0ZW0sXG4gIEFsZXJ0LFxuICBDaXJjdWxhclByb2dyZXNzLFxuICBCdXR0b24sXG4gIENoaXAsXG4gIEF2YXRhcixcbiAgTGluZWFyUHJvZ3Jlc3MsXG4gIERpdmlkZXIsXG4gIEljb25CdXR0b24sXG4gIFRvb2x0aXAsXG4gIFBhcGVyXG59IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xuaW1wb3J0IHtcbiAgUmVmcmVzaCBhcyBSZWZyZXNoSWNvbixcbiAgRG93bmxvYWQgYXMgRG93bmxvYWRJY29uLFxuICBUcmVuZGluZ1VwIGFzIFRyZW5kaW5nVXBJY29uLFxuICBUcmVuZGluZ0Rvd24gYXMgVHJlbmRpbmdEb3duSWNvbixcbiAgU2NoZWR1bGUgYXMgU2NoZWR1bGVJY29uLFxuICBBc3NpZ25tZW50IGFzIEFzc2lnbm1lbnRJY29uLFxuICBQZW9wbGUgYXMgUGVvcGxlSWNvbixcbiAgU2VjdXJpdHkgYXMgU2VjdXJpdHlJY29uLFxuICBMb2NhdGlvbk9uIGFzIExvY2F0aW9uSWNvbixcbiAgV2FybmluZyBhcyBXYXJuaW5nSWNvbixcbiAgQ2hlY2tDaXJjbGUgYXMgQ2hlY2tJY29uLFxuICBFcnJvciBhcyBFcnJvckljb24sXG4gIFRpbWVsaW5lIGFzIFRpbWVsaW5lSWNvbixcbiAgQXNzZXNzbWVudCBhcyBBc3Nlc3NtZW50SWNvbixcbiAgU3BlZWQgYXMgU3BlZWRJY29uLFxuICBTdGFyIGFzIFN0YXJJY29uXG59IGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwnO1xuaW1wb3J0IHsgTGluZUNoYXJ0LCBMaW5lLCBBcmVhQ2hhcnQsIEFyZWEsIEJhckNoYXJ0LCBCYXIsIFBpZUNoYXJ0LCBQaWUsIENlbGwsIFhBeGlzLCBZQXhpcywgQ2FydGVzaWFuR3JpZCwgVG9vbHRpcCBhcyBSZWNoYXJ0c1Rvb2x0aXAsIFJlc3BvbnNpdmVDb250YWluZXIsIExlZ2VuZCB9IGZyb20gJ3JlY2hhcnRzJztcbmltcG9ydCBNb2Rlcm5TaWRlYmFyIGZyb20gJy4uLy4uL2NvbXBvbmVudHMvTW9kZXJuU2lkZWJhcic7XG5pbXBvcnQgQW5hbHl0aWNzQ2hhcnRzIGZyb20gJy4uLy4uL2NvbXBvbmVudHMvQW5hbHl0aWNzQ2hhcnRzJztcbmltcG9ydCBBcGlTZXJ2aWNlIGZyb20gJy4uLy4uL3NlcnZpY2VzL0FwaVNlcnZpY2UnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBbmFseXRpY3NQYWdlKCkge1xuICBjb25zdCB7IGlzTG9hZGVkLCBpc1NpZ25lZEluIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlVXNlcigpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW3RpbWVSYW5nZSwgc2V0VGltZVJhbmdlXSA9IHVzZVN0YXRlKCc3ZCcpO1xuICBjb25zdCBbcmVmcmVzaGluZywgc2V0UmVmcmVzaGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgLy8gUmVhbCBhbmFseXRpY3MgZGF0YSBmcm9tIGRhdGFiYXNlXG4gIGNvbnN0IFtkYXNoYm9hcmRNZXRyaWNzLCBzZXREYXNoYm9hcmRNZXRyaWNzXSA9IHVzZVN0YXRlKHtcbiAgICB0b3RhbEFnZW50czogMCxcbiAgICBhY3RpdmVTaGlmdHM6IDAsXG4gICAgY29tcGxldGVkU2hpZnRzOiAwLFxuICAgIHRvdGFsU2l0ZXM6IDAsXG4gICAgdG90YWxSZXBvcnRzOiAwLFxuICAgIGF2ZXJhZ2VSZXNwb25zZVRpbWU6IDAsXG4gICAgZWZmaWNpZW5jeTogMCxcbiAgICBjbGllbnRTYXRpc2ZhY3Rpb246IDBcbiAgfSk7XG5cbiAgY29uc3QgW3BlcmZvcm1hbmNlRGF0YSwgc2V0UGVyZm9ybWFuY2VEYXRhXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2FnZW50UGVyZm9ybWFuY2UsIHNldEFnZW50UGVyZm9ybWFuY2VdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbc2l0ZUFjdGl2aXR5LCBzZXRTaXRlQWN0aXZpdHldID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbcmVjZW50QWxlcnRzLCBzZXRSZWNlbnRBbGVydHNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbc2hpZnRUcmVuZHMsIHNldFNoaWZ0VHJlbmRzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2FuYWx5dGljc0RhdGEsIHNldEFuYWx5dGljc0RhdGFdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtrcGlzLCBzZXRLcGlzXSA9IHVzZVN0YXRlKHtcbiAgICB0b3RhbFNoaWZ0czogMCxcbiAgICBjb21wbGV0ZWRTaGlmdHM6IDAsXG4gICAgdG90YWxIb3VyczogMCxcbiAgICBhdmVyYWdlUmF0aW5nOiAwLFxuICAgIGFjdGl2ZUFnZW50czogMCxcbiAgICB0b3RhbFJlcG9ydHM6IDAsXG4gICAgcmVzcG9uc2VUaW1lOiAwLFxuICAgIGVmZmljaWVuY3k6IDBcbiAgfSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNMb2FkZWQgJiYgIWlzU2lnbmVkSW4pIHtcbiAgICAgIHJlZGlyZWN0KCcvc2lnbi1pbicpO1xuICAgIH1cblxuICAgIGlmIChpc0xvYWRlZCAmJiBpc1NpZ25lZEluKSB7XG4gICAgICBsb2FkQW5hbHl0aWNzRGF0YSgpO1xuXG4gICAgICAvLyBBdXRvLXJlZnJlc2ggZXZlcnkgMzAgc2Vjb25kcyBmb3IgcmVhbC10aW1lIGRhdGFcbiAgICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwobG9hZEFuYWx5dGljc0RhdGEsIDMwMDAwKTtcbiAgICAgIHJldHVybiAoKSA9PiBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgICB9XG4gIH0sIFtpc0xvYWRlZCwgaXNTaWduZWRJbiwgdGltZVJhbmdlXSk7XG5cbiAgY29uc3QgbG9hZEFuYWx5dGljc0RhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcihudWxsKTtcblxuICAgICAgY29uc3QgW1xuICAgICAgICBzaGlmdHNSZXNwb25zZSxcbiAgICAgICAgcmVwb3J0c1Jlc3BvbnNlLFxuICAgICAgICBhZ2VudHNSZXNwb25zZSxcbiAgICAgICAga3BpUmVzcG9uc2VcbiAgICAgIF0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgIEFwaVNlcnZpY2UuZ2V0KCcvYW5hbHl0aWNzL3NoaWZ0cycsIHsgdGltZVJhbmdlIH0pLFxuICAgICAgICBBcGlTZXJ2aWNlLmdldCgnL2FuYWx5dGljcy9yZXBvcnRzJywgeyB0aW1lUmFuZ2UgfSksXG4gICAgICAgIEFwaVNlcnZpY2UuZ2V0KCcvYW5hbHl0aWNzL2FnZW50cycsIHsgdGltZVJhbmdlIH0pLFxuICAgICAgICBBcGlTZXJ2aWNlLmdldCgnL2FuYWx5dGljcy9rcGlzJywgeyB0aW1lUmFuZ2UgfSlcbiAgICAgIF0pO1xuXG4gICAgICAvLyBDb21iaW5lIGFsbCBhbmFseXRpY3MgZGF0YVxuICAgICAgY29uc3QgY29tYmluZWREYXRhID0ge1xuICAgICAgICBvdmVydmlldzoga3BpUmVzcG9uc2Uuc3VjY2VzcyA/IGtwaVJlc3BvbnNlLmRhdGEgOiB7fSxcbiAgICAgICAgc2hpZnRUcmVuZHM6IHNoaWZ0c1Jlc3BvbnNlLnN1Y2Nlc3MgPyBzaGlmdHNSZXNwb25zZS5kYXRhLnRyZW5kcyA6IFtdLFxuICAgICAgICByZXBvcnRUeXBlczogcmVwb3J0c1Jlc3BvbnNlLnN1Y2Nlc3MgPyByZXBvcnRzUmVzcG9uc2UuZGF0YS50eXBlcyA6IFtdLFxuICAgICAgICBhZ2VudFBlcmZvcm1hbmNlOiBhZ2VudHNSZXNwb25zZS5zdWNjZXNzID8gYWdlbnRzUmVzcG9uc2UuZGF0YS5wZXJmb3JtYW5jZSA6IFtdLFxuICAgICAgICBzaXRlQWN0aXZpdHk6IHNoaWZ0c1Jlc3BvbnNlLnN1Y2Nlc3MgPyBzaGlmdHNSZXNwb25zZS5kYXRhLnNpdGVBY3Rpdml0eSA6IFtdXG4gICAgICB9O1xuXG4gICAgICBzZXRBbmFseXRpY3NEYXRhKGNvbWJpbmVkRGF0YSk7XG5cbiAgICAgIC8vIFNldCBLUElzXG4gICAgICBpZiAoa3BpUmVzcG9uc2Uuc3VjY2Vzcykge1xuICAgICAgICBzZXRLcGlzKGtwaVJlc3BvbnNlLmRhdGEpO1xuICAgICAgfVxuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgYW5hbHl0aWNzIGRhdGE6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIGFuYWx5dGljcyBkYXRhJyk7XG4gICAgICBcbiAgICAgIC8vIEZhbGxiYWNrIHRvIHNhbXBsZSBkYXRhIGZvciBkZW1vXG4gICAgICBzZXRBbmFseXRpY3NEYXRhKHtcbiAgICAgICAgb3ZlcnZpZXc6IHtcbiAgICAgICAgICB0b3RhbFNoaWZ0czogMTU2LFxuICAgICAgICAgIGFjdGl2ZVNoaWZ0czogMTIsXG4gICAgICAgICAgdG90YWxBZ2VudHM6IDI0LFxuICAgICAgICAgIGFjdGl2ZUFnZW50czogMTgsXG4gICAgICAgICAgdG90YWxTaXRlczogOCxcbiAgICAgICAgICBhY3RpdmVTaXRlczogNixcbiAgICAgICAgICB0b3RhbFJlcG9ydHM6IDg5LFxuICAgICAgICAgIHBlbmRpbmdSZXBvcnRzOiA1LFxuICAgICAgICAgIHNoaWZ0c0NoYW5nZTogMTIuNSxcbiAgICAgICAgICBhZ2VudHNDaGFuZ2U6IC0yLjEsXG4gICAgICAgICAgcmVwb3J0c0NoYW5nZTogOC4zXG4gICAgICAgIH0sXG4gICAgICAgIHNoaWZ0VHJlbmRzOiBbXG4gICAgICAgICAgeyBkYXRlOiAnMjAyNC0wMS0wMScsIHNjaGVkdWxlZDogMjAsIGNvbXBsZXRlZDogMTgsIGNhbmNlbGxlZDogMiB9LFxuICAgICAgICAgIHsgZGF0ZTogJzIwMjQtMDEtMDInLCBzY2hlZHVsZWQ6IDIyLCBjb21wbGV0ZWQ6IDIwLCBjYW5jZWxsZWQ6IDEgfSxcbiAgICAgICAgICB7IGRhdGU6ICcyMDI0LTAxLTAzJywgc2NoZWR1bGVkOiAxOCwgY29tcGxldGVkOiAxNywgY2FuY2VsbGVkOiAxIH0sXG4gICAgICAgICAgeyBkYXRlOiAnMjAyNC0wMS0wNCcsIHNjaGVkdWxlZDogMjUsIGNvbXBsZXRlZDogMjMsIGNhbmNlbGxlZDogMiB9LFxuICAgICAgICAgIHsgZGF0ZTogJzIwMjQtMDEtMDUnLCBzY2hlZHVsZWQ6IDIxLCBjb21wbGV0ZWQ6IDE5LCBjYW5jZWxsZWQ6IDEgfSxcbiAgICAgICAgICB7IGRhdGU6ICcyMDI0LTAxLTA2Jywgc2NoZWR1bGVkOiAxOSwgY29tcGxldGVkOiAxOCwgY2FuY2VsbGVkOiAxIH0sXG4gICAgICAgICAgeyBkYXRlOiAnMjAyNC0wMS0wNycsIHNjaGVkdWxlZDogMjMsIGNvbXBsZXRlZDogMjEsIGNhbmNlbGxlZDogMiB9XG4gICAgICAgIF0sXG4gICAgICAgIHJlcG9ydFR5cGVzOiBbXG4gICAgICAgICAgeyBuYW1lOiAnU2VjdXJpdHknLCB2YWx1ZTogMzUsIGNvbG9yOiAnI0RDMzU0NScgfSxcbiAgICAgICAgICB7IG5hbWU6ICdNYWludGVuYW5jZScsIHZhbHVlOiAyNSwgY29sb3I6ICcjRkZDMTA3JyB9LFxuICAgICAgICAgIHsgbmFtZTogJ0luY2lkZW50JywgdmFsdWU6IDIwLCBjb2xvcjogJyNEQzAwNEUnIH0sXG4gICAgICAgICAgeyBuYW1lOiAnR2VuZXJhbCcsIHZhbHVlOiAxNSwgY29sb3I6ICcjMTk3NkQyJyB9LFxuICAgICAgICAgIHsgbmFtZTogJ1NhZmV0eScsIHZhbHVlOiA1LCBjb2xvcjogJyMyRTdEMzInIH1cbiAgICAgICAgXSxcbiAgICAgICAgYWdlbnRQZXJmb3JtYW5jZTogW1xuICAgICAgICAgIHsgbmFtZTogJ0pvaG4gRG9lJywgc2hpZnRzOiAxNSwgb25UaW1lOiAxNCwgcmVwb3J0czogOCwgcmF0aW5nOiA0LjggfSxcbiAgICAgICAgICB7IG5hbWU6ICdKYW5lIFNtaXRoJywgc2hpZnRzOiAxMiwgb25UaW1lOiAxMiwgcmVwb3J0czogNiwgcmF0aW5nOiA0LjkgfSxcbiAgICAgICAgICB7IG5hbWU6ICdNaWtlIEpvaG5zb24nLCBzaGlmdHM6IDE4LCBvblRpbWU6IDE2LCByZXBvcnRzOiAxMiwgcmF0aW5nOiA0LjYgfSxcbiAgICAgICAgICB7IG5hbWU6ICdTYXJhaCBXaWxzb24nLCBzaGlmdHM6IDE0LCBvblRpbWU6IDEzLCByZXBvcnRzOiA5LCByYXRpbmc6IDQuNyB9LFxuICAgICAgICAgIHsgbmFtZTogJ0RhdmlkIEJyb3duJywgc2hpZnRzOiAxNiwgb25UaW1lOiAxNSwgcmVwb3J0czogNywgcmF0aW5nOiA0LjUgfVxuICAgICAgICBdLFxuICAgICAgICBzaXRlQWN0aXZpdHk6IFtcbiAgICAgICAgICB7IHNpdGU6ICdEb3dudG93biBNYWxsJywgc2hpZnRzOiA0NSwgaW5jaWRlbnRzOiAzLCBlZmZpY2llbmN5OiA5NSB9LFxuICAgICAgICAgIHsgc2l0ZTogJ09mZmljZSBDb21wbGV4Jywgc2hpZnRzOiAzOCwgaW5jaWRlbnRzOiAxLCBlZmZpY2llbmN5OiA5OCB9LFxuICAgICAgICAgIHsgc2l0ZTogJ1dhcmVob3VzZSBBJywgc2hpZnRzOiAzMiwgaW5jaWRlbnRzOiAyLCBlZmZpY2llbmN5OiA5MiB9LFxuICAgICAgICAgIHsgc2l0ZTogJ1JldGFpbCBDZW50ZXInLCBzaGlmdHM6IDI4LCBpbmNpZGVudHM6IDQsIGVmZmljaWVuY3k6IDg4IH0sXG4gICAgICAgICAgeyBzaXRlOiAnSW5kdXN0cmlhbCBQYXJrJywgc2hpZnRzOiAyNSwgaW5jaWRlbnRzOiAxLCBlZmZpY2llbmN5OiA5NiB9XG4gICAgICAgIF1cbiAgICAgIH0pO1xuXG4gICAgICBzZXRLcGlzKHtcbiAgICAgICAgdG90YWxTaGlmdHM6IDE1NixcbiAgICAgICAgY29tcGxldGVkU2hpZnRzOiAxNDIsXG4gICAgICAgIHRvdGFsSG91cnM6IDEyNDgsXG4gICAgICAgIGF2ZXJhZ2VSYXRpbmc6IDQuNyxcbiAgICAgICAgYWN0aXZlQWdlbnRzOiAxOCxcbiAgICAgICAgdG90YWxSZXBvcnRzOiA4OSxcbiAgICAgICAgcmVzcG9uc2VUaW1lOiAxMi41LFxuICAgICAgICBlZmZpY2llbmN5OiA5NC4yXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUV4cG9ydCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgLy8gSW4gYSByZWFsIGFwcCwgdGhpcyB3b3VsZCBnZW5lcmF0ZSBhbmQgZG93bmxvYWQgYW5hbHl0aWNzIHJlcG9ydFxuICAgICAgY29uc29sZS5sb2coJ0V4cG9ydGluZyBhbmFseXRpY3MgZGF0YS4uLicpO1xuICAgICAgYWxlcnQoJ0V4cG9ydCBmdW5jdGlvbmFsaXR5IHdvdWxkIGJlIGltcGxlbWVudGVkIGhlcmUnKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZXhwb3J0aW5nIGFuYWx5dGljczonLCBlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IEtQSUNhcmQgPSAoeyB0aXRsZSwgdmFsdWUsIHVuaXQgPSAnJywgaWNvbjogSWNvbiwgY29sb3IgPSAncHJpbWFyeScsIHRyZW5kIH0pID0+IChcbiAgICA8Q2FyZCBlbGV2YXRpb249ezJ9PlxuICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicgfX0+XG4gICAgICAgICAgPEJveD5cbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNFwiIGNvbXBvbmVudD1cImRpdlwiIGNvbG9yPXtgJHtjb2xvcn0ubWFpbmB9PlxuICAgICAgICAgICAgICB7dmFsdWV9e3VuaXR9XG4gICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBjb2xvcj1cInRleHRTZWNvbmRhcnlcIj5cbiAgICAgICAgICAgICAge3RpdGxlfVxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAge3RyZW5kICE9PSB1bmRlZmluZWQgJiYgKFxuICAgICAgICAgICAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgYWxpZ25JdGVtczogJ2NlbnRlcicsIG10OiAxIH19PlxuICAgICAgICAgICAgICAgIDxUcmVuZGluZ1VwSWNvbiBcbiAgICAgICAgICAgICAgICAgIGNvbG9yPXt0cmVuZCA+PSAwID8gJ3N1Y2Nlc3MnIDogJ2Vycm9yJ30gXG4gICAgICAgICAgICAgICAgICBmb250U2l6ZT1cInNtYWxsXCIgXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSBcbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJjYXB0aW9uXCIgXG4gICAgICAgICAgICAgICAgICBjb2xvcj17dHJlbmQgPj0gMCA/ICdzdWNjZXNzLm1haW4nIDogJ2Vycm9yLm1haW4nfVxuICAgICAgICAgICAgICAgICAgc3g9e3sgbWw6IDAuNSB9fVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHtNYXRoLmFicyh0cmVuZCl9JVxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQm94PlxuICAgICAgICAgIDxJY29uIHN4PXt7IGZvbnRTaXplOiA0MCwgY29sb3I6IGAke2NvbG9yfS5tYWluYCwgb3BhY2l0eTogMC43IH19IC8+XG4gICAgICAgIDwvQm94PlxuICAgICAgPC9DYXJkQ29udGVudD5cbiAgICA8L0NhcmQ+XG4gICk7XG5cbiAgcmV0dXJuIChcbiAgICA8Qm94IHN4PXt7IGRpc3BsYXk6ICdmbGV4JywgbWluSGVpZ2h0OiAnMTAwdmgnLCBiYWNrZ3JvdW5kQ29sb3I6ICcjZjhmYWZjJyB9fT5cbiAgICAgIDxNb2Rlcm5TaWRlYmFyIC8+XG5cbiAgICAgIDxCb3ggY29tcG9uZW50PVwibWFpblwiIHN4PXt7IGZsZXhHcm93OiAxLCBwOiAzIH19PlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxCb3ggc3g9e3sgbWI6IDMsIGRpc3BsYXk6ICdmbGV4JywganVzdGlmeUNvbnRlbnQ6ICdzcGFjZS1iZXR3ZWVuJywgYWxpZ25JdGVtczogJ2NlbnRlcicgfX0+XG4gICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNFwiIGZvbnRXZWlnaHQ9XCJib2xkXCI+XG4gICAgICAgICAgQW5hbHl0aWNzIERhc2hib2FyZFxuICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBnYXA6IDEsIGFsaWduSXRlbXM6ICdjZW50ZXInIH19PlxuICAgICAgICAgIDxGb3JtQ29udHJvbCBzaXplPVwic21hbGxcIiBzeD17eyBtaW5XaWR0aDogMTIwIH19PlxuICAgICAgICAgICAgPElucHV0TGFiZWw+VGltZSBSYW5nZTwvSW5wdXRMYWJlbD5cbiAgICAgICAgICAgIDxTZWxlY3RcbiAgICAgICAgICAgICAgdmFsdWU9e3RpbWVSYW5nZX1cbiAgICAgICAgICAgICAgbGFiZWw9XCJUaW1lIFJhbmdlXCJcbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRUaW1lUmFuZ2UoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8TWVudUl0ZW0gdmFsdWU9XCIxZFwiPkxhc3QgMjQgSG91cnM8L01lbnVJdGVtPlxuICAgICAgICAgICAgICA8TWVudUl0ZW0gdmFsdWU9XCI3ZFwiPkxhc3QgNyBEYXlzPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgPE1lbnVJdGVtIHZhbHVlPVwiMzBkXCI+TGFzdCAzMCBEYXlzPC9NZW51SXRlbT5cbiAgICAgICAgICAgICAgPE1lbnVJdGVtIHZhbHVlPVwiOTBkXCI+TGFzdCA5MCBEYXlzPC9NZW51SXRlbT5cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvRm9ybUNvbnRyb2w+XG4gICAgICAgICAgXG4gICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVkXCJcbiAgICAgICAgICAgIHN0YXJ0SWNvbj17PERvd25sb2FkSWNvbiAvPn1cbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZUV4cG9ydH1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBFeHBvcnRcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICBcbiAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxuICAgICAgICAgICAgc3RhcnRJY29uPXs8UmVmcmVzaEljb24gLz59XG4gICAgICAgICAgICBvbkNsaWNrPXtsb2FkQW5hbHl0aWNzRGF0YX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICBSZWZyZXNoXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvQm94PlxuICAgICAgPC9Cb3g+XG5cbiAgICAgIHsvKiBFcnJvciBBbGVydCAqL31cbiAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgIDxBbGVydCBzZXZlcml0eT1cIndhcm5pbmdcIiBzeD17eyBtYjogMiB9fT5cbiAgICAgICAgICB7ZXJyb3J9IC0gU2hvd2luZyBzYW1wbGUgZGF0YSBmb3IgZGVtb25zdHJhdGlvblxuICAgICAgICA8L0FsZXJ0PlxuICAgICAgKX1cblxuICAgICAgey8qIEtQSSBDYXJkcyAqL31cbiAgICAgIDxHcmlkIGNvbnRhaW5lciBzcGFjaW5nPXszfSBzeD17eyBtYjogNCB9fT5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17M30+XG4gICAgICAgICAgPEtQSUNhcmRcbiAgICAgICAgICAgIHRpdGxlPVwiVG90YWwgU2hpZnRzXCJcbiAgICAgICAgICAgIHZhbHVlPXtrcGlzLnRvdGFsU2hpZnRzfVxuICAgICAgICAgICAgaWNvbj17U2NoZWR1bGVJY29ufVxuICAgICAgICAgICAgY29sb3I9XCJwcmltYXJ5XCJcbiAgICAgICAgICAgIHRyZW5kPXsxMi41fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvR3JpZD5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17M30+XG4gICAgICAgICAgPEtQSUNhcmRcbiAgICAgICAgICAgIHRpdGxlPVwiQ29tcGxldGlvbiBSYXRlXCJcbiAgICAgICAgICAgIHZhbHVlPXsoKGtwaXMuY29tcGxldGVkU2hpZnRzIC8ga3Bpcy50b3RhbFNoaWZ0cykgKiAxMDApLnRvRml4ZWQoMSl9XG4gICAgICAgICAgICB1bml0PVwiJVwiXG4gICAgICAgICAgICBpY29uPXtUcmVuZGluZ1VwSWNvbn1cbiAgICAgICAgICAgIGNvbG9yPVwic3VjY2Vzc1wiXG4gICAgICAgICAgICB0cmVuZD17NS4yfVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvR3JpZD5cbiAgICAgICAgPEdyaWQgaXRlbSB4cz17MTJ9IHNtPXs2fSBtZD17M30+XG4gICAgICAgICAgPEtQSUNhcmRcbiAgICAgICAgICAgIHRpdGxlPVwiVG90YWwgSG91cnNcIlxuICAgICAgICAgICAgdmFsdWU9e2twaXMudG90YWxIb3Vycy50b0xvY2FsZVN0cmluZygpfVxuICAgICAgICAgICAgdW5pdD1cImhcIlxuICAgICAgICAgICAgaWNvbj17U2NoZWR1bGVJY29ufVxuICAgICAgICAgICAgY29sb3I9XCJpbmZvXCJcbiAgICAgICAgICAgIHRyZW5kPXs4Ljd9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9HcmlkPlxuICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXszfT5cbiAgICAgICAgICA8S1BJQ2FyZFxuICAgICAgICAgICAgdGl0bGU9XCJBdmVyYWdlIFJhdGluZ1wiXG4gICAgICAgICAgICB2YWx1ZT17a3Bpcy5hdmVyYWdlUmF0aW5nfVxuICAgICAgICAgICAgdW5pdD1cIi81XCJcbiAgICAgICAgICAgIGljb249e1RyZW5kaW5nVXBJY29ufVxuICAgICAgICAgICAgY29sb3I9XCJ3YXJuaW5nXCJcbiAgICAgICAgICAgIHRyZW5kPXsyLjF9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9HcmlkPlxuICAgICAgPC9HcmlkPlxuXG4gICAgICB7LyogU2Vjb25kIFJvdyBLUElzICovfVxuICAgICAgPEdyaWQgY29udGFpbmVyIHNwYWNpbmc9ezN9IHN4PXt7IG1iOiA0IH19PlxuICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXszfT5cbiAgICAgICAgICA8S1BJQ2FyZFxuICAgICAgICAgICAgdGl0bGU9XCJBY3RpdmUgQWdlbnRzXCJcbiAgICAgICAgICAgIHZhbHVlPXtrcGlzLmFjdGl2ZUFnZW50c31cbiAgICAgICAgICAgIGljb249e1Blb3BsZUljb259XG4gICAgICAgICAgICBjb2xvcj1cInNlY29uZGFyeVwiXG4gICAgICAgICAgICB0cmVuZD17LTEuNX1cbiAgICAgICAgICAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezN9PlxuICAgICAgICAgIDxLUElDYXJkXG4gICAgICAgICAgICB0aXRsZT1cIlRvdGFsIFJlcG9ydHNcIlxuICAgICAgICAgICAgdmFsdWU9e2twaXMudG90YWxSZXBvcnRzfVxuICAgICAgICAgICAgaWNvbj17QXNzaWdubWVudEljb259XG4gICAgICAgICAgICBjb2xvcj1cInByaW1hcnlcIlxuICAgICAgICAgICAgdHJlbmQ9ezE1LjN9XG4gICAgICAgICAgLz5cbiAgICAgICAgPC9HcmlkPlxuICAgICAgICA8R3JpZCBpdGVtIHhzPXsxMn0gc209ezZ9IG1kPXszfT5cbiAgICAgICAgICA8S1BJQ2FyZFxuICAgICAgICAgICAgdGl0bGU9XCJBdmcgUmVzcG9uc2UgVGltZVwiXG4gICAgICAgICAgICB2YWx1ZT17a3Bpcy5yZXNwb25zZVRpbWV9XG4gICAgICAgICAgICB1bml0PVwibWluXCJcbiAgICAgICAgICAgIGljb249e1NjaGVkdWxlSWNvbn1cbiAgICAgICAgICAgIGNvbG9yPVwid2FybmluZ1wiXG4gICAgICAgICAgICB0cmVuZD17LTguMn1cbiAgICAgICAgICAvPlxuICAgICAgICA8L0dyaWQ+XG4gICAgICAgIDxHcmlkIGl0ZW0geHM9ezEyfSBzbT17Nn0gbWQ9ezN9PlxuICAgICAgICAgIDxLUElDYXJkXG4gICAgICAgICAgICB0aXRsZT1cIkVmZmljaWVuY3lcIlxuICAgICAgICAgICAgdmFsdWU9e2twaXMuZWZmaWNpZW5jeX1cbiAgICAgICAgICAgIHVuaXQ9XCIlXCJcbiAgICAgICAgICAgIGljb249e1NlY3VyaXR5SWNvbn1cbiAgICAgICAgICAgIGNvbG9yPVwic3VjY2Vzc1wiXG4gICAgICAgICAgICB0cmVuZD17My40fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvR3JpZD5cbiAgICAgIDwvR3JpZD5cblxuICAgICAgey8qIENoYXJ0cyBTZWN0aW9uICovfVxuICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsIHB5OiA4IH19PlxuICAgICAgICAgIDxDaXJjdWxhclByb2dyZXNzIHNpemU9ezYwfSAvPlxuICAgICAgICA8L0JveD5cbiAgICAgICkgOiBhbmFseXRpY3NEYXRhID8gKFxuICAgICAgICA8QW5hbHl0aWNzQ2hhcnRzXG4gICAgICAgICAgZGF0YT17YW5hbHl0aWNzRGF0YX1cbiAgICAgICAgICBsb2FkaW5nPXtsb2FkaW5nfVxuICAgICAgICAgIHRpbWVSYW5nZT17dGltZVJhbmdlfVxuICAgICAgICAgIG9uVGltZVJhbmdlQ2hhbmdlPXtzZXRUaW1lUmFuZ2V9XG4gICAgICAgIC8+XG4gICAgICApIDogKFxuICAgICAgICA8UGFwZXIgc3g9e3sgcDogNCwgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDZcIiBjb2xvcj1cInRleHRTZWNvbmRhcnlcIj5cbiAgICAgICAgICAgIE5vIGFuYWx5dGljcyBkYXRhIGF2YWlsYWJsZVxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBjb2xvcj1cInRleHRTZWNvbmRhcnlcIiBzeD17eyBtdDogMSB9fT5cbiAgICAgICAgICAgIERhdGEgd2lsbCBhcHBlYXIgaGVyZSBvbmNlIHNoaWZ0cyBhbmQgcmVwb3J0cyBhcmUgY3JlYXRlZFxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgPC9QYXBlcj5cbiAgICAgICl9XG4gICAgICA8L0JveD5cbiAgICA8L0JveD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VBdXRoIiwidXNlVXNlciIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwicmVkaXJlY3QiLCJCb3giLCJUeXBvZ3JhcGh5IiwiR3JpZCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkZvcm1Db250cm9sIiwiSW5wdXRMYWJlbCIsIlNlbGVjdCIsIk1lbnVJdGVtIiwiQWxlcnQiLCJDaXJjdWxhclByb2dyZXNzIiwiQnV0dG9uIiwiQ2hpcCIsIkF2YXRhciIsIkxpbmVhclByb2dyZXNzIiwiRGl2aWRlciIsIkljb25CdXR0b24iLCJUb29sdGlwIiwiUGFwZXIiLCJSZWZyZXNoIiwiUmVmcmVzaEljb24iLCJEb3dubG9hZCIsIkRvd25sb2FkSWNvbiIsIlRyZW5kaW5nVXAiLCJUcmVuZGluZ1VwSWNvbiIsIlRyZW5kaW5nRG93biIsIlRyZW5kaW5nRG93bkljb24iLCJTY2hlZHVsZSIsIlNjaGVkdWxlSWNvbiIsIkFzc2lnbm1lbnQiLCJBc3NpZ25tZW50SWNvbiIsIlBlb3BsZSIsIlBlb3BsZUljb24iLCJTZWN1cml0eSIsIlNlY3VyaXR5SWNvbiIsIkxvY2F0aW9uT24iLCJMb2NhdGlvbkljb24iLCJXYXJuaW5nIiwiV2FybmluZ0ljb24iLCJDaGVja0NpcmNsZSIsIkNoZWNrSWNvbiIsIkVycm9yIiwiRXJyb3JJY29uIiwiVGltZWxpbmUiLCJUaW1lbGluZUljb24iLCJBc3Nlc3NtZW50IiwiQXNzZXNzbWVudEljb24iLCJTcGVlZCIsIlNwZWVkSWNvbiIsIlN0YXIiLCJTdGFySWNvbiIsIkxpbmVDaGFydCIsIkxpbmUiLCJBcmVhQ2hhcnQiLCJBcmVhIiwiQmFyQ2hhcnQiLCJCYXIiLCJQaWVDaGFydCIsIlBpZSIsIkNlbGwiLCJYQXhpcyIsIllBeGlzIiwiQ2FydGVzaWFuR3JpZCIsIlJlY2hhcnRzVG9vbHRpcCIsIlJlc3BvbnNpdmVDb250YWluZXIiLCJMZWdlbmQiLCJNb2Rlcm5TaWRlYmFyIiwiQW5hbHl0aWNzQ2hhcnRzIiwiQXBpU2VydmljZSIsIkFuYWx5dGljc1BhZ2UiLCJpc0xvYWRlZCIsImlzU2lnbmVkSW4iLCJ1c2VyIiwibG9hZGluZyIsInNldExvYWRpbmciLCJlcnJvciIsInNldEVycm9yIiwidGltZVJhbmdlIiwic2V0VGltZVJhbmdlIiwicmVmcmVzaGluZyIsInNldFJlZnJlc2hpbmciLCJkYXNoYm9hcmRNZXRyaWNzIiwic2V0RGFzaGJvYXJkTWV0cmljcyIsInRvdGFsQWdlbnRzIiwiYWN0aXZlU2hpZnRzIiwiY29tcGxldGVkU2hpZnRzIiwidG90YWxTaXRlcyIsInRvdGFsUmVwb3J0cyIsImF2ZXJhZ2VSZXNwb25zZVRpbWUiLCJlZmZpY2llbmN5IiwiY2xpZW50U2F0aXNmYWN0aW9uIiwicGVyZm9ybWFuY2VEYXRhIiwic2V0UGVyZm9ybWFuY2VEYXRhIiwiYWdlbnRQZXJmb3JtYW5jZSIsInNldEFnZW50UGVyZm9ybWFuY2UiLCJzaXRlQWN0aXZpdHkiLCJzZXRTaXRlQWN0aXZpdHkiLCJyZWNlbnRBbGVydHMiLCJzZXRSZWNlbnRBbGVydHMiLCJzaGlmdFRyZW5kcyIsInNldFNoaWZ0VHJlbmRzIiwiYW5hbHl0aWNzRGF0YSIsInNldEFuYWx5dGljc0RhdGEiLCJrcGlzIiwic2V0S3BpcyIsInRvdGFsU2hpZnRzIiwidG90YWxIb3VycyIsImF2ZXJhZ2VSYXRpbmciLCJhY3RpdmVBZ2VudHMiLCJyZXNwb25zZVRpbWUiLCJsb2FkQW5hbHl0aWNzRGF0YSIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwic2hpZnRzUmVzcG9uc2UiLCJyZXBvcnRzUmVzcG9uc2UiLCJhZ2VudHNSZXNwb25zZSIsImtwaVJlc3BvbnNlIiwiUHJvbWlzZSIsImFsbCIsImdldCIsImNvbWJpbmVkRGF0YSIsIm92ZXJ2aWV3Iiwic3VjY2VzcyIsImRhdGEiLCJ0cmVuZHMiLCJyZXBvcnRUeXBlcyIsInR5cGVzIiwicGVyZm9ybWFuY2UiLCJjb25zb2xlIiwiYWN0aXZlU2l0ZXMiLCJwZW5kaW5nUmVwb3J0cyIsInNoaWZ0c0NoYW5nZSIsImFnZW50c0NoYW5nZSIsInJlcG9ydHNDaGFuZ2UiLCJkYXRlIiwic2NoZWR1bGVkIiwiY29tcGxldGVkIiwiY2FuY2VsbGVkIiwibmFtZSIsInZhbHVlIiwiY29sb3IiLCJzaGlmdHMiLCJvblRpbWUiLCJyZXBvcnRzIiwicmF0aW5nIiwic2l0ZSIsImluY2lkZW50cyIsImhhbmRsZUV4cG9ydCIsImxvZyIsImFsZXJ0IiwiS1BJQ2FyZCIsInRpdGxlIiwidW5pdCIsImljb24iLCJJY29uIiwidHJlbmQiLCJlbGV2YXRpb24iLCJzeCIsImRpc3BsYXkiLCJhbGlnbkl0ZW1zIiwianVzdGlmeUNvbnRlbnQiLCJ2YXJpYW50IiwiY29tcG9uZW50IiwidW5kZWZpbmVkIiwibXQiLCJmb250U2l6ZSIsIm1sIiwiTWF0aCIsImFicyIsIm9wYWNpdHkiLCJtaW5IZWlnaHQiLCJiYWNrZ3JvdW5kQ29sb3IiLCJmbGV4R3JvdyIsInAiLCJtYiIsImZvbnRXZWlnaHQiLCJnYXAiLCJzaXplIiwibWluV2lkdGgiLCJsYWJlbCIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInN0YXJ0SWNvbiIsIm9uQ2xpY2siLCJzZXZlcml0eSIsImNvbnRhaW5lciIsInNwYWNpbmciLCJpdGVtIiwieHMiLCJzbSIsIm1kIiwidG9GaXhlZCIsInRvTG9jYWxlU3RyaW5nIiwicHkiLCJvblRpbWVSYW5nZUNoYW5nZSIsInRleHRBbGlnbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/analytics/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/AnalyticsCharts.js":
/*!*******************************************!*\
  !*** ./src/components/AnalyticsCharts.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/LinearProgress/LinearProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Box,Card,CardContent,Chip,Divider,FormControl,Grid,InputLabel,LinearProgress,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/Area.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/../../node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,People,Schedule,Security,TrendingDown,TrendingUp!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/TrendingUp.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,People,Schedule,Security,TrendingDown,TrendingUp!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/TrendingDown.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,People,Schedule,Security,TrendingDown,TrendingUp!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,People,Schedule,Security,TrendingDown,TrendingUp!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,People,Schedule,Security,TrendingDown,TrendingUp!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Security.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,People,Schedule,Security,TrendingDown,TrendingUp!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n// BahinLink Web Admin Analytics Charts Component\n// ⚠️ CRITICAL: Real analytics with live data visualization ONLY\n\n\n\n\n\nconst AnalyticsCharts = ({ data = {}, loading = false, timeRange = \"7d\", onTimeRangeChange })=>{\n    const [selectedMetric, setSelectedMetric] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"shifts\");\n    // Color schemes for charts\n    const colors = {\n        primary: \"#1976d2\",\n        secondary: \"#dc004e\",\n        success: \"#2e7d32\",\n        warning: \"#ed6c02\",\n        error: \"#d32f2f\",\n        info: \"#0288d1\"\n    };\n    const pieColors = [\n        colors.primary,\n        colors.secondary,\n        colors.success,\n        colors.warning,\n        colors.error,\n        colors.info\n    ];\n    // Sample data structure - in real app this would come from props\n    const defaultData = {\n        overview: {\n            totalShifts: 156,\n            activeShifts: 12,\n            totalAgents: 24,\n            activeAgents: 18,\n            totalSites: 8,\n            activeSites: 6,\n            totalReports: 89,\n            pendingReports: 5,\n            shiftsChange: 12.5,\n            agentsChange: -2.1,\n            reportsChange: 8.3\n        },\n        shiftTrends: [\n            {\n                date: \"2024-01-01\",\n                scheduled: 20,\n                completed: 18,\n                cancelled: 2\n            },\n            {\n                date: \"2024-01-02\",\n                scheduled: 22,\n                completed: 20,\n                cancelled: 1\n            },\n            {\n                date: \"2024-01-03\",\n                scheduled: 18,\n                completed: 17,\n                cancelled: 1\n            },\n            {\n                date: \"2024-01-04\",\n                scheduled: 25,\n                completed: 23,\n                cancelled: 2\n            },\n            {\n                date: \"2024-01-05\",\n                scheduled: 21,\n                completed: 19,\n                cancelled: 1\n            },\n            {\n                date: \"2024-01-06\",\n                scheduled: 19,\n                completed: 18,\n                cancelled: 1\n            },\n            {\n                date: \"2024-01-07\",\n                scheduled: 23,\n                completed: 21,\n                cancelled: 2\n            }\n        ],\n        reportTypes: [\n            {\n                name: \"Security\",\n                value: 35,\n                color: colors.error\n            },\n            {\n                name: \"Maintenance\",\n                value: 25,\n                color: colors.warning\n            },\n            {\n                name: \"Incident\",\n                value: 20,\n                color: colors.secondary\n            },\n            {\n                name: \"General\",\n                value: 15,\n                color: colors.primary\n            },\n            {\n                name: \"Safety\",\n                value: 5,\n                color: colors.success\n            }\n        ],\n        agentPerformance: [\n            {\n                name: \"John Doe\",\n                shifts: 15,\n                onTime: 14,\n                reports: 8,\n                rating: 4.8\n            },\n            {\n                name: \"Jane Smith\",\n                shifts: 12,\n                onTime: 12,\n                reports: 6,\n                rating: 4.9\n            },\n            {\n                name: \"Mike Johnson\",\n                shifts: 18,\n                onTime: 16,\n                reports: 12,\n                rating: 4.6\n            },\n            {\n                name: \"Sarah Wilson\",\n                shifts: 14,\n                onTime: 13,\n                reports: 9,\n                rating: 4.7\n            },\n            {\n                name: \"David Brown\",\n                shifts: 16,\n                onTime: 15,\n                reports: 7,\n                rating: 4.5\n            }\n        ],\n        siteActivity: [\n            {\n                site: \"Downtown Mall\",\n                shifts: 45,\n                incidents: 3,\n                efficiency: 95\n            },\n            {\n                site: \"Office Complex\",\n                shifts: 38,\n                incidents: 1,\n                efficiency: 98\n            },\n            {\n                site: \"Warehouse A\",\n                shifts: 32,\n                incidents: 2,\n                efficiency: 92\n            },\n            {\n                site: \"Retail Center\",\n                shifts: 28,\n                incidents: 4,\n                efficiency: 88\n            },\n            {\n                site: \"Industrial Park\",\n                shifts: 25,\n                incidents: 1,\n                efficiency: 96\n            }\n        ]\n    };\n    const chartData = {\n        ...defaultData,\n        ...data\n    };\n    const MetricCard = ({ title, value, change, icon: Icon, color = \"primary\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            elevation: 2,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: `${color}.main`,\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, undefined),\n                                change !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        mt: 1\n                                    },\n                                    children: [\n                                        change >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            color: \"success\",\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                            lineNumber: 129,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            color: \"error\",\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            variant: \"caption\",\n                                            color: change >= 0 ? \"success.main\" : \"error.main\",\n                                            sx: {\n                                                ml: 0.5\n                                            },\n                                            children: [\n                                                Math.abs(change),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: `${color}.main`,\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n            lineNumber: 116,\n            columnNumber: 5\n        }, undefined);\n    const CustomTooltip = ({ active, payload, label })=>{\n        if (active && payload && payload.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                sx: {\n                    p: 1,\n                    border: 1,\n                    borderColor: \"divider\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"body2\",\n                        fontWeight: \"medium\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 153,\n                        columnNumber: 11\n                    }, undefined),\n                    payload.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            variant: \"caption\",\n                            sx: {\n                                color: entry.color,\n                                display: \"block\"\n                            },\n                            children: [\n                                entry.name,\n                                \": \",\n                                entry.value\n                            ]\n                        }, index, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, undefined))\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                lineNumber: 152,\n                columnNumber: 9\n            }, undefined);\n        }\n        return null;\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            sx: {\n                width: \"100%\",\n                mt: 2\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                    lineNumber: 174,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    variant: \"body2\",\n                    sx: {\n                        mt: 1,\n                        textAlign: \"center\"\n                    },\n                    children: \"Loading analytics...\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                    lineNumber: 175,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                sx: {\n                    mb: 3,\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        variant: \"h5\",\n                        fontWeight: \"bold\",\n                        children: \"Analytics Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        size: \"small\",\n                        sx: {\n                            minWidth: 120\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                children: \"Time Range\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                lineNumber: 190,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                value: timeRange,\n                                label: \"Time Range\",\n                                onChange: (e)=>onTimeRangeChange && onTimeRangeChange(e.target.value),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        value: \"1d\",\n                                        children: \"Last 24 Hours\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        value: \"7d\",\n                                        children: \"Last 7 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        value: \"30d\",\n                                        children: \"Last 30 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        value: \"90d\",\n                                        children: \"Last 90 Days\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 189,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                lineNumber: 185,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                            title: \"Total Shifts\",\n                            value: chartData.overview.totalShifts,\n                            change: chartData.overview.shiftsChange,\n                            icon: _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                            color: \"primary\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 207,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                            title: \"Active Agents\",\n                            value: `${chartData.overview.activeAgents}/${chartData.overview.totalAgents}`,\n                            change: chartData.overview.agentsChange,\n                            icon: _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                            color: \"success\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 215,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                            title: \"Active Sites\",\n                            value: `${chartData.overview.activeSites}/${chartData.overview.totalSites}`,\n                            icon: _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                            color: \"info\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MetricCard, {\n                            title: \"Pending Reports\",\n                            value: `${chartData.overview.pendingReports}/${chartData.overview.totalReports}`,\n                            change: chartData.overview.reportsChange,\n                            icon: _barrel_optimize_names_Assignment_People_Schedule_Security_TrendingDown_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                            color: \"warning\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 233,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                lineNumber: 205,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                container: true,\n                spacing: 3,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        lg: 8,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            elevation: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Shift Trends\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_20__.AreaChart, {\n                                            data: chartData.shiftTrends,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.XAxis, {\n                                                    dataKey: \"date\",\n                                                    tickFormatter: (value)=>new Date(value).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 37\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"completed\",\n                                                    stackId: \"1\",\n                                                    stroke: colors.success,\n                                                    fill: colors.success,\n                                                    name: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_26__.Area, {\n                                                    type: \"monotone\",\n                                                    dataKey: \"cancelled\",\n                                                    stackId: \"1\",\n                                                    stroke: colors.error,\n                                                    fill: colors.error,\n                                                    name: \"Cancelled\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 246,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        lg: 4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            elevation: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Report Types\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 288,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_27__.PieChart, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_28__.Pie, {\n                                                    data: chartData.reportTypes,\n                                                    cx: \"50%\",\n                                                    cy: \"50%\",\n                                                    outerRadius: 80,\n                                                    fill: \"#8884d8\",\n                                                    dataKey: \"value\",\n                                                    label: ({ name, percent })=>`${name} ${(percent * 100).toFixed(0)}%`,\n                                                    children: chartData.reportTypes.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_29__.Cell, {\n                                                            fill: entry.color\n                                                        }, `cell-${index}`, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            elevation: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Agent Performance\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 317,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.BarChart, {\n                                            data: chartData.agentPerformance,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.XAxis, {\n                                                    dataKey: \"name\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.YAxis, {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 37\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Bar, {\n                                                    dataKey: \"shifts\",\n                                                    fill: colors.primary,\n                                                    name: \"Total Shifts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Bar, {\n                                                    dataKey: \"onTime\",\n                                                    fill: colors.success,\n                                                    name: \"On Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                            lineNumber: 321,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 320,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 315,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 314,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        lg: 6,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            elevation: 2,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Box_Card_CardContent_Chip_Divider_FormControl_Grid_InputLabel_LinearProgress_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        variant: \"h6\",\n                                        gutterBottom: true,\n                                        children: \"Site Activity\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 339,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_19__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_30__.BarChart, {\n                                            data: chartData.siteActivity,\n                                            layout: \"horizontal\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_21__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_22__.XAxis, {\n                                                    type: \"number\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_23__.YAxis, {\n                                                    dataKey: \"site\",\n                                                    type: \"category\",\n                                                    width: 100\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_24__.Tooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 37\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_25__.Legend, {}, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Bar, {\n                                                    dataKey: \"shifts\",\n                                                    fill: colors.primary,\n                                                    name: \"Shifts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_31__.Bar, {\n                                                    dataKey: \"incidents\",\n                                                    fill: colors.error,\n                                                    name: \"Incidents\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                            lineNumber: 343,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                            lineNumber: 337,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                        lineNumber: 336,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/AnalyticsCharts.js\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnalyticsCharts);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AnalyticsCharts.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ModernSidebar.js":
/*!*****************************************!*\
  !*** ./src/components/ModernSidebar.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/system/esm/colorManipulator.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemButton/ListItemButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assessment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Analytics.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Shield.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ChevronLeft.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ExitToApp.js\");\n// BahinLink Modern Sidebar Navigation Component\n// ⚠️ CRITICAL: Sophisticated 2024 design with real data integration ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst SIDEBAR_WIDTH_EXPANDED = 280;\nconst SIDEBAR_WIDTH_COLLAPSED = 72;\nconst ModernSidebar = ()=>{\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const { signOut } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modern 2024 color palette - sophisticated neutrals with accent\n    const colors = {\n        sidebar: \"#1a1d29\",\n        sidebarHover: \"#252936\",\n        accent: \"#6366f1\",\n        accentHover: \"#5855eb\",\n        text: \"#e2e8f0\",\n        textSecondary: \"#94a3b8\",\n        textMuted: \"#64748b\",\n        border: \"#334155\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\" // Modern red\n    };\n    const navigationItems = [\n        {\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            path: \"/\",\n            description: \"Overview & Analytics\"\n        },\n        {\n            label: \"Agents\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            path: \"/agents\",\n            description: \"Security Personnel\"\n        },\n        {\n            label: \"Sites\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/sites\",\n            description: \"Client Locations\"\n        },\n        {\n            label: \"Shifts\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            path: \"/shifts\",\n            description: \"Schedule Management\"\n        },\n        {\n            label: \"Reports\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            path: \"/reports\",\n            description: \"Security Reports\"\n        },\n        {\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            path: \"/analytics\",\n            description: \"Performance Metrics\"\n        },\n        {\n            label: \"Users\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            path: \"/users\",\n            description: \"User Management\"\n        }\n    ];\n    const handleNavigation = (path)=>{\n        router.push(path);\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/sign-in\");\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        variant: \"permanent\",\n        sx: {\n            width: sidebarWidth,\n            flexShrink: 0,\n            \"& .MuiDrawer-paper\": {\n                width: sidebarWidth,\n                boxSizing: \"border-box\",\n                backgroundColor: colors.sidebar,\n                borderRight: `1px solid ${colors.border}`,\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.enteringScreen\n                }),\n                overflowX: \"hidden\",\n                // Modern shadow\n                boxShadow: \"4px 0 24px rgba(0, 0, 0, 0.12)\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    p: 3,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: isCollapsed ? \"center\" : \"space-between\",\n                    borderBottom: `1px solid ${colors.border}`,\n                    minHeight: 80\n                },\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                sx: {\n                                    fontSize: 32,\n                                    color: colors.accent,\n                                    mr: 2,\n                                    filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            color: colors.text,\n                                            fontWeight: 700,\n                                            fontSize: \"1.25rem\",\n                                            letterSpacing: \"-0.025em\"\n                                        },\n                                        children: \"BahinLink\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: colors.textSecondary,\n                                            fontSize: \"0.75rem\",\n                                            fontWeight: 500\n                                        },\n                                        children: \"Security Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, undefined),\n                    isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        sx: {\n                            fontSize: 28,\n                            color: colors.accent,\n                            filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: toggleSidebar,\n                        sx: {\n                            color: colors.textSecondary,\n                            backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                            width: 32,\n                            height: 32,\n                            \"&:hover\": {\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                color: colors.text\n                            },\n                            transition: \"all 0.2s ease-in-out\"\n                        },\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 225,\n                            columnNumber: 26\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 225,\n                            columnNumber: 45\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    label: \"LIVE\",\n                    size: \"small\",\n                    sx: {\n                        backgroundColor: colors.success,\n                        color: \"white\",\n                        fontWeight: 600,\n                        fontSize: \"0.75rem\",\n                        height: 24,\n                        \"& .MuiChip-label\": {\n                            px: 1.5\n                        },\n                        display: isCollapsed ? \"none\" : \"flex\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    px: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        py: 0\n                    },\n                    children: navigationItems.map((item)=>{\n                        const Icon = item.icon;\n                        const active = isActive(item.path);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            disablePadding: true,\n                            sx: {\n                                mb: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                title: isCollapsed ? `${item.label} - ${item.description}` : \"\",\n                                placement: \"right\",\n                                arrow: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    onClick: ()=>handleNavigation(item.path),\n                                    sx: {\n                                        borderRadius: 2,\n                                        mx: 1,\n                                        px: 2,\n                                        py: 1.5,\n                                        minHeight: 48,\n                                        backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                        border: active ? `1px solid ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.3)}` : \"1px solid transparent\",\n                                        \"&:hover\": {\n                                            backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2) : (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                            transform: \"translateX(2px)\"\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            sx: {\n                                                color: active ? colors.accent : colors.textSecondary,\n                                                minWidth: isCollapsed ? \"auto\" : 40,\n                                                mr: isCollapsed ? 0 : 1.5,\n                                                transition: \"color 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                sx: {\n                                                    fontSize: 22\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                lineNumber: 290,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            primary: item.label,\n                                            secondary: item.description,\n                                            primaryTypographyProps: {\n                                                sx: {\n                                                    color: active ? colors.text : colors.textSecondary,\n                                                    fontWeight: active ? 600 : 500,\n                                                    fontSize: \"0.875rem\",\n                                                    transition: \"color 0.2s ease-in-out\"\n                                                }\n                                            },\n                                            secondaryTypographyProps: {\n                                                sx: {\n                                                    color: colors.textMuted,\n                                                    fontSize: \"0.75rem\",\n                                                    mt: 0.25\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 294,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 262,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 257,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.path, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 256,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        sx: {\n                            borderColor: colors.border,\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        disablePadding: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: isCollapsed ? \"Settings\" : \"\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: ()=>handleNavigation(\"/settings\"),\n                                sx: {\n                                    borderRadius: 2,\n                                    mx: 1,\n                                    px: 2,\n                                    py: 1.5,\n                                    minHeight: 48,\n                                    backgroundColor: pathname === \"/settings\" ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                        transform: \"translateX(2px)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\",\n                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        sx: {\n                                            color: pathname === \"/settings\" ? colors.accent : colors.textSecondary,\n                                            minWidth: isCollapsed ? \"auto\" : 40,\n                                            mr: isCollapsed ? 0 : 1.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            sx: {\n                                                fontSize: 22\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        primary: \"Settings\",\n                                        primaryTypographyProps: {\n                                            sx: {\n                                                color: pathname === \"/settings\" ? colors.text : colors.textSecondary,\n                                                fontWeight: pathname === \"/settings\" ? 600 : 500,\n                                                fontSize: \"0.875rem\"\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 3,\n                    borderTop: `1px solid ${colors.border}`,\n                    pt: 2\n                },\n                children: !isCollapsed ? // Expanded Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    sx: {\n                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                        borderRadius: 3,\n                        p: 2,\n                        mx: 1,\n                        border: `1px solid ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.border, 0.5)}`\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            mb: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    src: user?.imageUrl,\n                                    alt: user?.fullName,\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        mr: 2,\n                                        border: `2px solid ${colors.accent}`,\n                                        boxShadow: `0 0 0 2px ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)}`\n                                    },\n                                    children: user?.firstName?.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    sx: {\n                                        flex: 1,\n                                        minWidth: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: colors.text,\n                                                fontWeight: 600,\n                                                fontSize: \"0.875rem\",\n                                                lineHeight: 1.2,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            mt: 0.5,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: 14,\n                                                        color: colors.accent,\n                                                        mr: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    sx: {\n                                                        color: colors.textSecondary,\n                                                        fontSize: \"0.75rem\",\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"Administrator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: colors.textMuted,\n                                                fontSize: \"0.7rem\",\n                                                display: \"block\",\n                                                mt: 0.25,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: user?.primaryEmailAddress?.emailAddress\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            onClick: handleSignOut,\n                            sx: {\n                                width: \"100%\",\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                color: colors.error,\n                                borderRadius: 2,\n                                py: 1,\n                                \"&:hover\": {\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                    transform: \"translateY(-1px)\"\n                                },\n                                transition: \"all 0.2s ease-in-out\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18,\n                                        mr: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    variant: \"caption\",\n                                    sx: {\n                                        fontWeight: 600\n                                    },\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, undefined) : // Collapsed Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: `${user?.fullName} - Administrator`,\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                src: user?.imageUrl,\n                                alt: user?.fullName,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    border: `2px solid ${colors.accent}`,\n                                    boxShadow: `0 0 0 2px ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)}`,\n                                    cursor: \"pointer\"\n                                },\n                                children: user?.firstName?.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: \"Sign Out\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                onClick: handleSignOut,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                    color: colors.error,\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                        transform: \"scale(1.05)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 515,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 500,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 483,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ModernSidebar.js\n");

/***/ }),

/***/ "(ssr)/./src/services/ApiService.js":
/*!************************************!*\
  !*** ./src/services/ApiService.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/../../node_modules/axios/lib/axios.js\");\n// BahinLink Web Admin API Service\n// ⚠️ CRITICAL: Real production API integration with Clerk authentication ONLY\n\n// Real production API base URL\nconst API_BASE_URL =  true ? \"http://localhost:3001/api\" : 0;\nclass ApiService {\n    constructor(){\n        this.baseURL = API_BASE_URL;\n        this.setupInterceptors();\n    }\n    /**\n   * Setup axios interceptors for real authentication\n   */ setupInterceptors() {\n        // Request interceptor to add real Clerk token\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.request.use(async (config)=>{\n            try {\n                // For client-side requests, we'll handle auth in the component\n                // This is a simplified version for development\n                config.headers[\"Content-Type\"] = \"application/json\";\n                config.baseURL = this.baseURL;\n                console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);\n                return config;\n            } catch (error) {\n                console.error(\"Request interceptor error:\", error);\n                return config;\n            }\n        }, (error)=>{\n            console.error(\"Request interceptor error:\", error);\n            return Promise.reject(error);\n        });\n        // Response interceptor for error handling\n        axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].interceptors.response.use((response)=>{\n            console.log(`API Response: ${response.status} ${response.config.url}`);\n            return response.data;\n        }, (error)=>{\n            console.error(\"API Error:\", error.response?.data || error.message);\n            if (error.response?.status === 401) {\n                // Redirect to sign-in\n                window.location.href = \"/sign-in\";\n            }\n            return Promise.reject(error.response?.data || error);\n        });\n    }\n    /**\n   * GET request to real API\n   */ async get(endpoint, params = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(endpoint, {\n                params\n            });\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * POST request to real API\n   */ async post(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * PUT request to real API\n   */ async put(endpoint, data = {}) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(endpoint, data);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    /**\n   * DELETE request to real API\n   */ async delete(endpoint) {\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(endpoint);\n            return response;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Dashboard Analytics\n    async getDashboardAnalytics() {\n        return this.get(\"/analytics/dashboard\");\n    }\n    async getPerformanceMetrics(params = {}) {\n        return this.get(\"/analytics/performance\", params);\n    }\n    async getAgentLocations() {\n        return this.get(\"/analytics/locations\");\n    }\n    // User Management\n    async getUsers(params = {}) {\n        return this.get(\"/users\", params);\n    }\n    async getUser(userId) {\n        return this.get(`/users/${userId}`);\n    }\n    async updateUser(userId, data) {\n        return this.put(`/users/${userId}`, data);\n    }\n    // Agent Management\n    async getAgents(params = {}) {\n        return this.get(\"/agents\", params);\n    }\n    async getAgent(agentId) {\n        return this.get(`/agents/${agentId}`);\n    }\n    async updateAgent(agentId, data) {\n        return this.put(`/agents/${agentId}`, data);\n    }\n    async getNearbyAgents(latitude, longitude, radius) {\n        return this.get(\"/agents/nearby\", {\n            latitude,\n            longitude,\n            radius\n        });\n    }\n    // Site Management\n    async getSites(params = {}) {\n        return this.get(\"/sites\", params);\n    }\n    async createSite(data) {\n        return this.post(\"/sites\", data);\n    }\n    async getSite(siteId) {\n        return this.get(`/sites/${siteId}`);\n    }\n    async updateSite(siteId, data) {\n        return this.put(`/sites/${siteId}`, data);\n    }\n    async generateSiteQR(siteId) {\n        return this.post(`/sites/${siteId}/generate-qr`);\n    }\n    // Shift Management\n    async getShifts(params = {}) {\n        return this.get(\"/shifts\", params);\n    }\n    async createShift(data) {\n        return this.post(\"/shifts\", data);\n    }\n    async getShift(shiftId) {\n        return this.get(`/shifts/${shiftId}`);\n    }\n    async updateShift(shiftId, data) {\n        return this.put(`/shifts/${shiftId}`, data);\n    }\n    async assignShift(shiftId, agentId) {\n        return this.put(`/shifts/${shiftId}`, {\n            agentId\n        });\n    }\n    // Time Tracking\n    async getTimeEntries(params = {}) {\n        return this.get(\"/time/entries\", params);\n    }\n    async verifyTimeEntry(timeEntryId, data) {\n        return this.put(`/time/entries/${timeEntryId}/verify`, data);\n    }\n    // Reports Management\n    async getReports(params = {}) {\n        return this.get(\"/reports\", params);\n    }\n    async getReport(reportId) {\n        return this.get(`/reports/${reportId}`);\n    }\n    async approveReport(reportId, data = {}) {\n        return this.post(`/reports/${reportId}/approve`, data);\n    }\n    async rejectReport(reportId, data) {\n        return this.post(`/reports/${reportId}/reject`, data);\n    }\n    // Notifications\n    async getNotifications(params = {}) {\n        return this.get(\"/notifications\", params);\n    }\n    async sendNotification(data) {\n        return this.post(\"/notifications\", data);\n    }\n    async broadcastNotification(data) {\n        return this.post(\"/notifications/broadcast\", data);\n    }\n    // Communications\n    async getMessages(params = {}) {\n        return this.get(\"/communications\", params);\n    }\n    async sendMessage(data) {\n        return this.post(\"/communications\", data);\n    }\n    async getMessageThreads(params = {}) {\n        return this.get(\"/communications/threads\", params);\n    }\n    // Client Management\n    async getClients(params = {}) {\n        return this.get(\"/clients\", params);\n    }\n    async getClientRequests(params = {}) {\n        return this.get(\"/client/requests\", params);\n    }\n    async updateClientRequest(requestId, data) {\n        return this.put(`/client/requests/${requestId}`, data);\n    }\n    // Geofencing\n    async checkGeofence(data) {\n        return this.post(\"/geofence/check\", data);\n    }\n    async getGeofenceViolations(params = {}) {\n        return this.get(\"/geofence/violations\", params);\n    }\n    async resolveGeofenceViolation(violationId, data) {\n        return this.put(`/geofence/violations/${violationId}/resolve`, data);\n    }\n    // File Management\n    async uploadFile(file, type = \"document\") {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const endpoint = `/upload/${type}`;\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(endpoint, formData, {\n                headers: {\n                    \"Content-Type\": \"multipart/form-data\"\n                },\n                baseURL: this.baseURL\n            });\n            return response.data;\n        } catch (error) {\n            throw error;\n        }\n    }\n    // Bulk Operations\n    async bulkUpdateShifts(shiftIds, data) {\n        return this.put(\"/shifts/bulk\", {\n            shiftIds,\n            ...data\n        });\n    }\n    async bulkNotifyAgents(agentIds, notification) {\n        return this.post(\"/notifications/bulk\", {\n            agentIds,\n            ...notification\n        });\n    }\n    // Export Functions\n    async exportReports(params = {}) {\n        return this.get(\"/reports/export\", params);\n    }\n    async exportTimeEntries(params = {}) {\n        return this.get(\"/time/entries/export\", params);\n    }\n    async exportAgentPerformance(params = {}) {\n        return this.get(\"/analytics/performance/export\", params);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (new ApiService());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/ApiService.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8d498114e33b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRjMjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZDQ5ODExNGUzM2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/analytics/page.js":
/*!***********************************!*\
  !*** ./src/app/analytics/page.js ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/analytics/page.js\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"(rsc)/../../node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n// BahinLink Web Admin Layout\n// ⚠️ CRITICAL: Real Clerk authentication and production data integration ONLY\n\n\n\n\n// Real Clerk publishable key - MUST be production key\nconst CLERK_PUBLISHABLE_KEY = \"pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk\";\nconst metadata = {\n    title: \"BahinLink Admin - Security Workforce Management\",\n    description: \"Real-time security workforce management for Bahin SARL\",\n    keywords: \"security, workforce, management, GPS tracking, Senegal, Bahin SARL\",\n    authors: [\n        {\n            name: \"Bahin SARL\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.ClerkProvider, {\n        publishableKey: CLERK_PUBLISHABLE_KEY,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/node-fetch-native","vendor-chunks/@clerk","vendor-chunks/@peculiar","vendor-chunks/asn1js","vendor-chunks/@opentelemetry","vendor-chunks/webcrypto-core","vendor-chunks/swr","vendor-chunks/pvtsutils","vendor-chunks/tslib","vendor-chunks/pvutils","vendor-chunks/cookie","vendor-chunks/deepmerge","vendor-chunks/use-sync-external-store","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/react-is","vendor-chunks/hoist-non-react-statics","vendor-chunks/@babel","vendor-chunks/object-assign","vendor-chunks/clsx","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@popperjs","vendor-chunks/react-transition-group","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/recharts","vendor-chunks/lodash","vendor-chunks/d3-shape","vendor-chunks/react-smooth","vendor-chunks/decimal.js-light","vendor-chunks/d3-scale","vendor-chunks/fast-equals","vendor-chunks/d3-time-format","vendor-chunks/recharts-scale","vendor-chunks/d3-time","vendor-chunks/d3-format","vendor-chunks/d3-array","vendor-chunks/d3-color","vendor-chunks/eventemitter3","vendor-chunks/d3-interpolate","vendor-chunks/d3-path","vendor-chunks/internmap","vendor-chunks/victory-vendor","vendor-chunks/tiny-invariant"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fanalytics%2Fpage&page=%2Fanalytics%2Fpage&appPaths=%2Fanalytics%2Fpage&pagePath=private-next-app-dir%2Fanalytics%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();