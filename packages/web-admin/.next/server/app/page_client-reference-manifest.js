globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./src/app/page.js":{"*":{"id":"(ssr)/./src/app/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"*":{"id":"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"*":{"id":"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"*":{"id":"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"*":{"id":"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/sign-in/page.js":{"*":{"id":"(ssr)/./src/app/sign-in/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/sites/page.js":{"*":{"id":"(ssr)/./src/app/sites/page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/analytics/page.js":{"*":{"id":"(ssr)/./src/app/analytics/page.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/workspaces/finalagent-main-final/packages/web-admin/src/app/page.js":{"id":"(app-pages-browser)/./src/app/page.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js":{"id":"(app-pages-browser)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js":{"id":"(app-pages-browser)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js":{"id":"(app-pages-browser)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js":{"id":"(app-pages-browser)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/../../node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/workspaces/finalagent-main-final/packages/web-admin/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/workspaces/finalagent-main-final/packages/web-admin/src/app/sign-in/page.js":{"id":"(app-pages-browser)/./src/app/sign-in/page.js","name":"*","chunks":[],"async":false},"/workspaces/finalagent-main-final/packages/web-admin/src/app/sites/page.js":{"id":"(app-pages-browser)/./src/app/sites/page.js","name":"*","chunks":[],"async":false},"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js":{"id":"(app-pages-browser)/./src/app/analytics/page.js","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"/workspaces/finalagent-main-final/packages/web-admin/src/":[],"/workspaces/finalagent-main-final/packages/web-admin/src/app/page":[],"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout":["static/css/app/layout.css"]}}