"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sites/route";
exports.ids = ["app/api/sites/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsites%2Froute&page=%2Fapi%2Fsites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsites%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsites%2Froute&page=%2Fapi%2Fsites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsites%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _workspaces_finalagent_main_final_packages_web_admin_src_app_api_sites_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/sites/route.js */ \"(rsc)/./src/app/api/sites/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sites/route\",\n        pathname: \"/api/sites\",\n        filename: \"route\",\n        bundlePath: \"app/api/sites/route\"\n    },\n    resolvedPagePath: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/api/sites/route.js\",\n    nextConfigOutput,\n    userland: _workspaces_finalagent_main_final_packages_web_admin_src_app_api_sites_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/sites/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsites%2Froute&page=%2Fapi%2Fsites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsites%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/sites/route.js":
/*!************************************!*\
  !*** ./src/app/api/sites/route.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Global Prisma instance for serverless optimization\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient({\n    datasources: {\n        db: {\n            url: process.env.DATABASE_URL\n        }\n    },\n    log:  true ? [\n        \"error\"\n    ] : 0\n});\n// Prevent multiple instances in development\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const type = searchParams.get(\"type\");\n        const status = searchParams.get(\"status\");\n        const search = searchParams.get(\"search\");\n        let sitesData;\n        try {\n            // Build where clause for filtering\n            const where = {\n                ...type && {\n                    type\n                },\n                ...status && {\n                    isActive: status === \"ACTIVE\"\n                },\n                ...search && {\n                    OR: [\n                        {\n                            name: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        },\n                        {\n                            address: {\n                                contains: search,\n                                mode: \"insensitive\"\n                            }\n                        }\n                    ]\n                }\n            };\n            // Fetch sites with related data\n            const sites = await prisma.site.findMany({\n                where,\n                include: {\n                    shifts: {\n                        where: {\n                            createdAt: {\n                                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                            }\n                        },\n                        orderBy: {\n                            createdAt: \"desc\"\n                        },\n                        take: 5\n                    },\n                    reports: {\n                        where: {\n                            createdAt: {\n                                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days\n                            }\n                        },\n                        orderBy: {\n                            createdAt: \"desc\"\n                        },\n                        take: 5\n                    }\n                },\n                orderBy: {\n                    createdAt: \"desc\"\n                }\n            });\n            // Transform data for frontend\n            const transformedSites = sites.map((site)=>{\n                const shifts = site.shifts || [];\n                const reports = site.reports || [];\n                // Calculate active shifts\n                const activeShifts = shifts.filter((s)=>s.status === \"IN_PROGRESS\").length;\n                // Calculate total shifts this month\n                const totalShifts = shifts.length;\n                // Calculate incident count\n                const incidents = reports.filter((r)=>r.type === \"INCIDENT\").length;\n                return {\n                    id: site.id,\n                    name: site.name,\n                    type: site.type,\n                    address: site.address,\n                    coordinates: site.coordinates,\n                    isActive: site.isActive,\n                    contactPerson: site.contactPerson,\n                    contactPhone: site.contactPhone,\n                    contactEmail: site.contactEmail,\n                    securityLevel: site.securityLevel,\n                    accessInstructions: site.accessInstructions,\n                    emergencyProcedures: site.emergencyProcedures,\n                    geofenceRadius: site.geofenceRadius,\n                    qrCode: site.qrCode,\n                    stats: {\n                        activeShifts,\n                        totalShifts,\n                        incidents,\n                        lastShift: shifts.length > 0 ? shifts[0].createdAt : null\n                    },\n                    recentShifts: shifts.slice(0, 3),\n                    recentReports: reports.slice(0, 3),\n                    createdAt: site.createdAt,\n                    updatedAt: site.updatedAt\n                };\n            });\n            sitesData = {\n                sites: transformedSites,\n                total: transformedSites.length,\n                lastUpdated: new Date().toISOString()\n            };\n        } catch (dbError) {\n            console.error(\"Database query error:\", dbError);\n            // Return sample data for demonstration when database is not available\n            sitesData = {\n                sites: [\n                    {\n                        id: \"1\",\n                        name: \"Sonatel HQ\",\n                        type: \"COMMERCIAL\",\n                        address: \"Plateau, Dakar, Senegal\",\n                        coordinates: {\n                            latitude: 14.6937,\n                            longitude: -17.4441\n                        },\n                        isActive: true,\n                        contactPerson: \"Mamadou Diallo\",\n                        contactPhone: \"+221 33 839 9000\",\n                        contactEmail: \"<EMAIL>\",\n                        securityLevel: \"HIGH\",\n                        accessInstructions: \"Badge required for entry. Report to security desk.\",\n                        emergencyProcedures: \"Contact security immediately at ext. 911\",\n                        geofenceRadius: 100,\n                        qrCode: \"QR_SONATEL_001\",\n                        stats: {\n                            activeShifts: 2,\n                            totalShifts: 45,\n                            incidents: 1,\n                            lastShift: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()\n                        },\n                        recentShifts: [],\n                        recentReports: [],\n                        createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date().toISOString()\n                    },\n                    {\n                        id: \"2\",\n                        name: \"CBAO Bank Dakar\",\n                        type: \"FINANCIAL\",\n                        address: \"Avenue L\\xe9opold S\\xe9dar Senghor, Dakar\",\n                        coordinates: {\n                            latitude: 14.7167,\n                            longitude: -17.4677\n                        },\n                        isActive: true,\n                        contactPerson: \"Fatou Ndiaye\",\n                        contactPhone: \"+221 33 849 5000\",\n                        contactEmail: \"<EMAIL>\",\n                        securityLevel: \"CRITICAL\",\n                        accessInstructions: \"Biometric access required. Escort mandatory.\",\n                        emergencyProcedures: \"Silent alarm protocol. Contact police immediately.\",\n                        geofenceRadius: 50,\n                        qrCode: \"QR_CBAO_002\",\n                        stats: {\n                            activeShifts: 1,\n                            totalShifts: 38,\n                            incidents: 0,\n                            lastShift: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()\n                        },\n                        recentShifts: [],\n                        recentReports: [],\n                        createdAt: new Date(Date.now() - 150 * 24 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date().toISOString()\n                    },\n                    {\n                        id: \"3\",\n                        name: \"Orange Senegal\",\n                        type: \"COMMERCIAL\",\n                        address: \"Rue de Thiong, Dakar\",\n                        coordinates: {\n                            latitude: 14.6928,\n                            longitude: -17.4467\n                        },\n                        isActive: true,\n                        contactPerson: \"Ousmane Ba\",\n                        contactPhone: \"+221 33 859 5000\",\n                        contactEmail: \"<EMAIL>\",\n                        securityLevel: \"MEDIUM\",\n                        accessInstructions: \"Visitor badge required. Check in at reception.\",\n                        emergencyProcedures: \"Evacuate to assembly point. Contact emergency services.\",\n                        geofenceRadius: 75,\n                        qrCode: \"QR_ORANGE_003\",\n                        stats: {\n                            activeShifts: 0,\n                            totalShifts: 32,\n                            incidents: 2,\n                            lastShift: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()\n                        },\n                        recentShifts: [],\n                        recentReports: [],\n                        createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date().toISOString()\n                    },\n                    {\n                        id: \"4\",\n                        name: \"Ecobank Senegal\",\n                        type: \"FINANCIAL\",\n                        address: \"Place de l'Ind\\xe9pendance, Dakar\",\n                        coordinates: {\n                            latitude: 14.6892,\n                            longitude: -17.4419\n                        },\n                        isActive: true,\n                        contactPerson: \"Aissatou Sow\",\n                        contactPhone: \"+221 33 869 7000\",\n                        contactEmail: \"<EMAIL>\",\n                        securityLevel: \"CRITICAL\",\n                        accessInstructions: \"Dual authentication required. Metal detector screening.\",\n                        emergencyProcedures: \"Lockdown protocol. Notify central security.\",\n                        geofenceRadius: 60,\n                        qrCode: \"QR_ECOBANK_004\",\n                        stats: {\n                            activeShifts: 1,\n                            totalShifts: 28,\n                            incidents: 0,\n                            lastShift: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()\n                        },\n                        recentShifts: [],\n                        recentReports: [],\n                        createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),\n                        updatedAt: new Date().toISOString()\n                    }\n                ],\n                total: 4,\n                lastUpdated: new Date().toISOString(),\n                note: \"Sample data - Database connection pending\"\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(sitesData);\n    } catch (error) {\n        console.error(\"Sites API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch sites data\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/sites/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsites%2Froute&page=%2Fapi%2Fsites%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsites%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();