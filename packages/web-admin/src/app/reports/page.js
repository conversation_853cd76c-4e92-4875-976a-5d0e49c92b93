// BahinLink Web Admin Reports Management Page
// ⚠️ CRITICAL: Real report management with approval workflow ONLY

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Chip,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Assignment as AssignmentIcon,
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Warning as WarningIcon,
  LocationOn,
  Photo,
  Star
} from '@mui/icons-material';
import ModernSidebar from '../../components/ModernSidebar';

const ReportsPage = () => {
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    priority: '',
    agentId: '',
    siteId: '',
    startDate: null,
    endDate: null
  });
  const [agents, setAgents] = useState([]);
  const [sites, setSites] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    overdue: 0
  });
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [reviewAction, setReviewAction] = useState('');
  const [reviewData, setReviewData] = useState({
    notes: '',
    qualityScore: 5
  });

  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/reports');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setReports(data.reports || []);

      // Calculate stats
      const reportsData = data.reports || [];
      const now = new Date();
      const newStats = {
        total: reportsData.length,
        pending: reportsData.filter(r => r.status === 'SUBMITTED').length,
        approved: reportsData.filter(r => r.status === 'APPROVED').length,
        rejected: reportsData.filter(r => r.status === 'REJECTED').length,
        overdue: reportsData.filter(r => {
          if (r.status !== 'SUBMITTED') return false;
          const submittedAt = new Date(r.createdAt);
          const hoursSinceSubmission = (now - submittedAt) / (1000 * 60 * 60);
          return hoursSinceSubmission > 24;
        }).length
      };
      setStats(newStats);

    } catch (error) {
      console.error('Error loading reports data:', error);
      setError('Failed to load reports data');
      // Set fallback data
      setReports([]);
      setStats({
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        overdue: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (reportId, reviewData) => {
    try {
      const response = await ApiService.put(`/reports/${reportId}`, {
        status: 'APPROVED',
        reviewNotes: reviewData.notes,
        qualityScore: reviewData.qualityScore,
        reviewedAt: new Date().toISOString()
      });
      
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error approving report:', error);
      setError('Failed to approve report');
    }
  };

  const handleReject = async (reportId, reviewData) => {
    try {
      const response = await ApiService.put(`/reports/${reportId}`, {
        status: 'REJECTED',
        reviewNotes: reviewData.notes,
        qualityScore: reviewData.qualityScore,
        reviewedAt: new Date().toISOString()
      });
      
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error rejecting report:', error);
      setError('Failed to reject report');
    }
  };

  const handleEdit = async (reportId) => {
    // Navigate to edit page or open edit dialog
    console.log('Edit report:', reportId);
  };

  const handleDelete = async (reportId) => {
    try {
      const response = await ApiService.delete(`/reports/${reportId}`);
      if (response.success) {
        loadData();
      }
    } catch (error) {
      console.error('Error deleting report:', error);
      setError('Failed to delete report');
    }
  };

  const handleView = (reportId) => {
    // Navigate to report details page
    console.log('View report:', reportId);
  };

  const handleExport = async () => {
    try {
      // In a real app, this would generate and download a CSV/Excel file
      console.log('Exporting reports data...');
      alert('Export functionality would be implemented here');
    } catch (error) {
      console.error('Error exporting data:', error);
    }
  };

  const StatCard = ({ title, value, icon: Icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" component="div" color={`${color}.main`}>
              {value}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {title}
            </Typography>
          </Box>
          <Icon sx={{ fontSize: 40, color: `${color}.main`, opacity: 0.7 }} />
        </Box>
      </CardContent>
    </Card>
  );

  const handleExport = () => {
    if (reports.length > 0) {
      const dataStr = JSON.stringify(reports, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

      const exportFileDefaultName = `reports-${new Date().toISOString().split('T')[0]}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    }
  };

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <CircularProgress size={40} sx={{ color: '#6b7280' }} />
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#ffffff' }}>
      <ModernSidebar />

      <Box component="main" sx={{ flexGrow: 1, p: 4 }}>
        {/* Clean Header */}
        <Box sx={{ mb: 6, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 300,
                color: '#1a1a1a',
                mb: 1,
                letterSpacing: '-0.02em',
                fontSize: '2.5rem'
              }}
            >
              Reports
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#6b7280',
                fontSize: '16px'
              }}
            >
              Review and manage security reports
            </Typography>
          </Box>

          {/* Clean Controls */}
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExport}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Export
            </Button>

            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadData}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Refresh
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Minimalist KPI Grid */}
        <Box sx={{ mb: 8 }}>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: 4,
              mb: 6
            }}
          >
            {/* Total Reports */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.total}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Total Reports
              </Typography>
            </Box>

            {/* Pending */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#f59e0b',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.pending}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Pending Review
              </Typography>
            </Box>

            {/* Approved */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#10b981',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.approved}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Approved
              </Typography>
            </Box>

            {/* Rejected */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#ef4444',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.rejected}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Rejected
              </Typography>
            </Box>

            {/* Overdue */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#dc2626',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.overdue}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Overdue
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Clean Reports Table */}
        <Box sx={{
          borderRadius: 3,
          border: '1px solid #f1f5f9',
          backgroundColor: '#ffffff',
          overflow: 'hidden'
        }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Report
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Agent & Site
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Type & Priority
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Status
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Created
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {reports.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography variant="body2" color="textSecondary">
                        No reports found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  reports.map((report) => (
                    <TableRow
                      key={report.id}
                      sx={{
                        '&:hover': { backgroundColor: '#f9fafb' },
                        borderBottom: '1px solid #f1f5f9'
                      }}
                    >
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500, color: '#1a1a1a', mb: 0.5 }}>
                            {report.title}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#6b7280' }}>
                            {report.description?.substring(0, 60)}...
                          </Typography>
                          {report.photos && report.photos.length > 0 && (
                            <Box sx={{ mt: 0.5 }}>
                              <Photo sx={{ fontSize: 14, color: '#6b7280', mr: 0.5 }} />
                              <Typography variant="caption" sx={{ color: '#6b7280' }}>
                                {report.photos.length} photo(s)
                              </Typography>
                            </Box>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                            {report.agentName}
                          </Typography>
                          <Typography variant="caption" sx={{ color: '#6b7280' }}>
                            <LocationOn sx={{ fontSize: 14, mr: 0.5, verticalAlign: 'middle' }} />
                            {report.siteName}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Chip
                            label={report.type}
                            size="small"
                            sx={{
                              backgroundColor: '#f3f4f6',
                              color: '#374151',
                              fontSize: '12px',
                              mb: 0.5
                            }}
                          />
                          <br />
                          <Chip
                            label={report.priority}
                            size="small"
                            sx={{
                              backgroundColor:
                                report.priority === 'CRITICAL' ? '#ef4444' :
                                report.priority === 'HIGH' ? '#f59e0b' :
                                report.priority === 'MEDIUM' ? '#3b82f6' : '#10b981',
                              color: 'white',
                              fontSize: '12px'
                            }}
                          />
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Chip
                          label={report.status.replace('_', ' ')}
                          size="small"
                          sx={{
                            backgroundColor:
                              report.status === 'APPROVED' ? '#10b981' :
                              report.status === 'REJECTED' ? '#ef4444' :
                              report.status === 'SUBMITTED' ? '#f59e0b' : '#6b7280',
                            color: 'white',
                            fontSize: '12px'
                          }}
                        />
                        {report.rating && (
                          <Box sx={{ mt: 0.5, display: 'flex', alignItems: 'center' }}>
                            <Star sx={{ fontSize: 14, color: '#fbbf24', mr: 0.5 }} />
                            <Typography variant="caption" sx={{ color: '#6b7280' }}>
                              {report.rating}/5
                            </Typography>
                          </Box>
                        )}
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                          {new Date(report.createdAt).toLocaleDateString()}
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#6b7280' }}>
                          {new Date(report.createdAt).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          {report.status === 'SUBMITTED' && (
                            <>
                              <IconButton size="small" sx={{ color: '#10b981' }}>
                                <ApproveIcon fontSize="small" />
                              </IconButton>
                              <IconButton size="small" sx={{ color: '#ef4444' }}>
                                <RejectIcon fontSize="small" />
                              </IconButton>
                            </>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>
    </Box>
  );
};

export default ReportsPage;




              value={stats.overdue}
              icon={WarningIcon}
              color="error"
            />
          </Grid>
        </Grid>

        {/* Filters */}
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={filters.status}
                  label="Status"
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="DRAFT">Draft</MenuItem>
                  <MenuItem value="SUBMITTED">Submitted</MenuItem>
                  <MenuItem value="APPROVED">Approved</MenuItem>
                  <MenuItem value="REJECTED">Rejected</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Type</InputLabel>
                <Select
                  value={filters.type}
                  label="Type"
                  onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="INCIDENT">Incident</MenuItem>
                  <MenuItem value="MAINTENANCE">Maintenance</MenuItem>
                  <MenuItem value="SECURITY">Security</MenuItem>
                  <MenuItem value="SAFETY">Safety</MenuItem>
                  <MenuItem value="GENERAL">General</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Priority</InputLabel>
                <Select
                  value={filters.priority}
                  label="Priority"
                  onChange={(e) => setFilters(prev => ({ ...prev, priority: e.target.value }))}
                >
                  <MenuItem value="">All Priorities</MenuItem>
                  <MenuItem value="LOW">Low</MenuItem>
                  <MenuItem value="MEDIUM">Medium</MenuItem>
                  <MenuItem value="HIGH">High</MenuItem>
                  <MenuItem value="URGENT">Urgent</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Agent</InputLabel>
                <Select
                  value={filters.agentId}
                  label="Agent"
                  onChange={(e) => setFilters(prev => ({ ...prev, agentId: e.target.value }))}
                >
                  <MenuItem value="">All Agents</MenuItem>
                  {agents.map((agent) => (
                    <MenuItem key={agent.id} value={agent.id}>
                      {agent.user ? `${agent.user.firstName} ${agent.user.lastName}` : 'Unknown'}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <DatePicker
                label="Start Date"
                value={filters.startDate}
                onChange={(date) => setFilters(prev => ({ ...prev, startDate: date }))}
                slotProps={{ textField: { size: 'small', fullWidth: true } }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => setFilters({
                  status: '',
                  type: '',
                  priority: '',
                  agentId: '',
                  siteId: '',
                  startDate: null,
                  endDate: null
                })}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Reports Table */}
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <ReportTable
            reports={reports}
            loading={loading}
            onView={handleView}
            onApprove={handleApprove}
            onReject={handleReject}
            onEdit={handleEdit}
            onDelete={handleDelete}
          />
        )}
          </Box>
        </LocalizationProvider>
      </Box>
    </Box>
  );
};

export default ReportsPage;
