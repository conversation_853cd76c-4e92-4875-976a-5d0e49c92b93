{"c": ["app/shifts/page", "webpack"], "r": [], "m": ["(app-pages-browser)/../../node_modules/@mui/icons-material/esm/PlayArrow.js", "(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Stop.js", "(app-pages-browser)/../../node_modules/date-fns/isToday.mjs", "(app-pages-browser)/../../node_modules/date-fns/isTomorrow.mjs", "(app-pages-browser)/../../node_modules/date-fns/isYesterday.mjs", "(app-pages-browser)/../../node_modules/date-fns/subDays.mjs", "(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fshifts%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=false!", "(app-pages-browser)/./src/app/shifts/page.js", "(app-pages-browser)/./src/components/ShiftTable.js"]}