// BahinLink Site Map Component
// ⚠️ CRITICAL: Real-time site visualization with production data ONLY

'use client';

import { useEffect, useRef, useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Chip,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  LocationOn,
  Security,
  Business,
  Warning,
  CheckCircle,
  Error,
  Info
} from '@mui/icons-material';

const SiteMap = ({ sites = [] }) => {
  const mapRef = useRef(null);
  const [map, setMap] = useState(null);
  const [markers, setMarkers] = useState([]);
  const [mapError, setMapError] = useState(null);

  useEffect(() => {
    // Initialize map when component mounts
    initializeMap();
    
    return () => {
      // Cleanup markers when component unmounts
      markers.forEach(marker => {
        if (marker.remove) marker.remove();
      });
    };
  }, []);

  useEffect(() => {
    // Update markers when sites change
    if (map && sites.length > 0) {
      updateMarkers();
    }
  }, [map, sites]);

  const initializeMap = async () => {
    try {
      // Check if we're in browser environment
      if (typeof window === 'undefined') return;

      // Dynamic import of Leaflet to avoid SSR issues
      const L = await import('leaflet');
      
      // Fix for default markers in Leaflet
      delete L.Icon.Default.prototype._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
        iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
      });

      // Initialize map centered on Dakar, Senegal
      const mapInstance = L.map(mapRef.current).setView([14.6928, -17.4467], 11);

      // Add OpenStreetMap tiles
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
      }).addTo(mapInstance);

      setMap(mapInstance);
    } catch (error) {
      console.error('Error initializing map:', error);
      setMapError('Failed to load map. Please check your internet connection.');
    }
  };

  const updateMarkers = async () => {
    if (!map) return;

    try {
      const L = await import('leaflet');
      
      // Clear existing markers
      markers.forEach(marker => {
        if (marker.remove) marker.remove();
      });

      const newMarkers = [];

      // Add markers for each site
      sites.forEach(site => {
        if (site.latitude && site.longitude) {
          const marker = L.marker([site.latitude, site.longitude])
            .addTo(map)
            .bindPopup(createPopupContent(site));
          
          newMarkers.push(marker);
        }
      });

      setMarkers(newMarkers);

      // Fit map to show all markers if there are any
      if (newMarkers.length > 0) {
        const group = new L.featureGroup(newMarkers);
        map.fitBounds(group.getBounds().pad(0.1));
      }
    } catch (error) {
      console.error('Error updating markers:', error);
    }
  };

  const createPopupContent = (site) => {
    const statusColor = getStatusColor(site.status);
    const typeIcon = getTypeIcon(site.type);
    
    return `
      <div style="min-width: 200px;">
        <div style="display: flex; align-items: center; margin-bottom: 8px;">
          <strong style="font-size: 16px;">${site.name}</strong>
        </div>
        <div style="margin-bottom: 4px;">
          <span style="color: #666;">Type:</span> ${typeIcon} ${site.type || 'Unknown'}
        </div>
        <div style="margin-bottom: 4px;">
          <span style="color: #666;">Status:</span> 
          <span style="color: ${statusColor}; font-weight: bold;">${site.status || 'Unknown'}</span>
        </div>
        <div style="margin-bottom: 4px;">
          <span style="color: #666;">Address:</span> ${site.address || 'No address'}
        </div>
        ${site.client ? `
          <div style="margin-bottom: 4px;">
            <span style="color: #666;">Client:</span> ${site.client.name}
          </div>
        ` : ''}
        ${site.activeAgents ? `
          <div style="margin-bottom: 4px;">
            <span style="color: #666;">Active Agents:</span> ${site.activeAgents}
          </div>
        ` : ''}
      </div>
    `;
  };

  const getStatusColor = (status) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE': return '#10b981';
      case 'INACTIVE': return '#ef4444';
      case 'MAINTENANCE': return '#f59e0b';
      default: return '#6b7280';
    }
  };

  const getTypeIcon = (type) => {
    switch (type?.toUpperCase()) {
      case 'COMMERCIAL': return '🏢';
      case 'RESIDENTIAL': return '🏠';
      case 'INDUSTRIAL': return '🏭';
      case 'GOVERNMENT': return '🏛️';
      default: return '📍';
    }
  };

  const getStatusIcon = (status) => {
    switch (status?.toUpperCase()) {
      case 'ACTIVE': return <CheckCircle sx={{ color: '#10b981', fontSize: 16 }} />;
      case 'INACTIVE': return <Error sx={{ color: '#ef4444', fontSize: 16 }} />;
      case 'MAINTENANCE': return <Warning sx={{ color: '#f59e0b', fontSize: 16 }} />;
      default: return <Info sx={{ color: '#6b7280', fontSize: 16 }} />;
    }
  };

  if (mapError) {
    return (
      <Alert severity="error" sx={{ height: '100%', display: 'flex', alignItems: 'center' }}>
        {mapError}
      </Alert>
    );
  }

  return (
    <Box sx={{ height: '100%', position: 'relative' }}>
      {/* Map Container */}
      <Box
        ref={mapRef}
        sx={{
          height: '100%',
          width: '100%',
          borderRadius: 2,
          overflow: 'hidden',
          '& .leaflet-container': {
            height: '100%',
            width: '100%'
          }
        }}
      />

      {/* Map Info Overlay */}
      <Box
        sx={{
          position: 'absolute',
          top: 16,
          right: 16,
          zIndex: 1000
        }}
      >
        <Card sx={{ minWidth: 200 }}>
          <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Sites Overview
            </Typography>
            
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <LocationOn sx={{ fontSize: 16 }} />
                  Total Sites
                </Typography>
                <Chip label={sites.length} size="small" color="primary" />
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {getStatusIcon('ACTIVE')}
                  Active
                </Typography>
                <Chip 
                  label={sites.filter(s => s.status === 'ACTIVE').length} 
                  size="small" 
                  sx={{ backgroundColor: '#dcfce7', color: '#166534' }}
                />
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  {getStatusIcon('INACTIVE')}
                  Inactive
                </Typography>
                <Chip 
                  label={sites.filter(s => s.status === 'INACTIVE').length} 
                  size="small" 
                  sx={{ backgroundColor: '#fee2e2', color: '#991b1b' }}
                />
              </Box>
            </Box>
          </CardContent>
        </Card>
      </Box>

      {/* Loading indicator */}
      {!map && !mapError && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 1000
          }}
        >
          <Typography variant="body2" color="text.secondary">
            Loading map...
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default SiteMap;
