"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.js":
/*!***********************************!*\
  !*** ./src/app/analytics/page.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Box,Button,Card,CardContent,CircularProgress,FormControl,Grid,InputLabel,MenuItem,Paper,Select,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Download,People,Refresh,Schedule,Security,TrendingUp!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/TrendingUp.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Download,People,Refresh,Schedule,Security,TrendingUp!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Download.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Download,People,Refresh,Schedule,Security,TrendingUp!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Download,People,Refresh,Schedule,Security,TrendingUp!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Download,People,Refresh,Schedule,Security,TrendingUp!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Download,People,Refresh,Schedule,Security,TrendingUp!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assignment,Download,People,Refresh,Schedule,Security,TrendingUp!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Security.js\");\n/* harmony import */ var _components_AnalyticsCharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/AnalyticsCharts */ \"(app-pages-browser)/./src/components/AnalyticsCharts.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../services/ApiService */ \"(app-pages-browser)/./src/services/ApiService.js\");\n// BahinLink Web Admin Analytics Dashboard Page\n// ⚠️ CRITICAL: Real analytics with live data visualization ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst AnalyticsPage = ()=>{\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"7d\");\n    const [kpis, setKpis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalShifts: 0,\n        completedShifts: 0,\n        totalHours: 0,\n        averageRating: 0,\n        activeAgents: 0,\n        totalReports: 0,\n        responseTime: 0,\n        efficiency: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAnalyticsData();\n    }, [\n        timeRange\n    ]);\n    const loadAnalyticsData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const [shiftsResponse, reportsResponse, agentsResponse, kpiResponse] = await Promise.all([\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/analytics/shifts\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/analytics/reports\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/analytics/agents\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"/analytics/kpis\", {\n                    timeRange\n                })\n            ]);\n            // Combine all analytics data\n            const combinedData = {\n                overview: kpiResponse.success ? kpiResponse.data : {},\n                shiftTrends: shiftsResponse.success ? shiftsResponse.data.trends : [],\n                reportTypes: reportsResponse.success ? reportsResponse.data.types : [],\n                agentPerformance: agentsResponse.success ? agentsResponse.data.performance : [],\n                siteActivity: shiftsResponse.success ? shiftsResponse.data.siteActivity : []\n            };\n            setAnalyticsData(combinedData);\n            // Set KPIs\n            if (kpiResponse.success) {\n                setKpis(kpiResponse.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading analytics data:\", error);\n            setError(\"Failed to load analytics data\");\n            // Fallback to sample data for demo\n            setAnalyticsData({\n                overview: {\n                    totalShifts: 156,\n                    activeShifts: 12,\n                    totalAgents: 24,\n                    activeAgents: 18,\n                    totalSites: 8,\n                    activeSites: 6,\n                    totalReports: 89,\n                    pendingReports: 5,\n                    shiftsChange: 12.5,\n                    agentsChange: -2.1,\n                    reportsChange: 8.3\n                },\n                shiftTrends: [\n                    {\n                        date: \"2024-01-01\",\n                        scheduled: 20,\n                        completed: 18,\n                        cancelled: 2\n                    },\n                    {\n                        date: \"2024-01-02\",\n                        scheduled: 22,\n                        completed: 20,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-03\",\n                        scheduled: 18,\n                        completed: 17,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-04\",\n                        scheduled: 25,\n                        completed: 23,\n                        cancelled: 2\n                    },\n                    {\n                        date: \"2024-01-05\",\n                        scheduled: 21,\n                        completed: 19,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-06\",\n                        scheduled: 19,\n                        completed: 18,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-07\",\n                        scheduled: 23,\n                        completed: 21,\n                        cancelled: 2\n                    }\n                ],\n                reportTypes: [\n                    {\n                        name: \"Security\",\n                        value: 35,\n                        color: \"#DC3545\"\n                    },\n                    {\n                        name: \"Maintenance\",\n                        value: 25,\n                        color: \"#FFC107\"\n                    },\n                    {\n                        name: \"Incident\",\n                        value: 20,\n                        color: \"#DC004E\"\n                    },\n                    {\n                        name: \"General\",\n                        value: 15,\n                        color: \"#1976D2\"\n                    },\n                    {\n                        name: \"Safety\",\n                        value: 5,\n                        color: \"#2E7D32\"\n                    }\n                ],\n                agentPerformance: [\n                    {\n                        name: \"John Doe\",\n                        shifts: 15,\n                        onTime: 14,\n                        reports: 8,\n                        rating: 4.8\n                    },\n                    {\n                        name: \"Jane Smith\",\n                        shifts: 12,\n                        onTime: 12,\n                        reports: 6,\n                        rating: 4.9\n                    },\n                    {\n                        name: \"Mike Johnson\",\n                        shifts: 18,\n                        onTime: 16,\n                        reports: 12,\n                        rating: 4.6\n                    },\n                    {\n                        name: \"Sarah Wilson\",\n                        shifts: 14,\n                        onTime: 13,\n                        reports: 9,\n                        rating: 4.7\n                    },\n                    {\n                        name: \"David Brown\",\n                        shifts: 16,\n                        onTime: 15,\n                        reports: 7,\n                        rating: 4.5\n                    }\n                ],\n                siteActivity: [\n                    {\n                        site: \"Downtown Mall\",\n                        shifts: 45,\n                        incidents: 3,\n                        efficiency: 95\n                    },\n                    {\n                        site: \"Office Complex\",\n                        shifts: 38,\n                        incidents: 1,\n                        efficiency: 98\n                    },\n                    {\n                        site: \"Warehouse A\",\n                        shifts: 32,\n                        incidents: 2,\n                        efficiency: 92\n                    },\n                    {\n                        site: \"Retail Center\",\n                        shifts: 28,\n                        incidents: 4,\n                        efficiency: 88\n                    },\n                    {\n                        site: \"Industrial Park\",\n                        shifts: 25,\n                        incidents: 1,\n                        efficiency: 96\n                    }\n                ]\n            });\n            setKpis({\n                totalShifts: 156,\n                completedShifts: 142,\n                totalHours: 1248,\n                averageRating: 4.7,\n                activeAgents: 18,\n                totalReports: 89,\n                responseTime: 12.5,\n                efficiency: 94.2\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleExport = async ()=>{\n        try {\n            // In a real app, this would generate and download analytics report\n            console.log(\"Exporting analytics data...\");\n            alert(\"Export functionality would be implemented here\");\n        } catch (error) {\n            console.error(\"Error exporting analytics:\", error);\n        }\n    };\n    const KPICard = (param)=>{\n        let { title, value, unit = \"\", icon: Icon, color = \"primary\", trend } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            elevation: 2,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: \"\".concat(color, \".main\"),\n                                    children: [\n                                        value,\n                                        unit\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined),\n                                trend !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        mt: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            color: trend >= 0 ? \"success\" : \"error\",\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"caption\",\n                                            color: trend >= 0 ? \"success.main\" : \"error.main\",\n                                            sx: {\n                                                ml: 0.5\n                                            },\n                                            children: [\n                                                Math.abs(trend),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 180,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 175,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: \"\".concat(color, \".main\"),\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                    lineNumber: 166,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n            lineNumber: 164,\n            columnNumber: 5\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            p: 3\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    mb: 3,\n                    display: \"flex\",\n                    justifyContent: \"space-between\",\n                    alignItems: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"h4\",\n                        fontWeight: \"bold\",\n                        children: \"Analytics Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 200,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            gap: 1,\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                size: \"small\",\n                                sx: {\n                                    minWidth: 120\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        children: \"Time Range\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        value: timeRange,\n                                        label: \"Time Range\",\n                                        onChange: (e)=>setTimeRange(e.target.value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                value: \"1d\",\n                                                children: \"Last 24 Hours\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                value: \"7d\",\n                                                children: \"Last 7 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 212,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                value: \"30d\",\n                                                children: \"Last 30 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 213,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                value: \"90d\",\n                                                children: \"Last 90 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 214,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                variant: \"outlined\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 24\n                                }, void 0),\n                                onClick: handleExport,\n                                children: \"Export\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 218,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                variant: \"outlined\",\n                                startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 228,\n                                    columnNumber: 24\n                                }, void 0),\n                                onClick: loadAnalyticsData,\n                                children: \"Refresh\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                severity: \"warning\",\n                sx: {\n                    mb: 2\n                },\n                children: [\n                    error,\n                    \" - Showing sample data for demonstration\"\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                            title: \"Total Shifts\",\n                            value: kpis.totalShifts,\n                            icon: _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                            color: \"primary\",\n                            trend: 12.5\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 245,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                            title: \"Completion Rate\",\n                            value: (kpis.completedShifts / kpis.totalShifts * 100).toFixed(1),\n                            unit: \"%\",\n                            icon: _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            color: \"success\",\n                            trend: 5.2\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                            title: \"Total Hours\",\n                            value: kpis.totalHours.toLocaleString(),\n                            unit: \"h\",\n                            icon: _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                            color: \"info\",\n                            trend: 8.7\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 264,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                            title: \"Average Rating\",\n                            value: kpis.averageRating,\n                            unit: \"/5\",\n                            icon: _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                            color: \"warning\",\n                            trend: 2.1\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 275,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 274,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 244,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                container: true,\n                spacing: 3,\n                sx: {\n                    mb: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                            title: \"Active Agents\",\n                            value: kpis.activeAgents,\n                            icon: _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n                            color: \"secondary\",\n                            trend: -1.5\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 288,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                            title: \"Total Reports\",\n                            value: kpis.totalReports,\n                            icon: _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                            color: \"primary\",\n                            trend: 15.3\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                            title: \"Avg Response Time\",\n                            value: kpis.responseTime,\n                            unit: \"min\",\n                            icon: _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                            color: \"warning\",\n                            trend: -8.2\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 306,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 3,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                            title: \"Efficiency\",\n                            value: kpis.efficiency,\n                            unit: \"%\",\n                            icon: _barrel_optimize_names_Assignment_Download_People_Refresh_Schedule_Security_TrendingUp_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                            color: \"success\",\n                            trend: 3.4\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 317,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                sx: {\n                    display: \"flex\",\n                    justifyContent: \"center\",\n                    py: 8\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                    size: 60\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                    lineNumber: 331,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 330,\n                columnNumber: 9\n            }, undefined) : analyticsData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AnalyticsCharts__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                data: analyticsData,\n                loading: loading,\n                timeRange: timeRange,\n                onTimeRangeChange: setTimeRange\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                sx: {\n                    p: 4,\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"h6\",\n                        color: \"textSecondary\",\n                        children: \"No analytics data available\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 342,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Box_Button_Card_CardContent_CircularProgress_FormControl_Grid_InputLabel_MenuItem_Paper_Select_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                        variant: \"body2\",\n                        color: \"textSecondary\",\n                        sx: {\n                            mt: 1\n                        },\n                        children: \"Data will appear here once shifts and reports are created\"\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n        lineNumber: 197,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AnalyticsPage, \"GmQR6AHUiHXyA713mWB6w+LCnB8=\");\n_c = AnalyticsPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AnalyticsPage);\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.js\n"));

/***/ })

});