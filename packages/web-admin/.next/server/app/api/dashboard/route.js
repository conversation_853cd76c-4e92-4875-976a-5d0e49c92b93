"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/dashboard/route";
exports.ids = ["app/api/dashboard/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/../../node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _workspaces_finalagent_main_final_packages_web_admin_src_app_api_dashboard_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/dashboard/route.js */ \"(rsc)/./src/app/api/dashboard/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/dashboard/route\",\n        pathname: \"/api/dashboard\",\n        filename: \"route\",\n        bundlePath: \"app/api/dashboard/route\"\n    },\n    resolvedPagePath: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/api/dashboard/route.js\",\n    nextConfigOutput,\n    userland: _workspaces_finalagent_main_final_packages_web_admin_src_app_api_dashboard_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/dashboard/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/dashboard/route.js":
/*!****************************************!*\
  !*** ./src/app/api/dashboard/route.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/../../node_modules/next/dist/api/server.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Global Prisma instance for serverless optimization\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma || new _prisma_client__WEBPACK_IMPORTED_MODULE_1__.PrismaClient({\n    datasources: {\n        db: {\n            url: process.env.DATABASE_URL\n        }\n    },\n    log:  true ? [\n        \"error\"\n    ] : 0\n});\n// Prevent multiple instances in development\nif (true) {\n    globalForPrisma.prisma = prisma;\n}\nasync function GET(request) {\n    try {\n        const now = new Date();\n        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());\n        const last7Days = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);\n        let dashboardData;\n        try {\n            // Fetch real dashboard data from database\n            const [totalAgents, activeAgents, todayShifts, activeShifts, pendingReports, recentReports, activeSites, recentActivity] = await Promise.all([\n                // Total agents\n                prisma.user.count({\n                    where: {\n                        role: \"AGENT\",\n                        isActive: true\n                    }\n                }),\n                // Active agents (agents with shifts today or currently on duty)\n                prisma.user.count({\n                    where: {\n                        role: \"AGENT\",\n                        isActive: true,\n                        agent: {\n                            isAvailable: true\n                        }\n                    }\n                }),\n                // Today's shifts\n                prisma.shift.count({\n                    where: {\n                        shiftDate: {\n                            gte: today,\n                            lt: new Date(today.getTime() + 24 * 60 * 60 * 1000)\n                        }\n                    }\n                }),\n                // Active shifts (in progress)\n                prisma.shift.count({\n                    where: {\n                        status: \"IN_PROGRESS\"\n                    }\n                }),\n                // Pending reports\n                prisma.report.count({\n                    where: {\n                        status: \"SUBMITTED\"\n                    }\n                }),\n                // Recent reports (last 7 days)\n                prisma.report.findMany({\n                    where: {\n                        createdAt: {\n                            gte: last7Days\n                        }\n                    },\n                    include: {\n                        agent: {\n                            include: {\n                                user: true\n                            }\n                        },\n                        shift: {\n                            include: {\n                                site: true\n                            }\n                        }\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 10\n                }),\n                // Active sites\n                prisma.site.count({\n                    where: {\n                        isActive: true\n                    }\n                }),\n                // Recent activity (shifts and reports)\n                prisma.shift.findMany({\n                    where: {\n                        createdAt: {\n                            gte: last7Days\n                        }\n                    },\n                    include: {\n                        agent: {\n                            include: {\n                                user: true\n                            }\n                        },\n                        site: true\n                    },\n                    orderBy: {\n                        createdAt: \"desc\"\n                    },\n                    take: 5\n                })\n            ]);\n            // Calculate additional metrics\n            const completedShiftsToday = await prisma.shift.count({\n                where: {\n                    shiftDate: {\n                        gte: today,\n                        lt: new Date(today.getTime() + 24 * 60 * 60 * 1000)\n                    },\n                    status: \"COMPLETED\"\n                }\n            });\n            const completionRate = todayShifts > 0 ? completedShiftsToday / todayShifts * 100 : 0;\n            // Calculate average rating from recent reports\n            const reportsWithRating = recentReports.filter((r)=>r.rating && r.rating > 0);\n            const averageRating = reportsWithRating.length > 0 ? reportsWithRating.reduce((sum, r)=>sum + r.rating, 0) / reportsWithRating.length : 0;\n            dashboardData = {\n                kpis: {\n                    totalAgents,\n                    activeAgents,\n                    todayShifts,\n                    activeShifts,\n                    pendingReports,\n                    activeSites,\n                    completionRate: Math.round(completionRate * 10) / 10,\n                    averageRating: Math.round(averageRating * 10) / 10\n                },\n                recentActivity: recentActivity.map((shift)=>({\n                        id: shift.id,\n                        type: \"shift\",\n                        title: `Shift at ${shift.site?.name || \"Unknown Site\"}`,\n                        agent: shift.agent?.user?.firstName + \" \" + shift.agent?.user?.lastName,\n                        time: shift.createdAt,\n                        status: shift.status\n                    })),\n                recentReports: recentReports.map((report)=>({\n                        id: report.id,\n                        type: report.type,\n                        title: report.title,\n                        agent: report.agent?.user?.firstName + \" \" + report.agent?.user?.lastName,\n                        site: report.shift?.site?.name,\n                        time: report.createdAt,\n                        status: report.status,\n                        priority: report.priority\n                    })),\n                lastUpdated: new Date().toISOString()\n            };\n        } catch (dbError) {\n            console.error(\"Database query error:\", dbError);\n            // Return sample data for demonstration when database is not available\n            dashboardData = {\n                kpis: {\n                    totalAgents: 25,\n                    activeAgents: 18,\n                    todayShifts: 12,\n                    activeShifts: 8,\n                    pendingReports: 5,\n                    activeSites: 15,\n                    completionRate: 92.5,\n                    averageRating: 4.6\n                },\n                recentActivity: [\n                    {\n                        id: \"1\",\n                        type: \"shift\",\n                        title: \"Shift at Sonatel HQ\",\n                        agent: \"Amadou Ba\",\n                        time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n                        status: \"IN_PROGRESS\"\n                    },\n                    {\n                        id: \"2\",\n                        type: \"shift\",\n                        title: \"Shift at CBAO Bank Dakar\",\n                        agent: \"Fatou Sow\",\n                        time: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),\n                        status: \"COMPLETED\"\n                    }\n                ],\n                recentReports: [\n                    {\n                        id: \"1\",\n                        type: \"INCIDENT\",\n                        title: \"Suspicious activity reported\",\n                        agent: \"Moussa Diop\",\n                        site: \"Orange Senegal\",\n                        time: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),\n                        status: \"SUBMITTED\",\n                        priority: \"HIGH\"\n                    }\n                ],\n                lastUpdated: new Date().toISOString(),\n                note: \"Sample data - Database connection pending\"\n            };\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(dashboardData);\n    } catch (error) {\n        console.error(\"Dashboard API Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch dashboard data\",\n            details: error.message\n        }, {\n            status: 500\n        });\n    } finally{\n        await prisma.$disconnect();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/dashboard/route.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@opentelemetry"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fdashboard%2Froute&page=%2Fapi%2Fdashboard%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fdashboard%2Froute.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();