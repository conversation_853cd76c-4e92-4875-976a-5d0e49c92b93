'use client';

import { Box, Typography } from '@mui/material';

/**
 * Clean, minimalistic KPI card component
 * Used for displaying key metrics across all pages
 */
export default function CleanKPICard({
  title,
  value,
  unit = '',
  trend = null,
  trendLabel = '',
  color = '#1a1a1a',
  size = 'large'
}) {
  const fontSize = size === 'large' ? '3rem' : size === 'medium' ? '2rem' : '1.5rem';
  
  const getTrendColor = (trend) => {
    if (trend === null || trend === undefined) return '#6b7280';
    return trend >= 0 ? '#10b981' : '#ef4444';
  };

  const formatTrend = (trend) => {
    if (trend === null || trend === undefined) return '';
    const sign = trend >= 0 ? '+' : '';
    return `${sign}${trend}%`;
  };

  return (
    <Box sx={{ 
      p: 4, 
      borderRadius: 3,
      border: '1px solid #f1f5f9',
      backgroundColor: '#ffffff',
      transition: 'all 0.3s ease',
      '&:hover': {
        borderColor: '#e2e8f0',
        transform: 'translateY(-2px)',
        boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
      }
    }}>
      <Typography variant="h1" sx={{ 
        fontWeight: 700, 
        color: color,
        mb: 1,
        fontSize: fontSize,
        lineHeight: 1
      }}>
        {value}{unit}
      </Typography>
      
      <Typography variant="body1" sx={{ 
        color: '#6b7280', 
        fontSize: '15px',
        fontWeight: 500,
        mb: trend !== null ? 1 : 0
      }}>
        {title}
      </Typography>
      
      {trend !== null && (
        <Typography variant="caption" sx={{ 
          color: getTrendColor(trend), 
          fontSize: '13px',
          fontWeight: 500
        }}>
          {formatTrend(trend)} {trendLabel}
        </Typography>
      )}
    </Box>
  );
}
