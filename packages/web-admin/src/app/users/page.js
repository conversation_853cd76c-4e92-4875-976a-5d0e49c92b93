// BahinLink Web Admin Users Management Page
// ⚠️ CRITICAL: Real user management with role-based access ONLY

'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Alert,
  CircularProgress,
  Avatar
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  Edit as EditIcon,
  People as PeopleIcon,
  AdminPanelSettings as AdminIcon,
  Security as SecurityIcon,
  Email,
  Phone
} from '@mui/icons-material';
import ModernSidebar from '../../components/ModernSidebar';

const UsersPage = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [filters, setFilters] = useState({
    role: '',
    status: '',
    search: ''
  });
  const [stats, setStats] = useState({
    total: 0,
    admins: 0,
    supervisors: 0,
    agents: 0,
    clients: 0,
    active: 0,
    inactive: 0
  });
  const [newUser, setNewUser] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: 'AGENT',
    isActive: true
  });

  useEffect(() => {
    loadUsers();
  }, [filters]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/users');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      setUsers(data.users || []);

      // Calculate stats
      const usersData = data.users || [];
      const newStats = {
        total: usersData.length,
        admins: usersData.filter(u => u.role === 'ADMIN').length,
        supervisors: usersData.filter(u => u.role === 'SUPERVISOR').length,
        agents: usersData.filter(u => u.role === 'AGENT').length,
        clients: usersData.filter(u => u.role === 'CLIENT').length,
        active: usersData.filter(u => u.isActive).length,
        inactive: usersData.filter(u => !u.isActive).length
      };
      setStats(newStats);

    } catch (error) {
      console.error('Error loading users:', error);
      setError('Failed to load users');
      // Set fallback data
      setUsers([]);
      setStats({
        total: 0,
        admins: 0,
        supervisors: 0,
        agents: 0,
        clients: 0,
        active: 0,
        inactive: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMenuOpen = (event, user) => {
    setAnchorEl(event.currentTarget);
    setSelectedUser(user);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedUser(null);
  };

  const handleCreateUser = async () => {
    try {
      const response = await ApiService.post('/users', newUser);
      
      if (response.success) {
        setCreateDialogOpen(false);
        setNewUser({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          role: 'AGENT',
          isActive: true
        });
        loadUsers();
      }
    } catch (error) {
      console.error('Error creating user:', error);
      setError('Failed to create user');
    }
  };

  const handleEditUser = async () => {
    try {
      const response = await ApiService.put(`/users/${selectedUser.id}`, selectedUser);
      
      if (response.success) {
        setEditDialogOpen(false);
        setSelectedUser(null);
        loadUsers();
      }
    } catch (error) {
      console.error('Error updating user:', error);
      setError('Failed to update user');
    }
  };

  const handleToggleStatus = async (userId, isActive) => {
    try {
      const response = await ApiService.put(`/users/${userId}`, { isActive: !isActive });
      
      if (response.success) {
        loadUsers();
      }
    } catch (error) {
      console.error('Error updating user status:', error);
      setError('Failed to update user status');
    }
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        const response = await ApiService.delete(`/users/${userId}`);
        
        if (response.success) {
          loadUsers();
        }
      } catch (error) {
        console.error('Error deleting user:', error);
        setError('Failed to delete user');
      }
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'ADMIN': return 'error';
      case 'SUPERVISOR': return 'warning';
      case 'AGENT': return 'primary';
      case 'CLIENT': return 'info';
      default: return 'default';
    }
  };

  const getRoleIcon = (role) => {
    switch (role) {
      case 'ADMIN': return <AdminIcon fontSize="small" />;
      case 'SUPERVISOR': return <SecurityIcon fontSize="small" />;
      case 'AGENT': return <PeopleIcon fontSize="small" />;
      case 'CLIENT': return <BusinessIcon fontSize="small" />;
      default: return <PeopleIcon fontSize="small" />;
    }
  };

  const getInitials = (firstName, lastName) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  const StatCard = ({ title, value, icon: Icon, color = 'primary' }) => (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" component="div" color={`${color}.main`}>
              {value}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {title}
            </Typography>
          </Box>
          <Icon sx={{ fontSize: 40, color: `${color}.main`, opacity: 0.7 }} />
        </Box>
      </CardContent>
    </Card>
  );

  const handleExport = () => {
    if (users.length > 0) {
      const dataStr = JSON.stringify(users, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

      const exportFileDefaultName = `users-${new Date().toISOString().split('T')[0]}.json`;

      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    }
  };

  if (loading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh'
      }}>
        <CircularProgress size={40} sx={{ color: '#6b7280' }} />
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#ffffff' }}>
      <ModernSidebar />

      <Box component="main" sx={{ flexGrow: 1, p: 4 }}>
        {/* Clean Header */}
        <Box sx={{ mb: 6, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box>
            <Typography
              variant="h3"
              sx={{
                fontWeight: 300,
                color: '#1a1a1a',
                mb: 1,
                letterSpacing: '-0.02em',
                fontSize: '2.5rem'
              }}
            >
              Users
            </Typography>
            <Typography
              variant="body1"
              sx={{
                color: '#6b7280',
                fontSize: '16px'
              }}
            >
              Manage user accounts and permissions
            </Typography>
          </Box>

          {/* Clean Controls */}
          <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<DownloadIcon />}
              onClick={handleExport}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Export
            </Button>

            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadUsers}
              sx={{
                borderColor: '#e5e7eb',
                color: '#6b7280',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  borderColor: '#d1d5db',
                  backgroundColor: '#f9fafb'
                }
              }}
            >
              Refresh
            </Button>

            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
              sx={{
                backgroundColor: '#3b82f6',
                fontSize: '14px',
                textTransform: 'none',
                '&:hover': {
                  backgroundColor: '#2563eb'
                }
              }}
            >
              Add User
            </Button>
          </Box>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 4 }}>
            {error}
          </Alert>
        )}

        {/* Minimalist KPI Grid */}
        <Box sx={{ mb: 8 }}>
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
              gap: 4,
              mb: 6
            }}
          >
            {/* Total Users */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#1a1a1a',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.total}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Total Users
              </Typography>
            </Box>

            {/* Admins */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#ef4444',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.admins}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Admins
              </Typography>
            </Box>

            {/* Supervisors */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#f59e0b',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.supervisors}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Supervisors
              </Typography>
            </Box>

            {/* Agents */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#3b82f6',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.agents}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Agents
              </Typography>
            </Box>

            {/* Active Users */}
            <Box sx={{
              p: 4,
              borderRadius: 3,
              border: '1px solid #f1f5f9',
              backgroundColor: '#ffffff',
              transition: 'all 0.3s ease',
              '&:hover': {
                borderColor: '#e2e8f0',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 25px rgba(0,0,0,0.08)'
              }
            }}>
              <Typography variant="h1" sx={{
                fontWeight: 700,
                color: '#10b981',
                mb: 1,
                fontSize: '3rem',
                lineHeight: 1
              }}>
                {stats.active}
              </Typography>
              <Typography variant="body1" sx={{
                color: '#6b7280',
                fontSize: '15px',
                fontWeight: 500
              }}>
                Active Users
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* Clean Users Table */}
        <Box sx={{
          borderRadius: 3,
          border: '1px solid #f1f5f9',
          backgroundColor: '#ffffff',
          overflow: 'hidden'
        }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8fafc' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    User
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Contact
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Role
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Status
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Last Login
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#374151', fontSize: '14px' }}>
                    Actions
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} sx={{ textAlign: 'center', py: 4 }}>
                      <Typography variant="body2" color="textSecondary">
                        No users found
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow
                      key={user.id}
                      sx={{
                        '&:hover': { backgroundColor: '#f9fafb' },
                        borderBottom: '1px solid #f1f5f9'
                      }}
                    >
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar sx={{ width: 40, height: 40, backgroundColor: '#3b82f6' }}>
                            {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 500, color: '#1a1a1a' }}>
                              {user.firstName} {user.lastName}
                            </Typography>
                            <Typography variant="caption" sx={{ color: '#6b7280' }}>
                              ID: {user.clerkId?.substring(0, 8)}...
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Box>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151', mb: 0.5 }}>
                            <Email sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                            {user.email}
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                            <Phone sx={{ fontSize: 16, mr: 0.5, verticalAlign: 'middle' }} />
                            {user.phone || 'N/A'}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Chip
                          label={user.role}
                          size="small"
                          icon={
                            user.role === 'ADMIN' ? <AdminIcon sx={{ fontSize: 16 }} /> :
                            user.role === 'SUPERVISOR' ? <SecurityIcon sx={{ fontSize: 16 }} /> :
                            <PeopleIcon sx={{ fontSize: 16 }} />
                          }
                          sx={{
                            backgroundColor:
                              user.role === 'ADMIN' ? '#ef4444' :
                              user.role === 'SUPERVISOR' ? '#f59e0b' :
                              user.role === 'AGENT' ? '#3b82f6' : '#6b7280',
                            color: 'white',
                            fontSize: '12px'
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Chip
                          label={user.isActive ? 'Active' : 'Inactive'}
                          size="small"
                          sx={{
                            backgroundColor: user.isActive ? '#10b981' : '#6b7280',
                            color: 'white',
                            fontSize: '12px'
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <Typography variant="body2" sx={{ fontSize: '14px', color: '#374151' }}>
                          {user.lastLoginAt ?
                            new Date(user.lastLoginAt).toLocaleDateString() :
                            'Never'
                          }
                        </Typography>
                        <Typography variant="caption" sx={{ color: '#6b7280' }}>
                          {user.lastLoginAt ?
                            new Date(user.lastLoginAt).toLocaleTimeString() :
                            ''
                          }
                        </Typography>
                      </TableCell>
                      <TableCell sx={{ borderBottom: 'none' }}>
                        <IconButton
                          size="small"
                          onClick={() => setEditDialogOpen(true)}
                          sx={{ color: '#6b7280' }}
                        >
                          <EditIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Box>
      </Box>
    </Box>
  );
};

export default UsersPage;
