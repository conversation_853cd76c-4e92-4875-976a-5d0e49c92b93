/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/users/page";
exports.ids = ["app/users/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "node:buffer":
/*!******************************!*\
  !*** external "node:buffer" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:buffer");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:stream/web":
/*!**********************************!*\
  !*** external "node:stream/web" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream/web");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?1c3a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'users',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/users/page.js */ \"(rsc)/./src/app/users/page.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\")), \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/users/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/users/page\",\n        pathname: \"/users\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/app-router/client/ClerkProvider.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/controlComponents.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/hooks.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js */ \"(ssr)/../../node_modules/@clerk/nextjs/dist/esm/client-boundary/uiComponents.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fapp-router%2Fclient%2FClerkProvider.js%22%2C%22ids%22%3A%5B%22ClientClerkProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FcontrolComponents.js%22%2C%22ids%22%3A%5B%22AuthenticateWithRedirectCallback%22%2C%22ClerkLoaded%22%2C%22ClerkLoading%22%2C%22MultisessionAppSupport%22%2C%22RedirectToCreateOrganization%22%2C%22RedirectToOrganizationProfile%22%2C%22RedirectToSignIn%22%2C%22RedirectToSignUp%22%2C%22RedirectToUserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2Fhooks.js%22%2C%22ids%22%3A%5B%22EmailLinkErrorCode%22%2C%22MagicLinkErrorCode%22%2C%22WithClerk%22%2C%22WithSession%22%2C%22WithUser%22%2C%22isClerkAPIResponseError%22%2C%22isEmailLinkError%22%2C%22isKnownError%22%2C%22isMagicLinkError%22%2C%22isMetamaskError%22%2C%22useAuth%22%2C%22useClerk%22%2C%22useEmailLink%22%2C%22useMagicLink%22%2C%22useOrganization%22%2C%22useOrganizationList%22%2C%22useOrganizations%22%2C%22useSession%22%2C%22useSessionList%22%2C%22useSignIn%22%2C%22useSignUp%22%2C%22useUser%22%2C%22withClerk%22%2C%22withSession%22%2C%22withUser%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2F%40clerk%2Fnextjs%2Fdist%2Fesm%2Fclient-boundary%2FuiComponents.js%22%2C%22ids%22%3A%5B%22CreateOrganization%22%2C%22GoogleOneTap%22%2C%22OrganizationList%22%2C%22OrganizationProfile%22%2C%22OrganizationSwitcher%22%2C%22SignIn%22%2C%22SignInButton%22%2C%22SignInWithMetamaskButton%22%2C%22SignOutButton%22%2C%22SignUp%22%2C%22SignUpButton%22%2C%22UserButton%22%2C%22UserProfile%22%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fusers%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fusers%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/users/page.js */ \"(ssr)/./src/app/users/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZ3b3Jrc3BhY2VzJTJGZmluYWxhZ2VudC1tYWluLWZpbmFsJTJGcGFja2FnZXMlMkZ3ZWItYWRtaW4lMkZzcmMlMkZhcHAlMkZ1c2VycyUyRnBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUErRyIsInNvdXJjZXMiOlsid2VicGFjazovL0BiYWhpbmxpbmsvd2ViLWFkbWluLz9iNTdmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL3dvcmtzcGFjZXMvZmluYWxhZ2VudC1tYWluLWZpbmFsL3BhY2thZ2VzL3dlYi1hZG1pbi9zcmMvYXBwL3VzZXJzL3BhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp%2Fusers%2Fpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/users/page.js":
/*!*******************************!*\
  !*** ./src/app/users/page.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Chip,CircularProgress,IconButton,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Security.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Download.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Add.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Email.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Phone.js\");\n/* harmony import */ var _barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Add,AdminPanelSettings,Download,Edit,Email,People,Phone,Refresh,Security!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Edit.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/ModernSidebar */ \"(ssr)/./src/components/ModernSidebar.js\");\n// BahinLink Web Admin Users Management Page\n// ⚠️ CRITICAL: Real user management with role-based access ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst UsersPage = ()=>{\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [anchorEl, setAnchorEl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedUser, setSelectedUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [createDialogOpen, setCreateDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editDialogOpen, setEditDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        role: \"\",\n        status: \"\",\n        search: \"\"\n    });\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        admins: 0,\n        supervisors: 0,\n        agents: 0,\n        clients: 0,\n        active: 0,\n        inactive: 0\n    });\n    const [newUser, setNewUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        firstName: \"\",\n        lastName: \"\",\n        email: \"\",\n        phone: \"\",\n        role: \"AGENT\",\n        isActive: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadUsers();\n    }, [\n        filters\n    ]);\n    const loadUsers = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await fetch(\"/api/users\");\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const data = await response.json();\n            setUsers(data.users || []);\n            // Calculate stats\n            const usersData = data.users || [];\n            const newStats = {\n                total: usersData.length,\n                admins: usersData.filter((u)=>u.role === \"ADMIN\").length,\n                supervisors: usersData.filter((u)=>u.role === \"SUPERVISOR\").length,\n                agents: usersData.filter((u)=>u.role === \"AGENT\").length,\n                clients: usersData.filter((u)=>u.role === \"CLIENT\").length,\n                active: usersData.filter((u)=>u.isActive).length,\n                inactive: usersData.filter((u)=>!u.isActive).length\n            };\n            setStats(newStats);\n        } catch (error) {\n            console.error(\"Error loading users:\", error);\n            setError(\"Failed to load users\");\n            // Set fallback data\n            setUsers([]);\n            setStats({\n                total: 0,\n                admins: 0,\n                supervisors: 0,\n                agents: 0,\n                clients: 0,\n                active: 0,\n                inactive: 0\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleMenuOpen = (event, user)=>{\n        setAnchorEl(event.currentTarget);\n        setSelectedUser(user);\n    };\n    const handleMenuClose = ()=>{\n        setAnchorEl(null);\n        setSelectedUser(null);\n    };\n    const handleCreateUser = async ()=>{\n        try {\n            const response = await ApiService.post(\"/users\", newUser);\n            if (response.success) {\n                setCreateDialogOpen(false);\n                setNewUser({\n                    firstName: \"\",\n                    lastName: \"\",\n                    email: \"\",\n                    phone: \"\",\n                    role: \"AGENT\",\n                    isActive: true\n                });\n                loadUsers();\n            }\n        } catch (error) {\n            console.error(\"Error creating user:\", error);\n            setError(\"Failed to create user\");\n        }\n    };\n    const handleEditUser = async ()=>{\n        try {\n            const response = await ApiService.put(`/users/${selectedUser.id}`, selectedUser);\n            if (response.success) {\n                setEditDialogOpen(false);\n                setSelectedUser(null);\n                loadUsers();\n            }\n        } catch (error) {\n            console.error(\"Error updating user:\", error);\n            setError(\"Failed to update user\");\n        }\n    };\n    const handleToggleStatus = async (userId, isActive)=>{\n        try {\n            const response = await ApiService.put(`/users/${userId}`, {\n                isActive: !isActive\n            });\n            if (response.success) {\n                loadUsers();\n            }\n        } catch (error) {\n            console.error(\"Error updating user status:\", error);\n            setError(\"Failed to update user status\");\n        }\n    };\n    const handleDeleteUser = async (userId)=>{\n        if (window.confirm(\"Are you sure you want to delete this user?\")) {\n            try {\n                const response = await ApiService.delete(`/users/${userId}`);\n                if (response.success) {\n                    loadUsers();\n                }\n            } catch (error) {\n                console.error(\"Error deleting user:\", error);\n                setError(\"Failed to delete user\");\n            }\n        }\n    };\n    const getRoleColor = (role)=>{\n        switch(role){\n            case \"ADMIN\":\n                return \"error\";\n            case \"SUPERVISOR\":\n                return \"warning\";\n            case \"AGENT\":\n                return \"primary\";\n            case \"CLIENT\":\n                return \"info\";\n            default:\n                return \"default\";\n        }\n    };\n    const getRoleIcon = (role)=>{\n        switch(role){\n            case \"ADMIN\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 204,\n                    columnNumber: 28\n                }, undefined);\n            case \"SUPERVISOR\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 205,\n                    columnNumber: 33\n                }, undefined);\n            case \"AGENT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 206,\n                    columnNumber: 28\n                }, undefined);\n            case \"CLIENT\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BusinessIcon, {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 207,\n                    columnNumber: 29\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    fontSize: \"small\"\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 208,\n                    columnNumber: 23\n                }, undefined);\n        }\n    };\n    const getInitials = (firstName, lastName)=>{\n        return `${firstName?.[0] || \"\"}${lastName?.[0] || \"\"}`.toUpperCase();\n    };\n    const StatCard = ({ title, value, icon: Icon, color = \"primary\" })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: `${color}.main`,\n                                    children: value\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: `${color}.main`,\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n            lineNumber: 217,\n            columnNumber: 5\n        }, undefined);\n    const handleExport = ()=>{\n        if (users.length > 0) {\n            const dataStr = JSON.stringify(users, null, 2);\n            const dataUri = \"data:application/json;charset=utf-8,\" + encodeURIComponent(dataStr);\n            const exportFileDefaultName = `users-${new Date().toISOString().split(\"T\")[0]}.json`;\n            const linkElement = document.createElement(\"a\");\n            linkElement.setAttribute(\"href\", dataUri);\n            linkElement.setAttribute(\"download\", exportFileDefaultName);\n            linkElement.click();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            sx: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                minHeight: \"100vh\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                size: 40,\n                sx: {\n                    color: \"#6b7280\"\n                }\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                lineNumber: 256,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n            lineNumber: 250,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#ffffff\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                lineNumber: 263,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    p: 4\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            mb: 6,\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"flex-start\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        variant: \"h3\",\n                                        sx: {\n                                            fontWeight: 300,\n                                            color: \"#1a1a1a\",\n                                            mb: 1,\n                                            letterSpacing: \"-0.02em\",\n                                            fontSize: \"2.5rem\"\n                                        },\n                                        children: \"Users\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 269,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        variant: \"body1\",\n                                        sx: {\n                                            color: \"#6b7280\",\n                                            fontSize: \"16px\"\n                                        },\n                                        children: \"Manage user accounts and permissions\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 281,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    gap: 2,\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 296,\n                                            columnNumber: 26\n                                        }, void 0),\n                                        onClick: handleExport,\n                                        sx: {\n                                            borderColor: \"#e5e7eb\",\n                                            color: \"#6b7280\",\n                                            fontSize: \"14px\",\n                                            textTransform: \"none\",\n                                            \"&:hover\": {\n                                                borderColor: \"#d1d5db\",\n                                                backgroundColor: \"#f9fafb\"\n                                            }\n                                        },\n                                        children: \"Export\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 294,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 314,\n                                            columnNumber: 26\n                                        }, void 0),\n                                        onClick: loadUsers,\n                                        sx: {\n                                            borderColor: \"#e5e7eb\",\n                                            color: \"#6b7280\",\n                                            fontSize: \"14px\",\n                                            textTransform: \"none\",\n                                            \"&:hover\": {\n                                                borderColor: \"#d1d5db\",\n                                                backgroundColor: \"#f9fafb\"\n                                            }\n                                        },\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 312,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        variant: \"contained\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 332,\n                                            columnNumber: 26\n                                        }, void 0),\n                                        onClick: ()=>setCreateDialogOpen(true),\n                                        sx: {\n                                            backgroundColor: \"#3b82f6\",\n                                            fontSize: \"14px\",\n                                            textTransform: \"none\",\n                                            \"&:hover\": {\n                                                backgroundColor: \"#2563eb\"\n                                            }\n                                        },\n                                        children: \"Add User\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        severity: \"error\",\n                        sx: {\n                            mb: 4\n                        },\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            mb: 8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            sx: {\n                                display: \"grid\",\n                                gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                                gap: 4,\n                                mb: 6\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        p: 4,\n                                        borderRadius: 3,\n                                        border: \"1px solid #f1f5f9\",\n                                        backgroundColor: \"#ffffff\",\n                                        transition: \"all 0.3s ease\",\n                                        \"&:hover\": {\n                                            borderColor: \"#e2e8f0\",\n                                            transform: \"translateY(-2px)\",\n                                            boxShadow: \"0 8px 25px rgba(0,0,0,0.08)\"\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#1a1a1a\",\n                                                mb: 1,\n                                                fontSize: \"3rem\",\n                                                lineHeight: 1\n                                            },\n                                            children: stats.total\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 378,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#6b7280\",\n                                                fontSize: \"15px\",\n                                                fontWeight: 500\n                                            },\n                                            children: \"Total Users\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        p: 4,\n                                        borderRadius: 3,\n                                        border: \"1px solid #f1f5f9\",\n                                        backgroundColor: \"#ffffff\",\n                                        transition: \"all 0.3s ease\",\n                                        \"&:hover\": {\n                                            borderColor: \"#e2e8f0\",\n                                            transform: \"translateY(-2px)\",\n                                            boxShadow: \"0 8px 25px rgba(0,0,0,0.08)\"\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#ef4444\",\n                                                mb: 1,\n                                                fontSize: \"3rem\",\n                                                lineHeight: 1\n                                            },\n                                            children: stats.admins\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#6b7280\",\n                                                fontSize: \"15px\",\n                                                fontWeight: 500\n                                            },\n                                            children: \"Admins\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        p: 4,\n                                        borderRadius: 3,\n                                        border: \"1px solid #f1f5f9\",\n                                        backgroundColor: \"#ffffff\",\n                                        transition: \"all 0.3s ease\",\n                                        \"&:hover\": {\n                                            borderColor: \"#e2e8f0\",\n                                            transform: \"translateY(-2px)\",\n                                            boxShadow: \"0 8px 25px rgba(0,0,0,0.08)\"\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#f59e0b\",\n                                                mb: 1,\n                                                fontSize: \"3rem\",\n                                                lineHeight: 1\n                                            },\n                                            children: stats.supervisors\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#6b7280\",\n                                                fontSize: \"15px\",\n                                                fontWeight: 500\n                                            },\n                                            children: \"Supervisors\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        p: 4,\n                                        borderRadius: 3,\n                                        border: \"1px solid #f1f5f9\",\n                                        backgroundColor: \"#ffffff\",\n                                        transition: \"all 0.3s ease\",\n                                        \"&:hover\": {\n                                            borderColor: \"#e2e8f0\",\n                                            transform: \"translateY(-2px)\",\n                                            boxShadow: \"0 8px 25px rgba(0,0,0,0.08)\"\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#3b82f6\",\n                                                mb: 1,\n                                                fontSize: \"3rem\",\n                                                lineHeight: 1\n                                            },\n                                            children: stats.agents\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 471,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#6b7280\",\n                                                fontSize: \"15px\",\n                                                fontWeight: 500\n                                            },\n                                            children: \"Agents\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    sx: {\n                                        p: 4,\n                                        borderRadius: 3,\n                                        border: \"1px solid #f1f5f9\",\n                                        backgroundColor: \"#ffffff\",\n                                        transition: \"all 0.3s ease\",\n                                        \"&:hover\": {\n                                            borderColor: \"#e2e8f0\",\n                                            transform: \"translateY(-2px)\",\n                                            boxShadow: \"0 8px 25px rgba(0,0,0,0.08)\"\n                                        }\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"h1\",\n                                            sx: {\n                                                fontWeight: 700,\n                                                color: \"#10b981\",\n                                                mb: 1,\n                                                fontSize: \"3rem\",\n                                                lineHeight: 1\n                                            },\n                                            children: stats.active\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 502,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            variant: \"body1\",\n                                            sx: {\n                                                color: \"#6b7280\",\n                                                fontSize: \"15px\",\n                                                fontWeight: 500\n                                            },\n                                            children: \"Active Users\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                    lineNumber: 490,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 357,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        sx: {\n                            borderRadius: 3,\n                            border: \"1px solid #f1f5f9\",\n                            backgroundColor: \"#ffffff\",\n                            overflow: \"hidden\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            sx: {\n                                                backgroundColor: \"#f8fafc\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#374151\",\n                                                        fontSize: \"14px\"\n                                                    },\n                                                    children: \"User\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#374151\",\n                                                        fontSize: \"14px\"\n                                                    },\n                                                    children: \"Contact\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 536,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#374151\",\n                                                        fontSize: \"14px\"\n                                                    },\n                                                    children: \"Role\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#374151\",\n                                                        fontSize: \"14px\"\n                                                    },\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#374151\",\n                                                        fontSize: \"14px\"\n                                                    },\n                                                    children: \"Last Login\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 545,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    sx: {\n                                                        fontWeight: 600,\n                                                        color: \"#374151\",\n                                                        fontSize: \"14px\"\n                                                    },\n                                                    children: \"Actions\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 548,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 532,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 531,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        children: users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                colSpan: 6,\n                                                sx: {\n                                                    textAlign: \"center\",\n                                                    py: 4\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    variant: \"body2\",\n                                                    color: \"textSecondary\",\n                                                    children: \"No users found\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 556,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                            lineNumber: 555,\n                                            columnNumber: 19\n                                        }, undefined) : users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                sx: {\n                                                    \"&:hover\": {\n                                                        backgroundColor: \"#f9fafb\"\n                                                    },\n                                                    borderBottom: \"1px solid #f1f5f9\"\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        sx: {\n                                                            borderBottom: \"none\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            sx: {\n                                                                display: \"flex\",\n                                                                alignItems: \"center\",\n                                                                gap: 2\n                                                            },\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    sx: {\n                                                                        width: 40,\n                                                                        height: 40,\n                                                                        backgroundColor: \"#3b82f6\"\n                                                                    },\n                                                                    children: [\n                                                                        user.firstName?.charAt(0),\n                                                                        user.lastName?.charAt(0)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                    lineNumber: 573,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            variant: \"body2\",\n                                                                            sx: {\n                                                                                fontWeight: 500,\n                                                                                color: \"#1a1a1a\"\n                                                                            },\n                                                                            children: [\n                                                                                user.firstName,\n                                                                                \" \",\n                                                                                user.lastName\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                            lineNumber: 577,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                            variant: \"caption\",\n                                                                            sx: {\n                                                                                color: \"#6b7280\"\n                                                                            },\n                                                                            children: [\n                                                                                \"ID: \",\n                                                                                user.clerkId?.substring(0, 8),\n                                                                                \"...\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                            lineNumber: 580,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 571,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        sx: {\n                                                            borderBottom: \"none\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"body2\",\n                                                                    sx: {\n                                                                        fontSize: \"14px\",\n                                                                        color: \"#374151\",\n                                                                        mb: 0.5\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                            sx: {\n                                                                                fontSize: 16,\n                                                                                mr: 0.5,\n                                                                                verticalAlign: \"middle\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                            lineNumber: 589,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        user.email\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                    lineNumber: 588,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    variant: \"body2\",\n                                                                    sx: {\n                                                                        fontSize: \"14px\",\n                                                                        color: \"#374151\"\n                                                                    },\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                            sx: {\n                                                                                fontSize: 16,\n                                                                                mr: 0.5,\n                                                                                verticalAlign: \"middle\"\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                            lineNumber: 593,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        user.phone || \"N/A\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                    lineNumber: 592,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        sx: {\n                                                            borderBottom: \"none\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            label: user.role,\n                                                            size: \"small\",\n                                                            icon: user.role === \"ADMIN\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: 16\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 603,\n                                                                columnNumber: 53\n                                                            }, void 0) : user.role === \"SUPERVISOR\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: 16\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 58\n                                                            }, void 0) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                sx: {\n                                                                    fontSize: 16\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 29\n                                                            }, void 0),\n                                                            sx: {\n                                                                backgroundColor: user.role === \"ADMIN\" ? \"#ef4444\" : user.role === \"SUPERVISOR\" ? \"#f59e0b\" : user.role === \"AGENT\" ? \"#3b82f6\" : \"#6b7280\",\n                                                                color: \"white\",\n                                                                fontSize: \"12px\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 598,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        sx: {\n                                                            borderBottom: \"none\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            label: user.isActive ? \"Active\" : \"Inactive\",\n                                                            size: \"small\",\n                                                            sx: {\n                                                                backgroundColor: user.isActive ? \"#10b981\" : \"#6b7280\",\n                                                                color: \"white\",\n                                                                fontSize: \"12px\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                            lineNumber: 618,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        sx: {\n                                                            borderBottom: \"none\"\n                                                        },\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                variant: \"body2\",\n                                                                sx: {\n                                                                    fontSize: \"14px\",\n                                                                    color: \"#374151\"\n                                                                },\n                                                                children: user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : \"Never\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 629,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                variant: \"caption\",\n                                                                sx: {\n                                                                    color: \"#6b7280\"\n                                                                },\n                                                                children: user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleTimeString() : \"\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 635,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 628,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        sx: {\n                                                            borderBottom: \"none\"\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Chip_CircularProgress_IconButton_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            size: \"small\",\n                                                            onClick: ()=>setEditDialogOpen(true),\n                                                            sx: {\n                                                                color: \"#6b7280\"\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Add_AdminPanelSettings_Download_Edit_Email_People_Phone_Refresh_Security_mui_icons_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {}, void 0, false, {\n                                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                                lineNumber: 648,\n                                                                columnNumber: 27\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                        lineNumber: 642,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, user.id, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                                lineNumber: 564,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                        lineNumber: 553,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                                lineNumber: 530,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                            lineNumber: 529,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                        lineNumber: 523,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n                lineNumber: 265,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js\",\n        lineNumber: 262,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UsersPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/users/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ModernSidebar.js":
/*!*****************************************!*\
  !*** ./src/components/ModernSidebar.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @clerk/nextjs */ \"(ssr)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/styles/useTheme.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Drawer/Drawer.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/IconButton/IconButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/system/esm/colorManipulator.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Chip/Chip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/List/List.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItem/ListItem.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Tooltip/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemButton/ListItemButton.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemIcon/ListItemIcon.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/ListItemText/ListItemText.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Divider/Divider.js\");\n/* harmony import */ var _barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=Avatar,Box,Chip,Divider,Drawer,IconButton,List,ListItem,ListItemButton,ListItemIcon,ListItemText,Tooltip,Typography,alpha,useTheme!=!@mui/material */ \"(ssr)/../../node_modules/@mui/material/Avatar/Avatar.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/LocationOn.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Assessment.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Analytics.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AccountCircle.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Shield.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ChevronRight.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ChevronLeft.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/Settings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/AdminPanelSettings.js\");\n/* harmony import */ var _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! __barrel_optimize__?names=AccountCircle,AdminPanelSettings,Analytics,Assessment,Assignment,ChevronLeft,ChevronRight,Dashboard,ExitToApp,LocationOn,People,Settings,Shield!=!@mui/icons-material */ \"(ssr)/../../node_modules/@mui/icons-material/esm/ExitToApp.js\");\n// BahinLink Modern Sidebar Navigation Component\n// ⚠️ CRITICAL: Sophisticated 2024 design with real data integration ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst SIDEBAR_WIDTH_EXPANDED = 280;\nconst SIDEBAR_WIDTH_COLLAPSED = 72;\nconst ModernSidebar = ()=>{\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const { signOut } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_3__.useClerk)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const theme = (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"])();\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Modern 2024 color palette - sophisticated neutrals with accent\n    const colors = {\n        sidebar: \"#1a1d29\",\n        sidebarHover: \"#252936\",\n        accent: \"#6366f1\",\n        accentHover: \"#5855eb\",\n        text: \"#e2e8f0\",\n        textSecondary: \"#94a3b8\",\n        textMuted: \"#64748b\",\n        border: \"#334155\",\n        success: \"#10b981\",\n        warning: \"#f59e0b\",\n        error: \"#ef4444\" // Modern red\n    };\n    const navigationItems = [\n        {\n            label: \"Dashboard\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            path: \"/\",\n            description: \"Overview & Analytics\"\n        },\n        {\n            label: \"Agents\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            path: \"/agents\",\n            description: \"Security Personnel\"\n        },\n        {\n            label: \"Sites\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            path: \"/sites\",\n            description: \"Client Locations\"\n        },\n        {\n            label: \"Shifts\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            path: \"/shifts\",\n            description: \"Schedule Management\"\n        },\n        {\n            label: \"Reports\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            path: \"/reports\",\n            description: \"Security Reports\"\n        },\n        {\n            label: \"Analytics\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            path: \"/analytics\",\n            description: \"Performance Metrics\"\n        },\n        {\n            label: \"Users\",\n            icon: _barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            path: \"/users\",\n            description: \"User Management\"\n        }\n    ];\n    const handleNavigation = (path)=>{\n        router.push(path);\n    };\n    const handleSignOut = async ()=>{\n        await signOut();\n        router.push(\"/sign-in\");\n    };\n    const toggleSidebar = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    const isActive = (path)=>{\n        return pathname === path;\n    };\n    const sidebarWidth = isCollapsed ? SIDEBAR_WIDTH_COLLAPSED : SIDEBAR_WIDTH_EXPANDED;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        variant: \"permanent\",\n        sx: {\n            width: sidebarWidth,\n            flexShrink: 0,\n            \"& .MuiDrawer-paper\": {\n                width: sidebarWidth,\n                boxSizing: \"border-box\",\n                backgroundColor: colors.sidebar,\n                borderRight: `1px solid ${colors.border}`,\n                transition: theme.transitions.create(\"width\", {\n                    easing: theme.transitions.easing.sharp,\n                    duration: theme.transitions.duration.enteringScreen\n                }),\n                overflowX: \"hidden\",\n                // Modern shadow\n                boxShadow: \"4px 0 24px rgba(0, 0, 0, 0.12)\"\n            }\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    p: 3,\n                    display: \"flex\",\n                    alignItems: \"center\",\n                    justifyContent: isCollapsed ? \"center\" : \"space-between\",\n                    borderBottom: `1px solid ${colors.border}`,\n                    minHeight: 80\n                },\n                children: [\n                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                sx: {\n                                    fontSize: 32,\n                                    color: colors.accent,\n                                    mr: 2,\n                                    filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"h6\",\n                                        sx: {\n                                            color: colors.text,\n                                            fontWeight: 700,\n                                            fontSize: \"1.25rem\",\n                                            letterSpacing: \"-0.025em\"\n                                        },\n                                        children: \"BahinLink\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"caption\",\n                                        sx: {\n                                            color: colors.textSecondary,\n                                            fontSize: \"0.75rem\",\n                                            fontWeight: 500\n                                        },\n                                        children: \"Security Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, undefined),\n                    isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        sx: {\n                            fontSize: 28,\n                            color: colors.accent,\n                            filter: \"drop-shadow(0 2px 4px rgba(99, 102, 241, 0.3))\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        onClick: toggleSidebar,\n                        sx: {\n                            color: colors.textSecondary,\n                            backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                            width: 32,\n                            height: 32,\n                            \"&:hover\": {\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                color: colors.text\n                            },\n                            transition: \"all 0.2s ease-in-out\"\n                        },\n                        children: isCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 225,\n                            columnNumber: 26\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {}, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 225,\n                            columnNumber: 45\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 211,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 3,\n                    py: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                    label: \"LIVE\",\n                    size: \"small\",\n                    sx: {\n                        backgroundColor: colors.success,\n                        color: \"white\",\n                        fontWeight: 600,\n                        fontSize: \"0.75rem\",\n                        height: 24,\n                        \"& .MuiChip-label\": {\n                            px: 1.5\n                        },\n                        display: isCollapsed ? \"none\" : \"flex\"\n                    }\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 231,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 230,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    flex: 1,\n                    px: 2\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                    sx: {\n                        py: 0\n                    },\n                    children: navigationItems.map((item)=>{\n                        const Icon = item.icon;\n                        const active = isActive(item.path);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                            disablePadding: true,\n                            sx: {\n                                mb: 0.5\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                title: isCollapsed ? `${item.label} - ${item.description}` : \"\",\n                                placement: \"right\",\n                                arrow: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                    onClick: ()=>handleNavigation(item.path),\n                                    sx: {\n                                        borderRadius: 2,\n                                        mx: 1,\n                                        px: 2,\n                                        py: 1.5,\n                                        minHeight: 48,\n                                        backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                        border: active ? `1px solid ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.3)}` : \"1px solid transparent\",\n                                        \"&:hover\": {\n                                            backgroundColor: active ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2) : (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                            transform: \"translateX(2px)\"\n                                        },\n                                        transition: \"all 0.2s ease-in-out\",\n                                        justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            sx: {\n                                                color: active ? colors.accent : colors.textSecondary,\n                                                minWidth: isCollapsed ? \"auto\" : 40,\n                                                mr: isCollapsed ? 0 : 1.5,\n                                                transition: \"color 0.2s ease-in-out\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                sx: {\n                                                    fontSize: 22\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                lineNumber: 290,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 282,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                            primary: item.label,\n                                            secondary: item.description,\n                                            primaryTypographyProps: {\n                                                sx: {\n                                                    color: active ? colors.text : colors.textSecondary,\n                                                    fontWeight: active ? 600 : 500,\n                                                    fontSize: \"0.875rem\",\n                                                    transition: \"color 0.2s ease-in-out\"\n                                                }\n                                            },\n                                            secondaryTypographyProps: {\n                                                sx: {\n                                                    color: colors.textMuted,\n                                                    fontSize: \"0.75rem\",\n                                                    mt: 0.25\n                                                }\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 294,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 262,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 257,\n                                columnNumber: 17\n                            }, undefined)\n                        }, item.path, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 256,\n                            columnNumber: 15\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 1\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                        sx: {\n                            borderColor: colors.border,\n                            mb: 2\n                        }\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 324,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        disablePadding: true,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: isCollapsed ? \"Settings\" : \"\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                onClick: ()=>handleNavigation(\"/settings\"),\n                                sx: {\n                                    borderRadius: 2,\n                                    mx: 1,\n                                    px: 2,\n                                    py: 1.5,\n                                    minHeight: 48,\n                                    backgroundColor: pathname === \"/settings\" ? (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.15) : \"transparent\",\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.8),\n                                        transform: \"translateX(2px)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\",\n                                    justifyContent: isCollapsed ? \"center\" : \"flex-start\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                        sx: {\n                                            color: pathname === \"/settings\" ? colors.accent : colors.textSecondary,\n                                            minWidth: isCollapsed ? \"auto\" : 40,\n                                            mr: isCollapsed ? 0 : 1.5\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            sx: {\n                                                fontSize: 22\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 352,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 345,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                        primary: \"Settings\",\n                                        primaryTypographyProps: {\n                                            sx: {\n                                                color: pathname === \"/settings\" ? colors.text : colors.textSecondary,\n                                                fontWeight: pathname === \"/settings\" ? 600 : 500,\n                                                fontSize: \"0.875rem\"\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                        lineNumber: 356,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 327,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                        lineNumber: 326,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 323,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                sx: {\n                    px: 2,\n                    pb: 3,\n                    borderTop: `1px solid ${colors.border}`,\n                    pt: 2\n                },\n                children: !isCollapsed ? // Expanded Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    sx: {\n                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.sidebarHover, 0.5),\n                        borderRadius: 3,\n                        p: 2,\n                        mx: 1,\n                        border: `1px solid ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.border, 0.5)}`\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            mb: 2,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    src: user?.imageUrl,\n                                    alt: user?.fullName,\n                                    sx: {\n                                        width: 40,\n                                        height: 40,\n                                        mr: 2,\n                                        border: `2px solid ${colors.accent}`,\n                                        boxShadow: `0 0 0 2px ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)}`\n                                    },\n                                    children: user?.firstName?.charAt(0)\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    sx: {\n                                        flex: 1,\n                                        minWidth: 0\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"body2\",\n                                            sx: {\n                                                color: colors.text,\n                                                fontWeight: 600,\n                                                fontSize: \"0.875rem\",\n                                                lineHeight: 1.2,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: [\n                                                user?.firstName,\n                                                \" \",\n                                                user?.lastName\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 408,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            mt: 0.5,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                    sx: {\n                                                        fontSize: 14,\n                                                        color: colors.accent,\n                                                        mr: 0.5\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                    variant: \"caption\",\n                                                    sx: {\n                                                        color: colors.textSecondary,\n                                                        fontSize: \"0.75rem\",\n                                                        fontWeight: 500\n                                                    },\n                                                    children: \"Administrator\"\n                                                }, void 0, false, {\n                                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            variant: \"caption\",\n                                            sx: {\n                                                color: colors.textMuted,\n                                                fontSize: \"0.7rem\",\n                                                display: \"block\",\n                                                mt: 0.25,\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\"\n                                            },\n                                            children: user?.primaryEmailAddress?.emailAddress\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 407,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 392,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            onClick: handleSignOut,\n                            sx: {\n                                width: \"100%\",\n                                backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                color: colors.error,\n                                borderRadius: 2,\n                                py: 1,\n                                \"&:hover\": {\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                    transform: \"translateY(-1px)\"\n                                },\n                                transition: \"all 0.2s ease-in-out\"\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18,\n                                        mr: 1\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 475,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    variant: \"caption\",\n                                    sx: {\n                                        fontWeight: 600\n                                    },\n                                    children: \"Sign Out\"\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 476,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 460,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 383,\n                    columnNumber: 11\n                }, undefined) : // Collapsed Profile View\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    alignItems: \"center\",\n                    gap: 1,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: `${user?.fullName} - Administrator`,\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                src: user?.imageUrl,\n                                alt: user?.fullName,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    border: `2px solid ${colors.accent}`,\n                                    boxShadow: `0 0 0 2px ${(0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.accent, 0.2)}`,\n                                    cursor: \"pointer\"\n                                },\n                                children: user?.firstName?.charAt(0)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 485,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                            title: \"Sign Out\",\n                            placement: \"right\",\n                            arrow: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                onClick: handleSignOut,\n                                sx: {\n                                    width: 36,\n                                    height: 36,\n                                    backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.1),\n                                    color: colors.error,\n                                    \"&:hover\": {\n                                        backgroundColor: (0,_barrel_optimize_names_Avatar_Box_Chip_Divider_Drawer_IconButton_List_ListItem_ListItemButton_ListItemIcon_ListItemText_Tooltip_Typography_alpha_useTheme_mui_material__WEBPACK_IMPORTED_MODULE_17__.alpha)(colors.error, 0.2),\n                                        transform: \"scale(1.05)\"\n                                    },\n                                    transition: \"all 0.2s ease-in-out\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AccountCircle_AdminPanelSettings_Analytics_Assessment_Assignment_ChevronLeft_ChevronRight_Dashboard_ExitToApp_LocationOn_People_Settings_Shield_mui_icons_material__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                                    sx: {\n                                        fontSize: 18\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                    lineNumber: 515,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                                lineNumber: 501,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                            lineNumber: 500,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                    lineNumber: 483,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n                lineNumber: 373,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/components/ModernSidebar.js\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ModernSidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ModernSidebar.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8d498114e33b\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzRjMjMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI4ZDQ5ODExNGUzM2JcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../../node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.js\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @clerk/nextjs */ \"(rsc)/../../node_modules/@clerk/nextjs/dist/esm/index.js\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n// BahinLink Web Admin Layout\n// ⚠️ CRITICAL: Real Clerk authentication and production data integration ONLY\n\n\n\n\n// Real Clerk publishable key - MUST be production key\nconst CLERK_PUBLISHABLE_KEY = \"pk_test_YWxlcnQtY2F0ZmlzaC00Ny5jbGVyay5hY2NvdW50cy5kZXYk\";\nconst metadata = {\n    title: \"BahinLink Admin - Security Workforce Management\",\n    description: \"Real-time security workforce management for Bahin SARL\",\n    keywords: \"security, workforce, management, GPS tracking, Senegal, Bahin SARL\",\n    authors: [\n        {\n            name: \"Bahin SARL\"\n        }\n    ],\n    viewport: \"width=device-width, initial-scale=1\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clerk_nextjs__WEBPACK_IMPORTED_MODULE_2__.ClerkProvider, {\n        publishableKey: CLERK_PUBLISHABLE_KEY,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n            lang: \"en\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"viewport\",\n                            content: \"width=device-width, initial-scale=1\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            href: \"/favicon.ico\"\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: (next_font_google_target_css_path_src_app_layout_js_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/layout.js\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/users/page.js":
/*!*******************************!*\
  !*** ./src/app/users/page.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/workspaces/finalagent-main-final/packages/web-admin/src/app/users/page.js#default`));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL3VzZXJzL3BhZ2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBNEM7QUFDNUMsbU5BQWdFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGJhaGlubGluay93ZWItYWRtaW4vLi9zcmMvYXBwL3VzZXJzL3BhZ2UuanM/YTQ5MSJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBCYWhpbkxpbmsgV2ViIEFkbWluIFVzZXJzIE1hbmFnZW1lbnQgUGFnZVxuLy8g4pqg77iPIENSSVRJQ0FMOiBSZWFsIHVzZXIgbWFuYWdlbWVudCB3aXRoIHJvbGUtYmFzZWQgYWNjZXNzIE9OTFlcblxuJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7XG4gIEJveCxcbiAgVHlwb2dyYXBoeSxcbiAgQnV0dG9uLFxuICBUYWJsZSxcbiAgVGFibGVCb2R5LFxuICBUYWJsZUNlbGwsXG4gIFRhYmxlQ29udGFpbmVyLFxuICBUYWJsZUhlYWQsXG4gIFRhYmxlUm93LFxuICBDaGlwLFxuICBJY29uQnV0dG9uLFxuICBBbGVydCxcbiAgQ2lyY3VsYXJQcm9ncmVzcyxcbiAgQXZhdGFyXG59IGZyb20gJ0BtdWkvbWF0ZXJpYWwnO1xuaW1wb3J0IHtcbiAgQWRkIGFzIEFkZEljb24sXG4gIFJlZnJlc2ggYXMgUmVmcmVzaEljb24sXG4gIERvd25sb2FkIGFzIERvd25sb2FkSWNvbixcbiAgRWRpdCBhcyBFZGl0SWNvbixcbiAgUGVvcGxlIGFzIFBlb3BsZUljb24sXG4gIEFkbWluUGFuZWxTZXR0aW5ncyBhcyBBZG1pbkljb24sXG4gIFNlY3VyaXR5IGFzIFNlY3VyaXR5SWNvbixcbiAgRW1haWwsXG4gIFBob25lXG59IGZyb20gJ0BtdWkvaWNvbnMtbWF0ZXJpYWwnO1xuaW1wb3J0IE1vZGVyblNpZGViYXIgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9Nb2Rlcm5TaWRlYmFyJztcblxuY29uc3QgVXNlcnNQYWdlID0gKCkgPT4ge1xuICBjb25zdCBbdXNlcnMsIHNldFVzZXJzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFthbmNob3JFbCwgc2V0QW5jaG9yRWxdID0gdXNlU3RhdGUobnVsbCk7XG4gIGNvbnN0IFtzZWxlY3RlZFVzZXIsIHNldFNlbGVjdGVkVXNlcl0gPSB1c2VTdGF0ZShudWxsKTtcbiAgY29uc3QgW2NyZWF0ZURpYWxvZ09wZW4sIHNldENyZWF0ZURpYWxvZ09wZW5dID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbZWRpdERpYWxvZ09wZW4sIHNldEVkaXREaWFsb2dPcGVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2ZpbHRlcnMsIHNldEZpbHRlcnNdID0gdXNlU3RhdGUoe1xuICAgIHJvbGU6ICcnLFxuICAgIHN0YXR1czogJycsXG4gICAgc2VhcmNoOiAnJ1xuICB9KTtcbiAgY29uc3QgW3N0YXRzLCBzZXRTdGF0c10gPSB1c2VTdGF0ZSh7XG4gICAgdG90YWw6IDAsXG4gICAgYWRtaW5zOiAwLFxuICAgIHN1cGVydmlzb3JzOiAwLFxuICAgIGFnZW50czogMCxcbiAgICBjbGllbnRzOiAwLFxuICAgIGFjdGl2ZTogMCxcbiAgICBpbmFjdGl2ZTogMFxuICB9KTtcbiAgY29uc3QgW25ld1VzZXIsIHNldE5ld1VzZXJdID0gdXNlU3RhdGUoe1xuICAgIGZpcnN0TmFtZTogJycsXG4gICAgbGFzdE5hbWU6ICcnLFxuICAgIGVtYWlsOiAnJyxcbiAgICBwaG9uZTogJycsXG4gICAgcm9sZTogJ0FHRU5UJyxcbiAgICBpc0FjdGl2ZTogdHJ1ZVxuICB9KTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWRVc2VycygpO1xuICB9LCBbZmlsdGVyc10pO1xuXG4gIGNvbnN0IGxvYWRVc2VycyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0TG9hZGluZyh0cnVlKTtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3VzZXJzJyk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yISBzdGF0dXM6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgc2V0VXNlcnMoZGF0YS51c2VycyB8fCBbXSk7XG5cbiAgICAgIC8vIENhbGN1bGF0ZSBzdGF0c1xuICAgICAgY29uc3QgdXNlcnNEYXRhID0gZGF0YS51c2VycyB8fCBbXTtcbiAgICAgIGNvbnN0IG5ld1N0YXRzID0ge1xuICAgICAgICB0b3RhbDogdXNlcnNEYXRhLmxlbmd0aCxcbiAgICAgICAgYWRtaW5zOiB1c2Vyc0RhdGEuZmlsdGVyKHUgPT4gdS5yb2xlID09PSAnQURNSU4nKS5sZW5ndGgsXG4gICAgICAgIHN1cGVydmlzb3JzOiB1c2Vyc0RhdGEuZmlsdGVyKHUgPT4gdS5yb2xlID09PSAnU1VQRVJWSVNPUicpLmxlbmd0aCxcbiAgICAgICAgYWdlbnRzOiB1c2Vyc0RhdGEuZmlsdGVyKHUgPT4gdS5yb2xlID09PSAnQUdFTlQnKS5sZW5ndGgsXG4gICAgICAgIGNsaWVudHM6IHVzZXJzRGF0YS5maWx0ZXIodSA9PiB1LnJvbGUgPT09ICdDTElFTlQnKS5sZW5ndGgsXG4gICAgICAgIGFjdGl2ZTogdXNlcnNEYXRhLmZpbHRlcih1ID0+IHUuaXNBY3RpdmUpLmxlbmd0aCxcbiAgICAgICAgaW5hY3RpdmU6IHVzZXJzRGF0YS5maWx0ZXIodSA9PiAhdS5pc0FjdGl2ZSkubGVuZ3RoXG4gICAgICB9O1xuICAgICAgc2V0U3RhdHMobmV3U3RhdHMpO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGxvYWRpbmcgdXNlcnM6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHVzZXJzJyk7XG4gICAgICAvLyBTZXQgZmFsbGJhY2sgZGF0YVxuICAgICAgc2V0VXNlcnMoW10pO1xuICAgICAgc2V0U3RhdHMoe1xuICAgICAgICB0b3RhbDogMCxcbiAgICAgICAgYWRtaW5zOiAwLFxuICAgICAgICBzdXBlcnZpc29yczogMCxcbiAgICAgICAgYWdlbnRzOiAwLFxuICAgICAgICBjbGllbnRzOiAwLFxuICAgICAgICBhY3RpdmU6IDAsXG4gICAgICAgIGluYWN0aXZlOiAwXG4gICAgICB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU1lbnVPcGVuID0gKGV2ZW50LCB1c2VyKSA9PiB7XG4gICAgc2V0QW5jaG9yRWwoZXZlbnQuY3VycmVudFRhcmdldCk7XG4gICAgc2V0U2VsZWN0ZWRVc2VyKHVzZXIpO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU1lbnVDbG9zZSA9ICgpID0+IHtcbiAgICBzZXRBbmNob3JFbChudWxsKTtcbiAgICBzZXRTZWxlY3RlZFVzZXIobnVsbCk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlQ3JlYXRlVXNlciA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBcGlTZXJ2aWNlLnBvc3QoJy91c2VycycsIG5ld1VzZXIpO1xuICAgICAgXG4gICAgICBpZiAocmVzcG9uc2Uuc3VjY2Vzcykge1xuICAgICAgICBzZXRDcmVhdGVEaWFsb2dPcGVuKGZhbHNlKTtcbiAgICAgICAgc2V0TmV3VXNlcih7XG4gICAgICAgICAgZmlyc3ROYW1lOiAnJyxcbiAgICAgICAgICBsYXN0TmFtZTogJycsXG4gICAgICAgICAgZW1haWw6ICcnLFxuICAgICAgICAgIHBob25lOiAnJyxcbiAgICAgICAgICByb2xlOiAnQUdFTlQnLFxuICAgICAgICAgIGlzQWN0aXZlOiB0cnVlXG4gICAgICAgIH0pO1xuICAgICAgICBsb2FkVXNlcnMoKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgY3JlYXRpbmcgdXNlcjonLCBlcnJvcik7XG4gICAgICBzZXRFcnJvcignRmFpbGVkIHRvIGNyZWF0ZSB1c2VyJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUVkaXRVc2VyID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IEFwaVNlcnZpY2UucHV0KGAvdXNlcnMvJHtzZWxlY3RlZFVzZXIuaWR9YCwgc2VsZWN0ZWRVc2VyKTtcbiAgICAgIFxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgc2V0RWRpdERpYWxvZ09wZW4oZmFsc2UpO1xuICAgICAgICBzZXRTZWxlY3RlZFVzZXIobnVsbCk7XG4gICAgICAgIGxvYWRVc2VycygpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyB1c2VyOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIHVzZXInKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlVG9nZ2xlU3RhdHVzID0gYXN5bmMgKHVzZXJJZCwgaXNBY3RpdmUpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBcGlTZXJ2aWNlLnB1dChgL3VzZXJzLyR7dXNlcklkfWAsIHsgaXNBY3RpdmU6ICFpc0FjdGl2ZSB9KTtcbiAgICAgIFxuICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgbG9hZFVzZXJzKCk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIHVzZXIgc3RhdHVzOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gdXBkYXRlIHVzZXIgc3RhdHVzJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZURlbGV0ZVVzZXIgPSBhc3luYyAodXNlcklkKSA9PiB7XG4gICAgaWYgKHdpbmRvdy5jb25maXJtKCdBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlIHRoaXMgdXNlcj8nKSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBBcGlTZXJ2aWNlLmRlbGV0ZShgL3VzZXJzLyR7dXNlcklkfWApO1xuICAgICAgICBcbiAgICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcbiAgICAgICAgICBsb2FkVXNlcnMoKTtcbiAgICAgICAgfVxuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgdXNlcjonLCBlcnJvcik7XG4gICAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gZGVsZXRlIHVzZXInKTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0Um9sZUNvbG9yID0gKHJvbGUpID0+IHtcbiAgICBzd2l0Y2ggKHJvbGUpIHtcbiAgICAgIGNhc2UgJ0FETUlOJzogcmV0dXJuICdlcnJvcic7XG4gICAgICBjYXNlICdTVVBFUlZJU09SJzogcmV0dXJuICd3YXJuaW5nJztcbiAgICAgIGNhc2UgJ0FHRU5UJzogcmV0dXJuICdwcmltYXJ5JztcbiAgICAgIGNhc2UgJ0NMSUVOVCc6IHJldHVybiAnaW5mbyc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ2RlZmF1bHQnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRSb2xlSWNvbiA9IChyb2xlKSA9PiB7XG4gICAgc3dpdGNoIChyb2xlKSB7XG4gICAgICBjYXNlICdBRE1JTic6IHJldHVybiA8QWRtaW5JY29uIGZvbnRTaXplPVwic21hbGxcIiAvPjtcbiAgICAgIGNhc2UgJ1NVUEVSVklTT1InOiByZXR1cm4gPFNlY3VyaXR5SWNvbiBmb250U2l6ZT1cInNtYWxsXCIgLz47XG4gICAgICBjYXNlICdBR0VOVCc6IHJldHVybiA8UGVvcGxlSWNvbiBmb250U2l6ZT1cInNtYWxsXCIgLz47XG4gICAgICBjYXNlICdDTElFTlQnOiByZXR1cm4gPEJ1c2luZXNzSWNvbiBmb250U2l6ZT1cInNtYWxsXCIgLz47XG4gICAgICBkZWZhdWx0OiByZXR1cm4gPFBlb3BsZUljb24gZm9udFNpemU9XCJzbWFsbFwiIC8+O1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRJbml0aWFscyA9IChmaXJzdE5hbWUsIGxhc3ROYW1lKSA9PiB7XG4gICAgcmV0dXJuIGAke2ZpcnN0TmFtZT8uWzBdIHx8ICcnfSR7bGFzdE5hbWU/LlswXSB8fCAnJ31gLnRvVXBwZXJDYXNlKCk7XG4gIH07XG5cbiAgY29uc3QgU3RhdENhcmQgPSAoeyB0aXRsZSwgdmFsdWUsIGljb246IEljb24sIGNvbG9yID0gJ3ByaW1hcnknIH0pID0+IChcbiAgICA8Q2FyZD5cbiAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGFsaWduSXRlbXM6ICdjZW50ZXInLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nIH19PlxuICAgICAgICAgIDxCb3g+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDRcIiBjb21wb25lbnQ9XCJkaXZcIiBjb2xvcj17YCR7Y29sb3J9Lm1haW5gfT5cbiAgICAgICAgICAgICAge3ZhbHVlfVxuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgY29sb3I9XCJ0ZXh0U2Vjb25kYXJ5XCI+XG4gICAgICAgICAgICAgIHt0aXRsZX1cbiAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICA8L0JveD5cbiAgICAgICAgICA8SWNvbiBzeD17eyBmb250U2l6ZTogNDAsIGNvbG9yOiBgJHtjb2xvcn0ubWFpbmAsIG9wYWNpdHk6IDAuNyB9fSAvPlxuICAgICAgICA8L0JveD5cbiAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgPC9DYXJkPlxuICApO1xuXG4gIGNvbnN0IGhhbmRsZUV4cG9ydCA9ICgpID0+IHtcbiAgICBpZiAodXNlcnMubGVuZ3RoID4gMCkge1xuICAgICAgY29uc3QgZGF0YVN0ciA9IEpTT04uc3RyaW5naWZ5KHVzZXJzLCBudWxsLCAyKTtcbiAgICAgIGNvbnN0IGRhdGFVcmkgPSAnZGF0YTphcHBsaWNhdGlvbi9qc29uO2NoYXJzZXQ9dXRmLTgsJysgZW5jb2RlVVJJQ29tcG9uZW50KGRhdGFTdHIpO1xuXG4gICAgICBjb25zdCBleHBvcnRGaWxlRGVmYXVsdE5hbWUgPSBgdXNlcnMtJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXX0uanNvbmA7XG5cbiAgICAgIGNvbnN0IGxpbmtFbGVtZW50ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpO1xuICAgICAgbGlua0VsZW1lbnQuc2V0QXR0cmlidXRlKCdocmVmJywgZGF0YVVyaSk7XG4gICAgICBsaW5rRWxlbWVudC5zZXRBdHRyaWJ1dGUoJ2Rvd25sb2FkJywgZXhwb3J0RmlsZURlZmF1bHROYW1lKTtcbiAgICAgIGxpbmtFbGVtZW50LmNsaWNrKCk7XG4gICAgfVxuICB9O1xuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxCb3ggc3g9e3tcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgICAgICBtaW5IZWlnaHQ6ICcxMDB2aCdcbiAgICAgIH19PlxuICAgICAgICA8Q2lyY3VsYXJQcm9ncmVzcyBzaXplPXs0MH0gc3g9e3sgY29sb3I6ICcjNmI3MjgwJyB9fSAvPlxuICAgICAgPC9Cb3g+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIG1pbkhlaWdodDogJzEwMHZoJywgYmFja2dyb3VuZENvbG9yOiAnI2ZmZmZmZicgfX0+XG4gICAgICA8TW9kZXJuU2lkZWJhciAvPlxuXG4gICAgICA8Qm94IGNvbXBvbmVudD1cIm1haW5cIiBzeD17eyBmbGV4R3JvdzogMSwgcDogNCB9fT5cbiAgICAgICAgey8qIENsZWFuIEhlYWRlciAqL31cbiAgICAgICAgPEJveCBzeD17eyBtYjogNiwgZGlzcGxheTogJ2ZsZXgnLCBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLCBhbGlnbkl0ZW1zOiAnZmxleC1zdGFydCcgfX0+XG4gICAgICAgICAgPEJveD5cbiAgICAgICAgICAgIDxUeXBvZ3JhcGh5XG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJoM1wiXG4gICAgICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogMzAwLFxuICAgICAgICAgICAgICAgIGNvbG9yOiAnIzFhMWExYScsXG4gICAgICAgICAgICAgICAgbWI6IDEsXG4gICAgICAgICAgICAgICAgbGV0dGVyU3BhY2luZzogJy0wLjAyZW0nLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMi41cmVtJ1xuICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICBVc2Vyc1xuICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPFR5cG9ncmFwaHlcbiAgICAgICAgICAgICAgdmFyaWFudD1cImJvZHkxXCJcbiAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICBjb2xvcjogJyM2YjcyODAnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTZweCdcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgTWFuYWdlIHVzZXIgYWNjb3VudHMgYW5kIHBlcm1pc3Npb25zXG4gICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgPC9Cb3g+XG5cbiAgICAgICAgICB7LyogQ2xlYW4gQ29udHJvbHMgKi99XG4gICAgICAgICAgPEJveCBzeD17eyBkaXNwbGF5OiAnZmxleCcsIGdhcDogMiwgYWxpZ25JdGVtczogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXG4gICAgICAgICAgICAgIHN0YXJ0SWNvbj17PERvd25sb2FkSWNvbiAvPn1cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlRXhwb3J0fVxuICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2U1ZTdlYicsXG4gICAgICAgICAgICAgICAgY29sb3I6ICcjNmI3MjgwJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE0cHgnLFxuICAgICAgICAgICAgICAgIHRleHRUcmFuc2Zvcm06ICdub25lJyxcbiAgICAgICAgICAgICAgICAnJjpob3Zlcic6IHtcbiAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2QxZDVkYicsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjlmYWZiJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgRXhwb3J0XG4gICAgICAgICAgICA8L0J1dHRvbj5cblxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxuICAgICAgICAgICAgICBzdGFydEljb249ezxSZWZyZXNoSWNvbiAvPn1cbiAgICAgICAgICAgICAgb25DbGljaz17bG9hZFVzZXJzfVxuICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2U1ZTdlYicsXG4gICAgICAgICAgICAgICAgY29sb3I6ICcjNmI3MjgwJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE0cHgnLFxuICAgICAgICAgICAgICAgIHRleHRUcmFuc2Zvcm06ICdub25lJyxcbiAgICAgICAgICAgICAgICAnJjpob3Zlcic6IHtcbiAgICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2QxZDVkYicsXG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjZjlmYWZiJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgUmVmcmVzaFxuICAgICAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgdmFyaWFudD1cImNvbnRhaW5lZFwiXG4gICAgICAgICAgICAgIHN0YXJ0SWNvbj17PEFkZEljb24gLz59XG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldENyZWF0ZURpYWxvZ09wZW4odHJ1ZSl9XG4gICAgICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnIzNiODJmNicsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxNHB4JyxcbiAgICAgICAgICAgICAgICB0ZXh0VHJhbnNmb3JtOiAnbm9uZScsXG4gICAgICAgICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMjU2M2ViJ1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgQWRkIFVzZXJcbiAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDwvQm94PlxuICAgICAgICA8L0JveD5cblxuICAgICAgICB7LyogRXJyb3IgQWxlcnQgKi99XG4gICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgPEFsZXJ0IHNldmVyaXR5PVwiZXJyb3JcIiBzeD17eyBtYjogNCB9fT5cbiAgICAgICAgICAgIHtlcnJvcn1cbiAgICAgICAgICA8L0FsZXJ0PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBNaW5pbWFsaXN0IEtQSSBHcmlkICovfVxuICAgICAgICA8Qm94IHN4PXt7IG1iOiA4IH19PlxuICAgICAgICAgIDxCb3hcbiAgICAgICAgICAgIHN4PXt7XG4gICAgICAgICAgICAgIGRpc3BsYXk6ICdncmlkJyxcbiAgICAgICAgICAgICAgZ3JpZFRlbXBsYXRlQ29sdW1uczogJ3JlcGVhdChhdXRvLWZpdCwgbWlubWF4KDIwMHB4LCAxZnIpKScsXG4gICAgICAgICAgICAgIGdhcDogNCxcbiAgICAgICAgICAgICAgbWI6IDZcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgPlxuICAgICAgICAgICAgey8qIFRvdGFsIFVzZXJzICovfVxuICAgICAgICAgICAgPEJveCBzeD17e1xuICAgICAgICAgICAgICBwOiA0LFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDMsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjZjFmNWY5JyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2ZmZmZmZicsXG4gICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4zcyBlYXNlJyxcbiAgICAgICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjZTJlOGYwJyxcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVZKC0ycHgpJyxcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDhweCAyNXB4IHJnYmEoMCwwLDAsMC4wOCknXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDFcIiBzeD17e1xuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDcwMCxcbiAgICAgICAgICAgICAgICBjb2xvcjogJyMxYTFhMWEnLFxuICAgICAgICAgICAgICAgIG1iOiAxLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnM3JlbScsXG4gICAgICAgICAgICAgICAgbGluZUhlaWdodDogMVxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7c3RhdHMudG90YWx9XG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkxXCIgc3g9e3tcbiAgICAgICAgICAgICAgICBjb2xvcjogJyM2YjcyODAnLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTVweCcsXG4gICAgICAgICAgICAgICAgZm9udFdlaWdodDogNTAwXG4gICAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICAgIFRvdGFsIFVzZXJzXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDwvQm94PlxuXG4gICAgICAgICAgICB7LyogQWRtaW5zICovfVxuICAgICAgICAgICAgPEJveCBzeD17e1xuICAgICAgICAgICAgICBwOiA0LFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDMsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjZjFmNWY5JyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2ZmZmZmZicsXG4gICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4zcyBlYXNlJyxcbiAgICAgICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjZTJlOGYwJyxcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVZKC0ycHgpJyxcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDhweCAyNXB4IHJnYmEoMCwwLDAsMC4wOCknXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDFcIiBzeD17e1xuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDcwMCxcbiAgICAgICAgICAgICAgICBjb2xvcjogJyNlZjQ0NDQnLFxuICAgICAgICAgICAgICAgIG1iOiAxLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnM3JlbScsXG4gICAgICAgICAgICAgICAgbGluZUhlaWdodDogMVxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7c3RhdHMuYWRtaW5zfVxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MVwiIHN4PXt7XG4gICAgICAgICAgICAgICAgY29sb3I6ICcjNmI3MjgwJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDUwMFxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICBBZG1pbnNcbiAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPC9Cb3g+XG5cbiAgICAgICAgICAgIHsvKiBTdXBlcnZpc29ycyAqL31cbiAgICAgICAgICAgIDxCb3ggc3g9e3tcbiAgICAgICAgICAgICAgcDogNCxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAzLFxuICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2YxZjVmOScsXG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmZmZmZmYnLFxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuM3MgZWFzZScsXG4gICAgICAgICAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2UyZThmMCcsXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWSgtMnB4KScsXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OiAnMCA4cHggMjVweCByZ2JhKDAsMCwwLDAuMDgpJ1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImgxXCIgc3g9e3tcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA3MDAsXG4gICAgICAgICAgICAgICAgY29sb3I6ICcjZjU5ZTBiJyxcbiAgICAgICAgICAgICAgICBtYjogMSxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzNyZW0nLFxuICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDFcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAge3N0YXRzLnN1cGVydmlzb3JzfVxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MVwiIHN4PXt7XG4gICAgICAgICAgICAgICAgY29sb3I6ICcjNmI3MjgwJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDUwMFxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICBTdXBlcnZpc29yc1xuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICA8L0JveD5cblxuICAgICAgICAgICAgey8qIEFnZW50cyAqL31cbiAgICAgICAgICAgIDxCb3ggc3g9e3tcbiAgICAgICAgICAgICAgcDogNCxcbiAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAzLFxuICAgICAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2YxZjVmOScsXG4gICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogJyNmZmZmZmYnLFxuICAgICAgICAgICAgICB0cmFuc2l0aW9uOiAnYWxsIDAuM3MgZWFzZScsXG4gICAgICAgICAgICAgICcmOmhvdmVyJzoge1xuICAgICAgICAgICAgICAgIGJvcmRlckNvbG9yOiAnI2UyZThmMCcsXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWSgtMnB4KScsXG4gICAgICAgICAgICAgICAgYm94U2hhZG93OiAnMCA4cHggMjVweCByZ2JhKDAsMCwwLDAuMDgpJ1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImgxXCIgc3g9e3tcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA3MDAsXG4gICAgICAgICAgICAgICAgY29sb3I6ICcjM2I4MmY2JyxcbiAgICAgICAgICAgICAgICBtYjogMSxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzNyZW0nLFxuICAgICAgICAgICAgICAgIGxpbmVIZWlnaHQ6IDFcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAge3N0YXRzLmFnZW50c31cbiAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTFcIiBzeD17e1xuICAgICAgICAgICAgICAgIGNvbG9yOiAnIzZiNzI4MCcsXG4gICAgICAgICAgICAgICAgZm9udFNpemU6ICcxNXB4JyxcbiAgICAgICAgICAgICAgICBmb250V2VpZ2h0OiA1MDBcbiAgICAgICAgICAgICAgfX0+XG4gICAgICAgICAgICAgICAgQWdlbnRzXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDwvQm94PlxuXG4gICAgICAgICAgICB7LyogQWN0aXZlIFVzZXJzICovfVxuICAgICAgICAgICAgPEJveCBzeD17e1xuICAgICAgICAgICAgICBwOiA0LFxuICAgICAgICAgICAgICBib3JkZXJSYWRpdXM6IDMsXG4gICAgICAgICAgICAgIGJvcmRlcjogJzFweCBzb2xpZCAjZjFmNWY5JyxcbiAgICAgICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2ZmZmZmZicsXG4gICAgICAgICAgICAgIHRyYW5zaXRpb246ICdhbGwgMC4zcyBlYXNlJyxcbiAgICAgICAgICAgICAgJyY6aG92ZXInOiB7XG4gICAgICAgICAgICAgICAgYm9yZGVyQ29sb3I6ICcjZTJlOGYwJyxcbiAgICAgICAgICAgICAgICB0cmFuc2Zvcm06ICd0cmFuc2xhdGVZKC0ycHgpJyxcbiAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICcwIDhweCAyNXB4IHJnYmEoMCwwLDAsMC4wOCknXG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH19PlxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiaDFcIiBzeD17e1xuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDcwMCxcbiAgICAgICAgICAgICAgICBjb2xvcjogJyMxMGI5ODEnLFxuICAgICAgICAgICAgICAgIG1iOiAxLFxuICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnM3JlbScsXG4gICAgICAgICAgICAgICAgbGluZUhlaWdodDogMVxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICB7c3RhdHMuYWN0aXZlfVxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MVwiIHN4PXt7XG4gICAgICAgICAgICAgICAgY29sb3I6ICcjNmI3MjgwJyxcbiAgICAgICAgICAgICAgICBmb250U2l6ZTogJzE1cHgnLFxuICAgICAgICAgICAgICAgIGZvbnRXZWlnaHQ6IDUwMFxuICAgICAgICAgICAgICB9fT5cbiAgICAgICAgICAgICAgICBBY3RpdmUgVXNlcnNcbiAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgPC9Cb3g+XG4gICAgICAgIDwvQm94PlxuXG4gICAgICAgIHsvKiBDbGVhbiBVc2VycyBUYWJsZSAqL31cbiAgICAgICAgPEJveCBzeD17e1xuICAgICAgICAgIGJvcmRlclJhZGl1czogMyxcbiAgICAgICAgICBib3JkZXI6ICcxcHggc29saWQgI2YxZjVmOScsXG4gICAgICAgICAgYmFja2dyb3VuZENvbG9yOiAnI2ZmZmZmZicsXG4gICAgICAgICAgb3ZlcmZsb3c6ICdoaWRkZW4nXG4gICAgICAgIH19PlxuICAgICAgICAgIDxUYWJsZUNvbnRhaW5lcj5cbiAgICAgICAgICAgIDxUYWJsZT5cbiAgICAgICAgICAgICAgPFRhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICA8VGFibGVSb3cgc3g9e3sgYmFja2dyb3VuZENvbG9yOiAnI2Y4ZmFmYycgfX0+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCwgY29sb3I6ICcjMzc0MTUxJywgZm9udFNpemU6ICcxNHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgVXNlclxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCwgY29sb3I6ICcjMzc0MTUxJywgZm9udFNpemU6ICcxNHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgQ29udGFjdFxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCwgY29sb3I6ICcjMzc0MTUxJywgZm9udFNpemU6ICcxNHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgUm9sZVxuICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIHN4PXt7IGZvbnRXZWlnaHQ6IDYwMCwgY29sb3I6ICcjMzc0MTUxJywgZm9udFNpemU6ICcxNHB4JyB9fT5cbiAgICAgICAgICAgICAgICAgICAgU3RhdHVzXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgc3g9e3sgZm9udFdlaWdodDogNjAwLCBjb2xvcjogJyMzNzQxNTEnLCBmb250U2l6ZTogJzE0cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICBMYXN0IExvZ2luXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgc3g9e3sgZm9udFdlaWdodDogNjAwLCBjb2xvcjogJyMzNzQxNTEnLCBmb250U2l6ZTogJzE0cHgnIH19PlxuICAgICAgICAgICAgICAgICAgICBBY3Rpb25zXG4gICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICA8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgICAgICB7dXNlcnMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgICAgPFRhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNvbFNwYW49ezZ9IHN4PXt7IHRleHRBbGlnbjogJ2NlbnRlcicsIHB5OiA0IH19PlxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIGNvbG9yPVwidGV4dFNlY29uZGFyeVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgTm8gdXNlcnMgZm91bmRcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgPC9UYWJsZVJvdz5cbiAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgdXNlcnMubWFwKCh1c2VyKSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvd1xuICAgICAgICAgICAgICAgICAgICAgIGtleT17dXNlci5pZH1cbiAgICAgICAgICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgICAgICAgICAgJyY6aG92ZXInOiB7IGJhY2tncm91bmRDb2xvcjogJyNmOWZhZmInIH0sXG4gICAgICAgICAgICAgICAgICAgICAgICBib3JkZXJCb3R0b206ICcxcHggc29saWQgI2YxZjVmOSdcbiAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBzeD17eyBib3JkZXJCb3R0b206ICdub25lJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCb3ggc3g9e3sgZGlzcGxheTogJ2ZsZXgnLCBhbGlnbkl0ZW1zOiAnY2VudGVyJywgZ2FwOiAyIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8QXZhdGFyIHN4PXt7IHdpZHRoOiA0MCwgaGVpZ2h0OiA0MCwgYmFja2dyb3VuZENvbG9yOiAnIzNiODJmNicgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIuZmlyc3ROYW1lPy5jaGFyQXQoMCl9e3VzZXIubGFzdE5hbWU/LmNoYXJBdCgwKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9BdmF0YXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxCb3g+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImJvZHkyXCIgc3g9e3sgZm9udFdlaWdodDogNTAwLCBjb2xvcjogJyMxYTFhMWEnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIuZmlyc3ROYW1lfSB7dXNlci5sYXN0TmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImNhcHRpb25cIiBzeD17eyBjb2xvcjogJyM2YjcyODAnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgSUQ6IHt1c2VyLmNsZXJrSWQ/LnN1YnN0cmluZygwLCA4KX0uLi5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Cb3g+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBzeD17eyBib3JkZXJCb3R0b206ICdub25lJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxCb3g+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJib2R5MlwiIHN4PXt7IGZvbnRTaXplOiAnMTRweCcsIGNvbG9yOiAnIzM3NDE1MScsIG1iOiAwLjUgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEVtYWlsIHN4PXt7IGZvbnRTaXplOiAxNiwgbXI6IDAuNSwgdmVydGljYWxBbGlnbjogJ21pZGRsZScgfX0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5lbWFpbH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17eyBmb250U2l6ZTogJzE0cHgnLCBjb2xvcjogJyMzNzQxNTEnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxQaG9uZSBzeD17eyBmb250U2l6ZTogMTYsIG1yOiAwLjUsIHZlcnRpY2FsQWxpZ246ICdtaWRkbGUnIH19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge3VzZXIucGhvbmUgfHwgJ04vQSd9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQm94PlxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgc3g9e3sgYm9yZGVyQm90dG9tOiAnbm9uZScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hpcFxuICAgICAgICAgICAgICAgICAgICAgICAgICBsYWJlbD17dXNlci5yb2xlfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBpY29uPXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VyLnJvbGUgPT09ICdBRE1JTicgPyA8QWRtaW5JY29uIHN4PXt7IGZvbnRTaXplOiAxNiB9fSAvPiA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlci5yb2xlID09PSAnU1VQRVJWSVNPUicgPyA8U2VjdXJpdHlJY29uIHN4PXt7IGZvbnRTaXplOiAxNiB9fSAvPiA6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFBlb3BsZUljb24gc3g9e3sgZm9udFNpemU6IDE2IH19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc3g9e3tcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB1c2VyLnJvbGUgPT09ICdBRE1JTicgPyAnI2VmNDQ0NCcgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlci5yb2xlID09PSAnU1VQRVJWSVNPUicgPyAnI2Y1OWUwYicgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdXNlci5yb2xlID09PSAnQUdFTlQnID8gJyMzYjgyZjYnIDogJyM2YjcyODAnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnd2hpdGUnLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvbnRTaXplOiAnMTJweCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBzeD17eyBib3JkZXJCb3R0b206ICdub25lJyB9fT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDaGlwXG4gICAgICAgICAgICAgICAgICAgICAgICAgIGxhYmVsPXt1c2VyLmlzQWN0aXZlID8gJ0FjdGl2ZScgOiAnSW5hY3RpdmUnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21hbGxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICBzeD17e1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGJhY2tncm91bmRDb2xvcjogdXNlci5pc0FjdGl2ZSA/ICcjMTBiOTgxJyA6ICcjNmI3MjgwJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb2xvcjogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBmb250U2l6ZTogJzEycHgnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgc3g9e3sgYm9yZGVyQm90dG9tOiAnbm9uZScgfX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeSB2YXJpYW50PVwiYm9keTJcIiBzeD17eyBmb250U2l6ZTogJzE0cHgnLCBjb2xvcjogJyMzNzQxNTEnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5sYXN0TG9naW5BdCA/XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3IERhdGUodXNlci5sYXN0TG9naW5BdCkudG9Mb2NhbGVEYXRlU3RyaW5nKCkgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICdOZXZlcidcbiAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxuICAgICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImNhcHRpb25cIiBzeD17eyBjb2xvcjogJyM2YjcyODAnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7dXNlci5sYXN0TG9naW5BdCA/XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbmV3IERhdGUodXNlci5sYXN0TG9naW5BdCkudG9Mb2NhbGVUaW1lU3RyaW5nKCkgOlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICcnXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIHN4PXt7IGJvcmRlckJvdHRvbTogJ25vbmUnIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgPEljb25CdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtYWxsXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0RWRpdERpYWxvZ09wZW4odHJ1ZSl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHN4PXt7IGNvbG9yOiAnIzZiNzI4MCcgfX1cbiAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPEVkaXRJY29uIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0ljb25CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICApKVxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvVGFibGVCb2R5PlxuICAgICAgICAgICAgPC9UYWJsZT5cbiAgICAgICAgICA8L1RhYmxlQ29udGFpbmVyPlxuICAgICAgICA8L0JveD5cbiAgICAgIDwvQm94PlxuICAgIDwvQm94PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgVXNlcnNQYWdlO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/users/page.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/node-fetch-native","vendor-chunks/@clerk","vendor-chunks/@peculiar","vendor-chunks/asn1js","vendor-chunks/@opentelemetry","vendor-chunks/webcrypto-core","vendor-chunks/swr","vendor-chunks/pvtsutils","vendor-chunks/tslib","vendor-chunks/pvutils","vendor-chunks/cookie","vendor-chunks/deepmerge","vendor-chunks/use-sync-external-store","vendor-chunks/map-obj","vendor-chunks/no-case","vendor-chunks/lower-case","vendor-chunks/snakecase-keys","vendor-chunks/snake-case","vendor-chunks/dot-case","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/@popperjs","vendor-chunks/react-transition-group","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/react-is","vendor-chunks/hoist-non-react-statics","vendor-chunks/@babel","vendor-chunks/object-assign","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fusers%2Fpage&page=%2Fusers%2Fpage&appPaths=%2Fusers%2Fpage&pagePath=private-next-app-dir%2Fusers%2Fpage.js&appDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fworkspaces%2Ffinalagent-main-final%2Fpackages%2Fweb-admin&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();