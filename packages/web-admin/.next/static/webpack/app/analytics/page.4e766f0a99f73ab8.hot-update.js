"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/analytics/page",{

/***/ "(app-pages-browser)/./src/app/analytics/page.js":
/*!***********************************!*\
  !*** ./src/app/analytics/page.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnalyticsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @clerk/nextjs */ \"(app-pages-browser)/../../node_modules/@clerk/nextjs/node_modules/@clerk/clerk-react/dist/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/../../node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/FormControl/FormControl.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/InputLabel/InputLabel.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Select/Select.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/MenuItem/MenuItem.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Avatar,Box,Button,Card,CardContent,Chip,CircularProgress,Divider,FormControl,Grid,IconButton,InputLabel,LinearProgress,MenuItem,Paper,Select,Tooltip,Typography!=!@mui/material */ \"(app-pages-browser)/../../node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/TrendingUp.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Download.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Refresh.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Schedule.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/People.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Assignment.js\");\n/* harmony import */ var _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Assessment,Assignment,CheckCircle,Download,Error,LocationOn,People,Refresh,Schedule,Security,Speed,Star,Timeline,TrendingDown,TrendingUp,Warning!=!@mui/icons-material */ \"(app-pages-browser)/../../node_modules/@mui/icons-material/esm/Security.js\");\n/* harmony import */ var _components_ModernSidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/ModernSidebar */ \"(app-pages-browser)/./src/components/ModernSidebar.js\");\n/* harmony import */ var _services_ApiService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../services/ApiService */ \"(app-pages-browser)/./src/services/ApiService.js\");\n// BahinLink Web Admin Analytics Dashboard Page\n// ⚠️ CRITICAL: Real analytics with live data visualization ONLY\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction AnalyticsPage() {\n    _s();\n    const { isLoaded, isSignedIn } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { user } = (0,_clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__.useUser)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"7d\");\n    const [refreshing, setRefreshing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Real analytics data from database\n    const [dashboardMetrics, setDashboardMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalAgents: 0,\n        activeShifts: 0,\n        completedShifts: 0,\n        totalSites: 0,\n        totalReports: 0,\n        averageResponseTime: 0,\n        efficiency: 0,\n        clientSatisfaction: 0\n    });\n    const [performanceData, setPerformanceData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [agentPerformance, setAgentPerformance] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [siteActivity, setSiteActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [recentAlerts, setRecentAlerts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [shiftTrends, setShiftTrends] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [kpis, setKpis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalShifts: 0,\n        completedShifts: 0,\n        totalHours: 0,\n        averageRating: 0,\n        activeAgents: 0,\n        totalReports: 0,\n        responseTime: 0,\n        efficiency: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && !isSignedIn) {\n            (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.redirect)(\"/sign-in\");\n        }\n        if (isLoaded && isSignedIn) {\n            loadAnalyticsData();\n            // Auto-refresh every 30 seconds for real-time data\n            const interval = setInterval(loadAnalyticsData, 30000);\n            return ()=>clearInterval(interval);\n        }\n    }, [\n        isLoaded,\n        isSignedIn,\n        timeRange\n    ]);\n    const loadAnalyticsData = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const [shiftsResponse, reportsResponse, agentsResponse, kpiResponse] = await Promise.all([\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/shifts\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/reports\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/agents\", {\n                    timeRange\n                }),\n                _services_ApiService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/analytics/kpis\", {\n                    timeRange\n                })\n            ]);\n            // Combine all analytics data\n            const combinedData = {\n                overview: kpiResponse.success ? kpiResponse.data : {},\n                shiftTrends: shiftsResponse.success ? shiftsResponse.data.trends : [],\n                reportTypes: reportsResponse.success ? reportsResponse.data.types : [],\n                agentPerformance: agentsResponse.success ? agentsResponse.data.performance : [],\n                siteActivity: shiftsResponse.success ? shiftsResponse.data.siteActivity : []\n            };\n            setAnalyticsData(combinedData);\n            // Set KPIs\n            if (kpiResponse.success) {\n                setKpis(kpiResponse.data);\n            }\n        } catch (error) {\n            console.error(\"Error loading analytics data:\", error);\n            setError(\"Failed to load analytics data\");\n            // Fallback to sample data for demo\n            setAnalyticsData({\n                overview: {\n                    totalShifts: 156,\n                    activeShifts: 12,\n                    totalAgents: 24,\n                    activeAgents: 18,\n                    totalSites: 8,\n                    activeSites: 6,\n                    totalReports: 89,\n                    pendingReports: 5,\n                    shiftsChange: 12.5,\n                    agentsChange: -2.1,\n                    reportsChange: 8.3\n                },\n                shiftTrends: [\n                    {\n                        date: \"2024-01-01\",\n                        scheduled: 20,\n                        completed: 18,\n                        cancelled: 2\n                    },\n                    {\n                        date: \"2024-01-02\",\n                        scheduled: 22,\n                        completed: 20,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-03\",\n                        scheduled: 18,\n                        completed: 17,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-04\",\n                        scheduled: 25,\n                        completed: 23,\n                        cancelled: 2\n                    },\n                    {\n                        date: \"2024-01-05\",\n                        scheduled: 21,\n                        completed: 19,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-06\",\n                        scheduled: 19,\n                        completed: 18,\n                        cancelled: 1\n                    },\n                    {\n                        date: \"2024-01-07\",\n                        scheduled: 23,\n                        completed: 21,\n                        cancelled: 2\n                    }\n                ],\n                reportTypes: [\n                    {\n                        name: \"Security\",\n                        value: 35,\n                        color: \"#DC3545\"\n                    },\n                    {\n                        name: \"Maintenance\",\n                        value: 25,\n                        color: \"#FFC107\"\n                    },\n                    {\n                        name: \"Incident\",\n                        value: 20,\n                        color: \"#DC004E\"\n                    },\n                    {\n                        name: \"General\",\n                        value: 15,\n                        color: \"#1976D2\"\n                    },\n                    {\n                        name: \"Safety\",\n                        value: 5,\n                        color: \"#2E7D32\"\n                    }\n                ],\n                agentPerformance: [\n                    {\n                        name: \"John Doe\",\n                        shifts: 15,\n                        onTime: 14,\n                        reports: 8,\n                        rating: 4.8\n                    },\n                    {\n                        name: \"Jane Smith\",\n                        shifts: 12,\n                        onTime: 12,\n                        reports: 6,\n                        rating: 4.9\n                    },\n                    {\n                        name: \"Mike Johnson\",\n                        shifts: 18,\n                        onTime: 16,\n                        reports: 12,\n                        rating: 4.6\n                    },\n                    {\n                        name: \"Sarah Wilson\",\n                        shifts: 14,\n                        onTime: 13,\n                        reports: 9,\n                        rating: 4.7\n                    },\n                    {\n                        name: \"David Brown\",\n                        shifts: 16,\n                        onTime: 15,\n                        reports: 7,\n                        rating: 4.5\n                    }\n                ],\n                siteActivity: [\n                    {\n                        site: \"Downtown Mall\",\n                        shifts: 45,\n                        incidents: 3,\n                        efficiency: 95\n                    },\n                    {\n                        site: \"Office Complex\",\n                        shifts: 38,\n                        incidents: 1,\n                        efficiency: 98\n                    },\n                    {\n                        site: \"Warehouse A\",\n                        shifts: 32,\n                        incidents: 2,\n                        efficiency: 92\n                    },\n                    {\n                        site: \"Retail Center\",\n                        shifts: 28,\n                        incidents: 4,\n                        efficiency: 88\n                    },\n                    {\n                        site: \"Industrial Park\",\n                        shifts: 25,\n                        incidents: 1,\n                        efficiency: 96\n                    }\n                ]\n            });\n            setKpis({\n                totalShifts: 156,\n                completedShifts: 142,\n                totalHours: 1248,\n                averageRating: 4.7,\n                activeAgents: 18,\n                totalReports: 89,\n                responseTime: 12.5,\n                efficiency: 94.2\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleExport = async ()=>{\n        try {\n            // In a real app, this would generate and download analytics report\n            console.log(\"Exporting analytics data...\");\n            alert(\"Export functionality would be implemented here\");\n        } catch (error) {\n            console.error(\"Error exporting analytics:\", error);\n        }\n    };\n    const KPICard = (param)=>{\n        let { title, value, unit = \"\", icon: Icon, color = \"primary\", trend } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            elevation: 2,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    sx: {\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"space-between\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"h4\",\n                                    component: \"div\",\n                                    color: \"\".concat(color, \".main\"),\n                                    children: [\n                                        value,\n                                        unit\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    variant: \"body2\",\n                                    color: \"textSecondary\",\n                                    children: title\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                trend !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    sx: {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        mt: 1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            color: trend >= 0 ? \"success\" : \"error\",\n                                            fontSize: \"small\"\n                                        }, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 225,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            variant: \"caption\",\n                                            color: trend >= 0 ? \"success.main\" : \"error.main\",\n                                            sx: {\n                                                ml: 0.5\n                                            },\n                                            children: [\n                                                Math.abs(trend),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 224,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            sx: {\n                                fontSize: 40,\n                                color: \"\".concat(color, \".main\"),\n                                opacity: 0.7\n                            }\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this)\n        }, void 0, false, {\n            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n            lineNumber: 213,\n            columnNumber: 5\n        }, this);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n        sx: {\n            display: \"flex\",\n            minHeight: \"100vh\",\n            backgroundColor: \"#f8fafc\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ModernSidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 247,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                component: \"main\",\n                sx: {\n                    flexGrow: 1,\n                    p: 3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            mb: 3,\n                            display: \"flex\",\n                            justifyContent: \"space-between\",\n                            alignItems: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                variant: \"h4\",\n                                fontWeight: \"bold\",\n                                children: \"Analytics Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 252,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                sx: {\n                                    display: \"flex\",\n                                    gap: 1,\n                                    alignItems: \"center\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        size: \"small\",\n                                        sx: {\n                                            minWidth: 120\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                children: \"Time Range\"\n                                            }, void 0, false, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 257,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                value: timeRange,\n                                                label: \"Time Range\",\n                                                onChange: (e)=>setTimeRange(e.target.value),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        value: \"1d\",\n                                                        children: \"Last 24 Hours\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        value: \"7d\",\n                                                        children: \"Last 7 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        value: \"30d\",\n                                                        children: \"Last 30 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        value: \"90d\",\n                                                        children: \"Last 90 Days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                                lineNumber: 258,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 256,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 272,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        onClick: handleExport,\n                                        children: \"Export\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 270,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        variant: \"outlined\",\n                                        startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                            lineNumber: 280,\n                                            columnNumber: 24\n                                        }, void 0),\n                                        onClick: loadAnalyticsData,\n                                        children: \"Refresh\"\n                                    }, void 0, false, {\n                                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                        lineNumber: 278,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 255,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 251,\n                        columnNumber: 7\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        severity: \"warning\",\n                        sx: {\n                            mb: 2\n                        },\n                        children: [\n                            error,\n                            \" - Showing sample data for demonstration\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 290,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        sx: {\n                            mb: 4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Shifts\",\n                                    value: kpis.totalShifts,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                    color: \"primary\",\n                                    trend: 12.5\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 298,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 297,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Completion Rate\",\n                                    value: (kpis.completedShifts / kpis.totalShifts * 100).toFixed(1),\n                                    unit: \"%\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    color: \"success\",\n                                    trend: 5.2\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 307,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 306,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Hours\",\n                                    value: kpis.totalHours.toLocaleString(),\n                                    unit: \"h\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                    color: \"info\",\n                                    trend: 8.7\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 317,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 316,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Average Rating\",\n                                    value: kpis.averageRating,\n                                    unit: \"/5\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    color: \"warning\",\n                                    trend: 2.1\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 327,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 326,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 296,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                        container: true,\n                        spacing: 3,\n                        sx: {\n                            mb: 4\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Active Agents\",\n                                    value: kpis.activeAgents,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n                                    color: \"secondary\",\n                                    trend: -1.5\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 341,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 340,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Total Reports\",\n                                    value: kpis.totalReports,\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n                                    color: \"primary\",\n                                    trend: 15.3\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 350,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 349,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Avg Response Time\",\n                                    value: kpis.responseTime,\n                                    unit: \"min\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n                                    color: \"warning\",\n                                    trend: -8.2\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 359,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 358,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                item: true,\n                                xs: 12,\n                                sm: 6,\n                                md: 3,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(KPICard, {\n                                    title: \"Efficiency\",\n                                    value: kpis.efficiency,\n                                    unit: \"%\",\n                                    icon: _barrel_optimize_names_Assessment_Assignment_CheckCircle_Download_Error_LocationOn_People_Refresh_Schedule_Security_Speed_Star_Timeline_TrendingDown_TrendingUp_Warning_mui_icons_material__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n                                    color: \"success\",\n                                    trend: 3.4\n                                }, void 0, false, {\n                                    fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                    lineNumber: 369,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 368,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 339,\n                        columnNumber: 7\n                    }, this),\n                    loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        sx: {\n                            display: \"flex\",\n                            justifyContent: \"center\",\n                            py: 8\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                            size: 60\n                        }, void 0, false, {\n                            fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 382,\n                        columnNumber: 9\n                    }, this) : analyticsData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AnalyticsCharts, {\n                        data: analyticsData,\n                        loading: loading,\n                        timeRange: timeRange,\n                        onTimeRangeChange: setTimeRange\n                    }, void 0, false, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 386,\n                        columnNumber: 9\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                        sx: {\n                            p: 4,\n                            textAlign: \"center\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                variant: \"h6\",\n                                color: \"textSecondary\",\n                                children: \"No analytics data available\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 394,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Avatar_Box_Button_Card_CardContent_Chip_CircularProgress_Divider_FormControl_Grid_IconButton_InputLabel_LinearProgress_MenuItem_Paper_Select_Tooltip_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                variant: \"body2\",\n                                color: \"textSecondary\",\n                                sx: {\n                                    mt: 1\n                                },\n                                children: \"Data will appear here once shifts and reports are created\"\n                            }, void 0, false, {\n                                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                        lineNumber: 393,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/workspaces/finalagent-main-final/packages/web-admin/src/app/analytics/page.js\",\n        lineNumber: 246,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"Dx3G7ApIedfHOI+E+zQTXcDOrqw=\", false, function() {\n    return [\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _clerk_nextjs__WEBPACK_IMPORTED_MODULE_5__.useUser\n    ];\n});\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/analytics/page.js\n"));

/***/ })

});