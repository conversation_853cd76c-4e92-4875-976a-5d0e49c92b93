// BahinLink Web Admin Settings Page
// ⚠️ CRITICAL: Real settings management with production data ONLY

'use client';

import { useAuth, useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { redirect } from 'next/navigation';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  Button,
  TextField,
  Divider,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Settings,
  Notifications,
  Security,
  Language,
  Palette,
  Save,
  Refresh,
  Edit,
  Delete,
  Add,
  Warning,
  CheckCircle
} from '@mui/icons-material';

import ModernSidebar from '../../components/ModernSidebar';
import ApiService from '../../services/ApiService';

export default function SettingsPage() {
  const { isLoaded, isSignedIn } = useAuth();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [settings, setSettings] = useState({
    notifications: {
      emailAlerts: true,
      smsAlerts: false,
      pushNotifications: true,
      emergencyAlerts: true
    },
    display: {
      theme: 'dark',
      language: 'fr',
      timezone: 'Africa/Dakar',
      dateFormat: 'DD/MM/YYYY'
    },
    security: {
      twoFactorAuth: false,
      sessionTimeout: 30,
      passwordExpiry: 90
    },
    system: {
      autoRefresh: true,
      refreshInterval: 30,
      mapProvider: 'google',
      defaultView: 'dashboard'
    }
  });
  const [saveStatus, setSaveStatus] = useState(null);

  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      redirect('/sign-in');
    }
    
    if (isLoaded && isSignedIn) {
      loadSettings();
    }
  }, [isLoaded, isSignedIn]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      // In a real app, load user settings from API
      // const userSettings = await ApiService.getUserSettings();
      // setSettings(userSettings);
      setLoading(false);
    } catch (error) {
      console.error('Error loading settings:', error);
      setLoading(false);
    }
  };

  const handleSettingChange = (category, setting, value) => {
    setSettings(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value
      }
    }));
  };

  const handleSaveSettings = async () => {
    try {
      setSaveStatus('saving');
      // In a real app, save settings to API
      // await ApiService.updateUserSettings(settings);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSaveStatus('success');
      setTimeout(() => setSaveStatus(null), 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus(null), 3000);
    }
  };

  if (!isLoaded || !isSignedIn) {
    return null;
  }

  return (
    <Box sx={{ display: 'flex', minHeight: '100vh', backgroundColor: '#f8fafc' }}>
      <ModernSidebar />
      
      <Box component="main" sx={{ flexGrow: 1, p: 3 }}>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 700, color: '#1e293b', mb: 1 }}>
            Settings
          </Typography>
          <Typography variant="body1" sx={{ color: '#64748b' }}>
            Manage your account preferences and system configuration
          </Typography>
        </Box>

        {/* Save Status Alert */}
        {saveStatus && (
          <Alert 
            severity={saveStatus === 'success' ? 'success' : saveStatus === 'error' ? 'error' : 'info'}
            sx={{ mb: 3 }}
          >
            {saveStatus === 'saving' && 'Saving settings...'}
            {saveStatus === 'success' && 'Settings saved successfully!'}
            {saveStatus === 'error' && 'Error saving settings. Please try again.'}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Notifications Settings */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 'fit-content' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Notifications sx={{ mr: 2, color: '#6366f1' }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Notifications
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.notifications.emailAlerts}
                        onChange={(e) => handleSettingChange('notifications', 'emailAlerts', e.target.checked)}
                      />
                    }
                    label="Email Alerts"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.notifications.smsAlerts}
                        onChange={(e) => handleSettingChange('notifications', 'smsAlerts', e.target.checked)}
                      />
                    }
                    label="SMS Alerts"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.notifications.pushNotifications}
                        onChange={(e) => handleSettingChange('notifications', 'pushNotifications', e.target.checked)}
                      />
                    }
                    label="Push Notifications"
                  />
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.notifications.emergencyAlerts}
                        onChange={(e) => handleSettingChange('notifications', 'emergencyAlerts', e.target.checked)}
                      />
                    }
                    label="Emergency Alerts"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Display Settings */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 'fit-content' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Palette sx={{ mr: 2, color: '#6366f1' }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Display & Language
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  <FormControl fullWidth>
                    <InputLabel>Theme</InputLabel>
                    <Select
                      value={settings.display.theme}
                      label="Theme"
                      onChange={(e) => handleSettingChange('display', 'theme', e.target.value)}
                    >
                      <MenuItem value="light">Light</MenuItem>
                      <MenuItem value="dark">Dark</MenuItem>
                      <MenuItem value="auto">Auto</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth>
                    <InputLabel>Language</InputLabel>
                    <Select
                      value={settings.display.language}
                      label="Language"
                      onChange={(e) => handleSettingChange('display', 'language', e.target.value)}
                    >
                      <MenuItem value="fr">Français</MenuItem>
                      <MenuItem value="en">English</MenuItem>
                      <MenuItem value="wo">Wolof</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <FormControl fullWidth>
                    <InputLabel>Timezone</InputLabel>
                    <Select
                      value={settings.display.timezone}
                      label="Timezone"
                      onChange={(e) => handleSettingChange('display', 'timezone', e.target.value)}
                    >
                      <MenuItem value="Africa/Dakar">Africa/Dakar (GMT+0)</MenuItem>
                      <MenuItem value="UTC">UTC</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Security Settings */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 'fit-content' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Security sx={{ mr: 2, color: '#6366f1' }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Security
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.security.twoFactorAuth}
                        onChange={(e) => handleSettingChange('security', 'twoFactorAuth', e.target.checked)}
                      />
                    }
                    label="Two-Factor Authentication"
                  />
                  
                  <TextField
                    label="Session Timeout (minutes)"
                    type="number"
                    value={settings.security.sessionTimeout}
                    onChange={(e) => handleSettingChange('security', 'sessionTimeout', parseInt(e.target.value))}
                    fullWidth
                  />
                  
                  <TextField
                    label="Password Expiry (days)"
                    type="number"
                    value={settings.security.passwordExpiry}
                    onChange={(e) => handleSettingChange('security', 'passwordExpiry', parseInt(e.target.value))}
                    fullWidth
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* System Settings */}
          <Grid item xs={12} md={6}>
            <Card sx={{ height: 'fit-content' }}>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Settings sx={{ mr: 2, color: '#6366f1' }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    System
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={settings.system.autoRefresh}
                        onChange={(e) => handleSettingChange('system', 'autoRefresh', e.target.checked)}
                      />
                    }
                    label="Auto Refresh Data"
                  />
                  
                  <TextField
                    label="Refresh Interval (seconds)"
                    type="number"
                    value={settings.system.refreshInterval}
                    onChange={(e) => handleSettingChange('system', 'refreshInterval', parseInt(e.target.value))}
                    fullWidth
                    disabled={!settings.system.autoRefresh}
                  />
                  
                  <FormControl fullWidth>
                    <InputLabel>Default View</InputLabel>
                    <Select
                      value={settings.system.defaultView}
                      label="Default View"
                      onChange={(e) => handleSettingChange('system', 'defaultView', e.target.value)}
                    >
                      <MenuItem value="dashboard">Dashboard</MenuItem>
                      <MenuItem value="agents">Agents</MenuItem>
                      <MenuItem value="sites">Sites</MenuItem>
                      <MenuItem value="analytics">Analytics</MenuItem>
                    </Select>
                  </FormControl>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Save Button */}
        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            startIcon={<Save />}
            onClick={handleSaveSettings}
            disabled={saveStatus === 'saving'}
            sx={{
              backgroundColor: '#6366f1',
              '&:hover': { backgroundColor: '#5855eb' },
              px: 4,
              py: 1.5
            }}
          >
            {saveStatus === 'saving' ? 'Saving...' : 'Save Settings'}
          </Button>
        </Box>
      </Box>
    </Box>
  );
}
