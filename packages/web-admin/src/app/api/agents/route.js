import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

// Global Prisma instance for serverless optimization
const globalForPrisma = globalThis;

const prisma = globalForPrisma.prisma || new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
  log: process.env.NODE_ENV === 'development' ? ['error'] : ['error'],
});

// Prevent multiple instances in development
if (process.env.NODE_ENV !== 'production') {
  globalForPrisma.prisma = prisma;
}

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    let agentsData;
    
    try {
      // Build where clause for filtering
      const where = {
        role: 'AGENT',
        ...(status && { isActive: status === 'ACTIVE' }),
        ...(search && {
          OR: [
            { firstName: { contains: search, mode: 'insensitive' } },
            { lastName: { contains: search, mode: 'insensitive' } },
            { email: { contains: search, mode: 'insensitive' } }
          ]
        })
      };

      // Fetch agents with related data
      const [agents, totalCount] = await Promise.all([
        prisma.user.findMany({
          where,
          include: {
            agent: {
              include: {
                shifts: {
                  where: {
                    createdAt: {
                      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
                    }
                  },
                  orderBy: {
                    createdAt: 'desc'
                  },
                  take: 5
                },
                reports: {
                  where: {
                    createdAt: {
                      gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
                    }
                  },
                  orderBy: {
                    createdAt: 'desc'
                  },
                  take: 5
                }
              }
            }
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: {
            createdAt: 'desc'
          }
        }),
        
        prisma.user.count({ where })
      ]);

      // Calculate performance metrics for each agent
      const agentsWithMetrics = agents.map(user => {
        const agent = user.agent;
        if (!agent) return null;

        const shifts = agent.shifts || [];
        const reports = agent.reports || [];
        
        // Calculate completion rate
        const completedShifts = shifts.filter(s => s.status === 'COMPLETED').length;
        const completionRate = shifts.length > 0 ? (completedShifts / shifts.length) * 100 : 0;
        
        // Calculate average rating from reports
        const ratingsReports = reports.filter(r => r.rating && r.rating > 0);
        const averageRating = ratingsReports.length > 0 
          ? ratingsReports.reduce((sum, r) => sum + r.rating, 0) / ratingsReports.length
          : 0;

        // Calculate total hours worked
        const totalHours = shifts.reduce((total, shift) => {
          if (shift.endTime && shift.startTime) {
            const hours = (new Date(shift.endTime) - new Date(shift.startTime)) / (1000 * 60 * 60);
            return total + hours;
          }
          return total;
        }, 0);

        return {
          id: user.id,
          employeeId: agent.employeeId,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          isActive: user.isActive,
          isAvailable: agent.isAvailable,
          certifications: agent.certifications || [],
          skills: agent.skills || [],
          emergencyContactName: agent.emergencyContactName,
          emergencyContactPhone: agent.emergencyContactPhone,
          currentLocation: agent.currentLocation,
          lastLocationUpdate: agent.lastLocationUpdate,
          performance: {
            totalShifts: shifts.length,
            completedShifts,
            completionRate: Math.round(completionRate * 10) / 10,
            totalReports: reports.length,
            averageRating: Math.round(averageRating * 10) / 10,
            totalHours: Math.round(totalHours)
          },
          recentShifts: shifts.slice(0, 3),
          recentReports: reports.slice(0, 3),
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        };
      }).filter(Boolean);

      agentsData = {
        agents: agentsWithMetrics,
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit),
        lastUpdated: new Date().toISOString()
      };

    } catch (dbError) {
      console.error('Database query error:', dbError);
      
      // Return sample data for demonstration when database is not available
      agentsData = {
        agents: [
          {
            id: '1',
            employeeId: 'BH001',
            firstName: 'Amadou',
            lastName: 'Ba',
            email: '<EMAIL>',
            phone: '+221 77 123 4567',
            isActive: true,
            isAvailable: true,
            certifications: ['Security Guard License', 'First Aid'],
            skills: ['Surveillance', 'Emergency Response'],
            emergencyContactName: 'Fatou Ba',
            emergencyContactPhone: '+221 77 987 6543',
            currentLocation: {
              latitude: 14.6937,
              longitude: -17.4441,
              address: 'Dakar, Senegal'
            },
            lastLocationUpdate: new Date().toISOString(),
            performance: {
              totalShifts: 45,
              completedShifts: 42,
              completionRate: 93.3,
              totalReports: 12,
              averageRating: 4.8,
              totalHours: 360
            },
            recentShifts: [],
            recentReports: [],
            createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '2',
            employeeId: 'BH002',
            firstName: 'Fatou',
            lastName: 'Sow',
            email: '<EMAIL>',
            phone: '+221 77 234 5678',
            isActive: true,
            isAvailable: false,
            certifications: ['Security Guard License'],
            skills: ['Patrol', 'Access Control'],
            emergencyContactName: 'Moussa Sow',
            emergencyContactPhone: '+221 77 876 5432',
            currentLocation: {
              latitude: 14.7167,
              longitude: -17.4677,
              address: 'Plateau, Dakar'
            },
            lastLocationUpdate: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            performance: {
              totalShifts: 38,
              completedShifts: 35,
              completionRate: 92.1,
              totalReports: 8,
              averageRating: 4.6,
              totalHours: 304
            },
            recentShifts: [],
            recentReports: [],
            createdAt: new Date(Date.now() - 75 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          },
          {
            id: '3',
            employeeId: 'BH003',
            firstName: 'Moussa',
            lastName: 'Diop',
            email: '<EMAIL>',
            phone: '+221 77 345 6789',
            isActive: true,
            isAvailable: true,
            certifications: ['Security Guard License', 'Fire Safety'],
            skills: ['Investigation', 'Report Writing'],
            emergencyContactName: 'Aissatou Diop',
            emergencyContactPhone: '+221 77 765 4321',
            currentLocation: {
              latitude: 14.6928,
              longitude: -17.4467,
              address: 'Medina, Dakar'
            },
            lastLocationUpdate: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            performance: {
              totalShifts: 52,
              completedShifts: 48,
              completionRate: 92.3,
              totalReports: 15,
              averageRating: 4.7,
              totalHours: 416
            },
            recentShifts: [],
            recentReports: [],
            createdAt: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          }
        ],
        total: 3,
        page: 1,
        limit: 50,
        totalPages: 1,
        lastUpdated: new Date().toISOString(),
        note: 'Sample data - Database connection pending'
      };
    }

    return NextResponse.json(agentsData);
    
  } catch (error) {
    console.error('Agents API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch agents data', details: error.message },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
